<div class="timeline-container" data-app="notifications">
	<div class="timeline-header" data-el="tl-header">
		<div class="timeline-header__botline">
			<div class="lp">
				<div class="nav-link-holder">
					<a href="<?php echo cl_link("notifications"); ?>" data-spa="true">
						<?php echo cl_translate("Notifications"); ?>
					</a>
				</div>
			</div>
			<div class="cp">
				<a href="<?php echo cl_link('/'); ?>">
					<img src="{%config site_logo%}" alt="Logo">
				</a>
			</div>
			<div class="rp">
				<div class="nav-link-holder">
					<button class="go-options dropleft">
						<div class="dropdown-toggle icon go-options__icon" data-toggle="dropdown">
							<?php echo cl_ficon('more_horiz'); ?>
						</div>
						<div class="dropdown-menu dropdown-icons">
							<a class="dropdown-item" href="<?php echo cl_link("settings/notifications"); ?>" data-spa="true">
								<span class="flex-item dropdown-item-icon">
									<?php echo cl_ficon('notifications'); ?>
								</span>
								<span class="flex-item">
									<?php echo cl_translate("Notification settings"); ?>
								</span>
								<span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
									<?php echo cl_ficon('open'); ?>
								</span>
							</a>
							<?php if (not_empty($cl["notifs"])): ?>
								<div class="dropdown-divider"></div>
								<a onclick="SMColibri.PS.notifs.delete_all_notifs();" class="dropdown-item" href="javascript:void(0);">
									<span class="flex-item dropdown-item-icon">
										<?php echo cl_ficon("delete"); ?>
									</span>
									<span class="flex-item">
										<span><?php echo cl_translate("Delete all notification"); ?></span>
									</span>
								</a>
							<?php endif; ?>
							<div class="dropdown-divider"></div>
							<a class="dropdown-item" href="javascript:void(0);" onclick="SMColibri.go_back();">
								<span class="flex-item dropdown-item-icon">
									<?php echo cl_ficon('arrow_back'); ?>
								</span>
								<span class="flex-item">
									<?php echo cl_translate("Go back"); ?>
								</span>
								<span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
									<?php echo cl_ficon('open'); ?>
								</span>
							</a>
						</div>
					</button>
				</div>
			</div>
		</div>
	</div>
	<div class="timeline-navbar">
		<div class="timeline-navbar__item">
			<a href="<?php echo cl_link("notifications"); ?>" data-spa="true">
				<button class="timeline-navbar__item-btn <?php if($cl['page_tab'] == 'notifs') {echo('active');} ?>">
					<span class="btn-flex-inner">
						<?php echo cl_translate("Notifications"); ?>
					</span>
				</button>
			</a>
		</div>
		<div class="timeline-navbar__item">
			<a href="<?php echo cl_link("mentions"); ?>" data-spa="true">
				<button class="timeline-navbar__item-btn <?php if($cl['page_tab'] == 'mentions') {echo('active');} ?>">
					<span class="btn-flex-inner">
						<?php echo cl_translate("Mentions"); ?>
					</span>
				</button>
			</a>
		</div>
	</div>
	<div class="d-block" id="vue-notifications-app">
		<div class="notifications">
			<?php if (not_empty($cl["notifs"])): ?>
				<div data-an="notifs-list" class="notifications-list">
					<?php foreach ($cl["notifs"] as $cl['li']): ?>
						<?php echo cl_template('notifications/includes/list_item'); ?>
					<?php endforeach; ?>
				</div>
			<?php else: ?>
				<?php echo cl_template('notifications/includes/no_notifs'); ?>
			<?php endif; ?>
		</div>

		<?php if (count($cl["notifs"]) >= 45): ?>
			<div class="timeline-data-loader">
				<button v-if="loading" class="timeline-data-loader__btn" disabled="true">
					<?php echo cl_translate("Please wait"); ?>
				</button>
				<button v-else-if="loadmore" class="timeline-data-loader__btn" v-on:click="load_notifications">
					<?php echo cl_translate("Show more"); ?>
				</button>
				<button v-else class="timeline-data-loader__btn" disabled="true">
					<?php echo cl_translate("That is all for now!"); ?>
				</button>
			</div>
		<?php endif; ?>
	</div>
	<?php echo cl_template('notifications/scripts/app_master_script'); ?>
</div>
