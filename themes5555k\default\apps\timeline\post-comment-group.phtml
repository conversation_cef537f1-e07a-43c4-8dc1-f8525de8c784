<?php if (in_array($cl['li']['owner'], array("0", "2"))): ?>
<div class="timeline-posts-container">
			<div class="timeline-posts-ls">
				<?php echo cl_template('profile/includes/suspended_profile'); ?>
			</div>
		</div>
	<?php else: ?>
<?php if (not_empty($cl['li'])): ?>
	<?php if ($cl['li']['advertising']): ?>
		<?php echo cl_template('timeline/ad'); ?>
	<?php else: ?>
		<div class="post-list-item"  style=" padding-bottom: 8px; !important;" data-list-item="<?php echo($cl['li']['id']); ?>" data-post-offset="<?php echo($cl['li']['offset_id']); ?>" <?php echo fetch_or_get($cl['li']['attrs'],''); ?>>
			<?php if (not_empty($cl['li']['me_blocked'])): ?>
				<div class="post-placeholde">
					<div class="d-flex flex-wn align-items-center">
						<div class="flex-item">
							<div class="icon">
								<?php echo cl_ficon("emoji_angry"); ?>
							</div>
						</div>
						<div class="felx-item">
							<p>
								<?php echo cl_translate("This user ({@uname@}) has blocked you from accessing their posts", array(
									"uname" => cl_html_el("a", $cl['li']['owner']['username'], array(
										"href" => $cl['li']['owner']['url'],
										"data-spa" => "true"
									))
								)); ?>
							</p>
						</div>
					</div>
				</div>
			<?php elseif (empty($cl['li']['can_see'])): ?>
				<div class="post-placeholder">
					<div class="d-flex flex-wn align-items-center">
						<div class="flex-item">
							<div class="icon">
								<?php echo cl_ficon("eye_hide"); ?>
							</div>
						</div>
						<div class="felx-item">
							<p>
								<?php echo cl_translate("Only followers of this user ({@uname@}) can see their posts", array(
									"uname" => cl_html_el("a", $cl['li']['owner']['username'], array(
										"href" => $cl['li']['owner']['url'],
										"data-spa" => "true"
									))
								)); ?>
							</p>
						</div>
					</div>
				</div>
			<?php else: ?>
				<?php if (not_empty($cl["pn"]) && $cl["pn"] == "profile"): ?>
					<?php if ($cl['li']['profile_pinned'] == "Y"): ?>
						<div class="post-list-item__alert">
							
							<div class="alert-badge">
							<div class="icon" style="background: var(--cl-secondary-border-color); border-radius: 20px; width: 20px;">
									<?php echo cl_ficon('pin'); ?>
								</div>
								<div class="text" style="background: var(--cl-secondary-border-color); border-radius: 10px; padding: 5px; font-size: 14px;">
									<?php echo cl_translate('{@uname@} pinned this post', array('uname' => $cl['li']['owner']['name'])); ?>
								</div>
							
							</div>
						</div>
					<?php endif; ?>
				<?php endif; ?>

				<?php if (not_empty($cl["pn"]) &&  in_array($cl["pn"], array("explore", "feed", "home"))): ?>
					<?php if ($cl['li']['admin_pinned'] == "Y"): ?>
						<div class="post-list-item__alert post-list-item__alert_admin" >
							<div class="alert-badge">
							<div class="icon" style="  width: 28px;
  height: 28px; background: var(--cl-secondary-border-color); border-radius: 10px; ">
									<?php echo cl_ficon('alert'); ?>
								</div>
								<div class="text" style="background: var(--cl-secondary-border-color); border-radius: 10px; padding: 5px; font-size: 14px;">
									<?php echo cl_translate('Administrator pinned this post'); ?>
								</div>
						
							</div>
						</div>
					<?php endif; ?>
				<?php endif; ?>

				<?php if (not_empty($cl['li']['is_repost'])): ?>
					<div class="post-list-item__header" >
						<div class="publication-repost">
							<span class="publication-repost__icon">
								<?php echo cl_ficon('reply'); ?>
							</span>
							<?php if (not_empty($cl['li']['is_reposter'])): ?>
								<a href="<?php echo $me['url']; ?>" data-spa="true">
									<?php echo cl_translate('You reposted'); ?>
								</a>
							<?php else: ?>
							
								<a href="<?php echo($cl['li']['reposter']['url']); ?>" data-spa="true">
									<?php echo cl_translate('{@uname@} reposted', array('uname' => $cl['li']['reposter']['name'])) ?>
								</a>
							<?php endif; ?>

							
						</div>
					</div>
				<?php endif; ?>
				<span style=" background:  #eee; width: 100%; height:90px; z-index: 1;"><span>
				<div class="post-list-item__content">
					<div class="post-data">
						<div class="post-data__avatar" style="z-index: 1 !important;">
							<a class="block-link" href="<?php echo($cl['li']['url']); ?>" data-spa="true">
								<div style="margin-top: 3px; margin-left: 5px;" class="avatar-holder <?php if(cl_is_online($cl['li']['owner']['is_online'])) {echo "avatar-holder-online2";} ?>">
									<img style="width: 35px; height: 35px; "  class="lozad" data-src="<?php echo($cl['li']['owner']['avatar']); ?>">
								</div> 
							</a>
						</div>
						<div class="post-data__content">
							<?php if (not_empty($cl['li']['is_blocked'])): ?>
								<div class="post-data__content-hidden" data-softhidden-post="<?php echo($cl['li']['id']); ?>">
									<div class="soft-hidden-post">
										<div class="d-flex align-items-center flex-wn">
											<div class="flex-item flex-grow-1">
												<p><?php echo cl_translate('This is a message from the user you blocked'); ?></p>
											</div>
											<div class="flex-item">
												<button class="btn btn-custom main-outline sm" onclick="SMColibri.show_post(<?php echo($cl['li']['id']); ?>, 'blocked');">
													<?php echo cl_translate('View'); ?>
												</button>
											</div>	
										</div>
									</div>
								</div>
							<?php elseif(not_empty($cl['li']['is_reported'])): ?>
								<div class="post-data__content-hidden" data-softhidden-post="<?php echo($cl['li']['id']); ?>">
									<div class="soft-hidden-post">
										<div class="d-flex align-items-center flex-wn">
											<div class="flex-item flex-grow-1">
												<p>
													<?php echo cl_translate('This post is currently under review'); ?>	
												</p>
											</div>
											
										</div>
									</div>
								</div>
							<?php endif; ?>

							<div class="post-data__content-inner" style="margin-left: -20px; margin-top: -3px; ">
						
								<div class="post-data-layout">
								<div style="padding:10px;  display: inline-block; ">
									<div class="post-data-layout__publisher" style="margin-top: -5px !important;">
									
										<div class="post-username">
											<a href="<?php echo($cl['li']['owner']['url']); ?>" data-spa="true" data-uinfo-lbox="<?php echo($cl['li']['owner']['id']); ?>" data-toggle="popover" data-placement="bottom">
												<span class="user-name-holder">
													<span class="user-name-holder__name" style="text-transform: capitalize !important;">
														<?php echo($cl['li']['owner']['name']); ?>
													</span>
													<?php if ($cl['li']['owner']['verified'] == '1'): ?>
														<span class="user-name-holder__badge" style=" margin-left: 2px; margin-top: 1px !important; width: 13px !important; height: 13px !important;">
															<?php echo cl_icon("verified_user_badge"); ?>
														</span>
														
													<?php endif; ?>
													<?php if ($cl['li']['owner']['is_premium'] == '1'): ?>

<span style="margin-left: 3px; margin-top: -3px;">
		<img src="{%config theme_url%}/statics/img/vip.png" width="16" height="16">
</span>
<?php endif; ?>
												
													  <span class="text-limit-comment" style="color: var(--cl-uname-color) !important; font-weight: 300 !important; font-size: 15px; margin-left: 4px;"> <span style="font-size: 12px;;">@</span><?php echo ($cl['li']['owner']['username']); ?></span>
												</span>
												<?php if (not_empty($cl['li']['edited'])): ?>
											&nbsp;	<span style="padding-left: 1px; margin-left: -20px !important; margin-top: -3px !important; " title="<?php echo cl_translate("Edited"); ?>: <?php echo cl_date("h:i A - M d, Y", $cl['li']['edited']); ?>"> &nbsp;
											
<svg fill="var(--cl-secondary-text-color)" width="18px" height="18px" viewBox="0 0 56 56" xmlns="http://www.w3.org/2000/svg"><path d="M 1.1883 43.4869 L 13.6935 43.4869 C 13.4483 44.4300 13.3162 45.3542 13.3162 46.2030 C 13.3162 49.2397 15.0138 51.4088 18.5786 51.4088 C 22.9168 51.4088 26.4250 48.3532 28.6884 43.4869 L 54.8118 43.4869 C 55.4719 43.4869 56 42.9777 56 42.3175 C 56 41.6574 55.4719 41.1670 54.8118 41.1670 L 29.6315 41.1670 C 30.6877 38.0737 31.3290 34.4523 31.4611 30.5290 C 32.6494 30.2650 33.8941 30.1329 35.1769 30.1329 C 35.8371 30.1329 36.2141 30.4159 36.2141 30.9063 C 36.2141 32.5472 35.3276 33.8298 35.3276 35.4708 C 35.3276 36.9043 36.3841 37.7719 37.7608 37.7719 C 41.5896 37.7719 46.2108 31.2269 47.7385 31.2269 C 49.1342 31.2269 47.3990 37.1117 52.1522 37.1117 C 52.9256 37.1117 53.9253 36.9043 54.6986 36.4139 C 55.1513 36.0932 55.4719 35.6405 55.4719 35.0558 C 55.4719 34.3391 55.0192 33.7544 54.2648 33.7544 C 53.6046 33.7544 53.0577 34.3013 52.4164 34.3013 C 50.3792 34.3013 52.3218 28.0393 48.5687 28.0393 C 45.2868 28.0393 40.1561 34.4145 38.8548 34.4145 C 38.6852 34.4145 38.5531 34.3202 38.5531 34.0939 C 38.5531 33.4149 39.4017 31.8871 39.4017 30.3593 C 39.4017 28.4732 37.8361 27.2660 35.3844 27.2660 C 34.0451 27.2660 32.7248 27.3980 31.4611 27.6432 C 31.0461 17.7032 26.3307 9.8756 19.1256 9.8756 C 14.2782 9.8756 10.5436 14.0063 10.5436 19.2687 C 10.5436 25.4176 14.5423 30.6799 19.5217 34.1693 C 17.3149 36.3384 15.5985 38.8093 14.5423 41.1670 L 1.1883 41.1670 C .5281 41.1670 0 41.6574 0 42.3175 C 0 42.9777 .5281 43.4869 1.1883 43.4869 Z M 13.4106 19.2687 C 13.4106 15.5907 15.9003 12.7426 19.1256 12.7426 C 24.7841 12.7426 28.4432 19.7780 28.6130 28.4166 C 26.0101 29.3219 23.6335 30.6988 21.5776 32.3397 C 17.6544 29.6237 13.4106 25.1347 13.4106 19.2687 Z M .6413 37.1495 C 1.1317 37.6399 1.8673 37.6210 2.3765 37.1495 L 4.7342 34.7918 L 7.0919 37.1495 C 7.5823 37.6399 8.3368 37.6399 8.8272 37.1495 C 9.3176 36.6591 9.3176 35.9046 8.8272 35.4142 L 6.4695 33.0754 L 8.8272 30.7177 C 9.3176 30.2273 9.3176 29.4917 8.8272 29.0012 C 8.3368 28.4920 7.5823 28.5109 7.0919 29.0012 L 4.7342 31.3401 L 2.3765 29.0012 C 1.8673 28.4920 1.1317 28.4920 .6413 29.0012 C .1509 29.4917 .1509 30.2461 .6413 30.7177 L 2.9990 33.0754 L .6413 35.4142 C .1509 35.9235 .1509 36.6591 .6413 37.1495 Z M 23.6335 36.5459 C 23.8787 36.6591 24.1051 36.7156 24.3314 36.7156 C 25.1047 36.7156 25.6517 36.1121 25.6517 35.4708 C 25.6517 34.9992 25.4254 34.5466 24.8784 34.2825 C 24.6143 34.1505 24.3503 34.0184 24.0674 33.8675 C 25.4254 32.9056 26.9343 32.0568 28.5564 31.4155 C 28.3300 35.0558 27.6322 38.4132 26.5571 41.1670 L 17.5790 41.1670 C 18.5597 39.3185 20.0309 37.3758 21.8982 35.6594 C 22.4641 35.9800 23.0488 36.2630 23.6335 36.5459 Z M 16.2586 45.6560 C 16.2586 44.9959 16.3718 44.2603 16.6170 43.4869 L 25.4820 43.4869 C 23.7844 46.6180 21.5022 48.5418 18.8993 48.5418 C 17.0886 48.5418 16.2586 47.3536 16.2586 45.6560 Z"/></svg>
												</span>
											<?php endif; ?> 
										<span style="padding-left: 2px; margin-top: -1px; font-size: 12px; color: var(--cl-primary-text-color);"> <?php echo($cl['li']['time']); ?></span>
													</span>
											</a>
										</div>
										<div class="ctrls-item dropleft" style="margin-top: -4px; color:  var(--cl-secondary-text-color) !important;">
												
										
										</span> &nbsp;
										</div>
										
									
									</div>
									<?php if ($cl['li']['target'] == 'pub_reply' && not_empty($cl['li']['reply_to'])): ?>
										<div class="post-data-layout__target">
											<?php if (not_empty($cl['li']['reply_to']['is_owner'])): ?>
												<div class="post-reply">
												
												</div>
											<?php else: ?>
												<div class="post-reply">
													
												</div>
											<?php endif; ?>
										</div>
									<?php endif; ?>

									<div class="post-data-layout__content" style="margin-top: -9px; margin-left: 1px;">
										<?php if ($cl['li']['is_donation_post'] == "Y"): ?>
											<?php if (not_empty($cl['li']["title"])): ?>
												<div class="publication-title">
													<?php echo($cl['li']['title']); ?>
												</div>
											<?php endif; ?>
										<?php endif; ?>

										<?php if (not_empty($cl['li']['text'])): ?>
											<?php 
												$cl['li']['text'] = cl_rn2br($cl['li']['text']);
												$cl['li']['text'] = cl_strip_brs($cl['li']['text']);
											?>
											<div class="publication-text" data-post-text="<?php echo($cl['li']['id']); ?>">
												<?php echo($cl['li']['text']); ?>
											</div>
										<?php endif; ?>

										<?php if ($cl['li']["content_view"] == true): ?>
											<?php if ($cl['li']['type'] == 'image' && not_empty($cl['li']['media'])): ?>
												<?php if (count($cl['li']['media']) == 1): ?>
													<div class="lozad-media" style="margin-bottom: 10px !important; margin-top: -5px !important;">
														<div class="publication-image issafe-<?php echo strtolower(fetch_or_get($cl['li']['media'][0]['is_safe'], "Y")); ?>">
															<a href="<?php echo cl_get_media(fetch_or_get($cl['li']['media'][0]['src'],'')); ?>" class="fbox-media">
																<img class="lozad" data-src="<?php echo cl_get_media(fetch_or_get($cl['li']['media'][0]['src'],'')); ?>" alt="Picture">
															</a>
														</div>
													</div>
												<?php else: ?>
													<div class="publication-images-collage" style="width: 100px;   margin-top: -30px !imp0ortant;">
														<?php if (count($cl['li']['media']) == 2): ?>
															<?php echo cl_template("timeline/includes/img_grid/c2_images"); ?>
														<?php elseif (count($cl['li']['media']) == 3): ?>
															<?php echo cl_template("timeline/includes/img_grid/c3_images"); ?>
														<?php elseif (count($cl['li']['media']) == 4): ?>
															<?php echo cl_template("timeline/includes/img_grid/c4_images"); ?>
														<?php elseif (count($cl['li']['media']) == 5): ?>
															<?php echo cl_template("timeline/includes/img_grid/c5_images"); ?>
														<?php elseif (count($cl['li']['media']) == 6): ?>
															<?php echo cl_template("timeline/includes/img_grid/c6_images"); ?>
														<?php elseif (count($cl['li']['media']) == 7): ?>
															<?php echo cl_template("timeline/includes/img_grid/c7_images"); ?>
														<?php elseif (count($cl['li']['media']) == 8): ?>
															<?php echo cl_template("timeline/includes/img_grid/c8_images"); ?>
														<?php elseif (count($cl['li']['media']) == 9): ?>
															<?php echo cl_template("timeline/includes/img_grid/c9_images"); ?>
														<?php elseif (count($cl['li']['media']) == 10): ?>
															<?php echo cl_template("timeline/includes/img_grid/c10_images"); ?>
														<?php endif; ?>
													</div>
												<?php endif; ?>
											<?php elseif($cl['li']['type'] == 'video' && not_empty($cl['li']['media'])): ?>
												<div class="lozad-media" style="max-width: 70% !important; max-height: auto;">
													<div class="publication-video" style="margin-bottom: 10px !important;">
														<div class="cl-plyr-video">
															<video data-video-ratio="<?php echo(fetch_or_get($cl['li']['media'][0]['x']["ratio"],'16:9')); ?>" class="plyr" preload="metadata" playsinline controls data-poster="<?php echo cl_get_media(fetch_or_get($cl['li']['media'][0]['x']['poster_thumb'],'')); ?>">
																<source src="<?php echo cl_get_media(fetch_or_get($cl['li']['media'][0]['src'],'')); ?>" type="video/mp4">
			 													<source src="<?php echo cl_get_media(fetch_or_get($cl['li']['media'][0]['src'],'')); ?>" type="video/webm">
			 													<source src="<?php echo cl_get_media(fetch_or_get($cl['li']['media'][0]['src'],'')); ?>" type="video/mov">
			 													<source src="<?php echo cl_get_media(fetch_or_get($cl['li']['media'][0]['src'],'')); ?>" type="video/3gp">
			 													<source src="<?php echo cl_get_media(fetch_or_get($cl['li']['media'][0]['src'],'')); ?>" type="video/ogg">
															</video>
														</div>
												    </div>
												</div>
											<?php elseif($cl['li']['type'] == 'audio' && not_empty($cl['li']['media'])): ?>
												<div class="publication-audio">
													<div class="">
<div style="margin-left: -15px">
	<a style="background: #eee !important;">
<wave-audio-path-player src="<?php echo cl_get_media(fetch_or_get($cl['li']['media'][0]['src'],'')); ?>" type="audio/mp3"  wave-slider="#000" color="#000" wave-progress-color="var(--cl-primary-color)" wave-color="#bfbfbf" wave-width="225" wave-height="30"></wave-audio-path-player></div>
											</a>
													</div>

												</div>
											<?php elseif($cl['li']['type'] == 'document' && not_empty($cl['li']['media'])): ?>
												<div class="publication-document">
													<a href="<?php echo cl_get_media($cl['li']['media'][0]['src']); ?>" target="_blank" class="document-file">
														<div class="document-file__icon">
															<?php echo cl_ficon("document"); ?>
														</div>
														<div class="document-file__body">
															<?php echo($cl['li']['media'][0]['x']["filename"]); ?>
														</div>
														<div class="document-file__type">
															<?php echo($cl['li']['media'][0]['x']["file_type"]); ?>-<?php echo cl_translate("FILE"); ?>
														</div>
														<div class="document-file__icon">
															<?php echo cl_ficon("open"); ?>
														</div>
													</a>
												</div>
											<?php elseif($cl['li']['type'] == 'gif' && not_empty($cl['li']['media'])): ?>
												<div class="lozad-media">
													<div class="publication-image" style="width: 250px; margin-bottom: 10px;">
														<a href="<?php echo fetch_or_get($cl['li']['media'][0]['src'],''); ?>" class="fbox-media">
															<img class="lozad" data-src="<?php echo fetch_or_get($cl['li']['media'][0]['src'],''); ?>" alt="GIF-Image">
														</a>
													</div>
												</div>
											<?php elseif($cl['li']['type'] == 'poll' && not_empty($cl['li']['poll'])): ?>
												<div class="publication-poll" data-post-poll="<?php echo($cl['li']['id']); ?>" data-status="<?php echo($cl['li']['poll']['has_voted']); ?>" data-stopped="<?php echo($cl['li']['poll_status']); ?>">
													<div class="publication-poll__inner">
														<?php foreach ($cl['li']['poll']['options'] as $i => $poll_data): ?>
															<div class="publication-poll__option" onclick="SMColibri.vote_poll(<?php echo($cl['li']['id']); ?>, <?php echo($i); ?>);" data-poll-option="<?php echo($i); ?>">
																<div class="bar-icon">
																	<?php echo cl_ficon("checkmark_circle"); ?>
																</div>
																<div class="bar-text">
																	<p>
																		<?php echo $poll_data["option"]; ?>
																	</p>
																</div>
																<div class="bar-num">
																	<b>
																		<?php if (not_empty($cl['li']['poll']['has_voted'])): ?>
																			<?php echo $poll_data["percentage"]; ?>%
																		<?php endif; ?>
																	</b>
																</div>

																<?php if (not_empty($cl['li']['poll']['has_voted'])): ?>
																	<span class="bar-slider" style="width: <?php echo $poll_data["percentage"]; ?>%;"></span>
																<?php else: ?>
																	<span class="bar-slider"></span>
																<?php endif; ?>
															</div>
														<?php endforeach; ?>

														<div class="publication-poll__total-votes" style="text-transform: capitalize;">
															<?php echo cl_translate("people voted"); ?>
															<span data-an="total-poll-voted" style="font-weight: bold;">
																<?php
																	$total_votes = 0;
																	$poll_data = json($cl['li']['poll_data']);

																	foreach ($poll_data as $pod) {
																		$total_votes = ($total_votes += $pod["votes"]);
																	}

																	echo  $total_votes;
																?>
															</span>

															

															<?php if ($cl['li']["poll_status"] == "stopped"): ?>
																- <?php echo cl_translate("Poll timed out"); ?>
															<?php endif; ?>
														</div>
													</div>
												</div>
											<?php elseif(not_empty($cl['li']['og_data'])): ?>
												<?php if (not_empty($cl['li']['og_data']['video_embed'])): ?>
													<div class="publication-og">
														<div class="publication-og__inner embeded-iframe">
															<div class="publication-og__image">
																<div class="lozad-media">
																	<a href="<?php echo($cl['li']['og_data']['video_embed']); ?>" class="fbox-media">
																		<img class="lozad" data-src="<?php echo($cl['li']['og_data']['image']); ?>" alt="Video">
																		<div class="video-play-button">
																			<span class="video-play-button__arrow">
																				<?php echo cl_ficon("play"); ?>
																			</span>
																		</div>
																	</a>
																</div>
															</div>
															<div class="publication-og__description">
																<h5>
																	<?php echo($cl['li']['og_data']['title']); ?>
																</h5>
																<?php if (not_empty($cl['li']['og_data']['description'])): ?>
																	<p>
																		<?php echo($cl['li']['og_data']['description']); ?>
																	</p>
																<?php else: ?>
																	<p>
																		<a target="_blank" href="<?php echo($cl['li']['og_data']['video_embed']); ?>">
																			<?php echo($cl['li']['og_data']['video_embed']); ?>
																		</a>
																	</p>
																<?php endif; ?>
															</div>
														</div>
													</div>
												<?php else: ?>
													<div class="publication-og">
														<div class="publication-og__inner link" data-href="<?php echo($cl['li']['og_data']['url']); ?>">
															<?php if (not_empty($cl['li']['og_data']['image'])): ?>
																<div class="publication-og__image">
																	<img src="<?php echo($cl['li']['og_data']['image']); ?>" alt="IMG">
																</div>
															<?php endif; ?>
															
															<div class="publication-og__description">
																<h5>
																	<?php echo($cl['li']['og_data']['title']); ?>
																</h5>
																<p>
																	<?php echo($cl['li']['og_data']['description']); ?>
																</p>
																<a href="<?php echo($cl['li']['og_data']['url']); ?>" target="_blank">
																	<?php echo($cl['li']['og_data']['url']); ?>
																</a>
															</div>
														</div>
													</div>
												<?php endif; ?>
											<?php endif; ?>
										<?php else: ?>
										
											
										<?php endif; ?>

										<?php if ($cl['li']["content_view"] == true): ?>
											<?php if ($cl['li']['is_donation_post'] == "Y"): ?>
												<div class="publication-funding">
													<div class="publication-funding__header">
														<?php echo cl_translate("{@raised@} raised of {@goal@} goal", array(
															"raised" => cl_html_el("span", cl_money($cl["li"]["donation_raised"]), array("class" => "raised", "data-an" => "donation-raised")),
															"goal" => cl_money($cl["li"]["donation_amount"])
														)); ?>
													</div>
													<div class="publication-funding__body">
														<div class="funding-progress">
															<div class="funding-progress__bar" data-an="donation-raised-percent" style="width: <?php echo $cl["li"]["donation_raised_percent"] . "%;"; ?>;"></div>
														</div>
														<div class="funding-total">
															<span>
																<span data-an="donations-total"><?php echo($cl["li"]["donations_total"]); ?></span> <?php echo cl_translate("donations"); ?>
															</span>
															<span>
																<span data-an="donations-left-amount">
																	<?php
																		if ($cl["li"]["donation_raised"] >= $cl["li"]["donation_amount"]) {
																			echo cl_money("0.00");
																		}
																		else{
																			echo cl_money($cl["li"]["donation_amount"] - $cl["li"]["donation_raised"]);
																		}
																	 ?>
																</span>

																<?php echo cl_translate("to go"); ?>
															</span>
														</div>
													</div>
													<?php if ($cl["li"]["is_owner"] != true): ?>
														<div class="publication-funding__footer">
															<button onclick="SMColibri.donate_post(<?php echo($cl["li"]["id"]); ?>)" class="btn target-url-btn btn-custom main-green lg btn-block">
																<?php echo cl_translate("Donate now"); ?>
															</button>
														</div>
													<?php else: ?>
														<div class="publication-funding__footer">
															<button onclick="SMColibri.donate_self();" class="btn target-url-btn btn-custom main-green lg btn-block">
																<?php echo cl_translate("Donate now"); ?>
															</button>
														</div>
													<?php endif; ?>
												</div>
											<?php endif; ?>
										<?php endif; ?>
									</div>
													</div>

									<div class="post-data-layout__controls" style="margin-left: 2px; margin-top: -20px;">
									

										<?php if (empty($cl['li']['has_liked'])): ?>
											<button class="ctrls-item" onclick="SMColibri.like_post('<?php echo $cl['li']['id']; ?>', this);">
												<span class="ctrls-item__icon">
													<?php echo cl_ficon('thumb_like'); ?>
													
												</span>
												<span class="num" data-an="likes-count">
												<?php echo $cl['li']['likes_count']; ?>
										</span>
											</button>
										<?php else: ?>
											<button class="ctrls-item liked" onclick="SMColibri.like_post('<?php echo $cl['li']['id']; ?>', this);">
												<span class="ctrls-item__icon">
													<?php echo cl_ficon('thumb_like'); ?>
													</span>
												<span class="num" data-an="likes-count">
												<?php echo $cl['li']['likes_count']; ?>
										</span>
											</button>
										<?php endif; ?>
											<button class="ctrls-item">
											<a class="ctrls-item__inner-link" href="<?php echo $cl['li']['url']; ?>" data-spa="true">
											<span class="num" style="font-size: 13px; padding-right: 5px;">
												<?php echo cl_translate('reply_thread'); ?>
													
												</span>
												<span class="num"><?php echo $cl['li']['replys_count']; ?></span> 
											</a>
										</button>
										
									
										<div class="ctrls-item dropleft">
										
											<div style=" margin-top: -2px !important;" class=" ctrls-item__icon" style="color:  var(--cl-secondary-text-color) !important;" data-toggle="dropdown">
												<svg fill="var(--cl-secondary-text-color)" width="20px" height="20px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">

<g data-name="Layer 2">

<g data-name="more-horizotnal">

<rect width="24" height="24" opacity="0"/>

<circle cx="12" cy="12" r="2"/>

<circle cx="19" cy="12" r="2"/>

<circle cx="5" cy="12" r="2"/>

</g>

</g>

</svg>
											</div>
											<div style="border: solid 1px var(--cl-primary-border-color);
											box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
											"class="dropdown-menu dropdown-icons">
												<?php if (not_empty($cl['li']['is_owner'])): ?>
													<?php if ($cl["li"]["type"] == "poll"): ?>
														<?php if ($cl['li']["poll_status"] == "active"): ?>
															<a onclick="SMColibri.stop_poll(<?php echo($cl['li']['id']); ?>);" class="dropdown-item" href="javascript:void(0);">
																<span class="flex-item dropdown-item-icon">
																	<?php echo cl_ficon('poll'); ?>
																</span>
																<span class="flex-item">
																	<?php echo cl_translate('Stop this poll'); ?>
																</span>
																<span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
																	<?php echo cl_ficon('timer_off'); ?>
																</span>
															</a>
															<div class="dropdown-divider"></div>
														<?php endif; ?>
													<?php endif; ?>
												<?php endif; ?>

												<a class="dropdown-item" href="<?php echo $cl['li']['url']; ?>" data-spa="true">
													<span class="flex-item dropdown-item-icon">
														<?php echo cl_ficon('open'); ?>
													</span>
													<span class="flex-item">
														<?php echo cl_translate('Show thread'); ?>
													</span>
												</a>
												<?php if ($cl['li']['type'] == "document"): ?>
													<div class="dropdown-divider"></div>
													<a download="<?php echo $cl['li']['media'][0]['x']["filename"]; ?>" class="dropdown-item" href="<?php echo cl_get_media(fetch_or_get($cl['li']['media'][0]['src'],'')); ?>">
														<span class="flex-item dropdown-item-icon">
															<?php echo cl_ficon('arrow_download'); ?>
														</span>
														<span class="flex-item">
															<?php echo cl_translate('Download document'); ?>
														</span>
														<span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
															<?php echo cl_ficon('document'); ?>
														</span>
													</a>
												<?php endif; ?>
												<?php if ($cl["config"]["post_video_download_system"] == "on" && $cl['li']['type'] == "video"): ?>
													<div class="dropdown-divider"></div>
													<a download="<?php echo cl_strf("%s-Video-Publication", $cl["config"]["name"]); ?>" class="dropdown-item" href="<?php echo cl_get_media(fetch_or_get($cl['li']['media'][0]['src'],'')); ?>">
														<span class="flex-item dropdown-item-icon">
															<?php echo cl_ficon('arrow_download'); ?>
														</span>
														<span class="flex-item">
															<?php echo cl_translate('Download video'); ?>
														</span>
														<span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
															<?php echo cl_ficon('video'); ?>
														</span>
													</a>
												<?php endif; ?>
												<?php if ($cl["config"]["post_audio_download_system"] == "on" && $cl['li']['type'] == "audio"): ?>
													<div class="dropdown-divider"></div>
													<a download="<?php echo cl_strf("%s-Audio-Publication", $cl["config"]["name"]); ?>" class="dropdown-item" href="<?php echo cl_get_media(fetch_or_get($cl['li']['media'][0]['src'],'')); ?>">
														<span class="flex-item dropdown-item-icon">
															<?php echo cl_ficon('arrow_download'); ?>
														</span>
														<span class="flex-item">
															<?php echo cl_translate('Download audio'); ?>
														</span>
														<span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
															<?php echo cl_ficon('music'); ?>
														</span>
													</a>
												<?php endif; ?>
												<div class="dropdown-divider"></div>
												<?php if (not_empty($cl['li']['can_edit'])): ?>
													<a onclick="SMColibri.edit_post('<?php echo $cl['li']['id']; ?>');" class="dropdown-item" href="javascript:void(0);">
														<span class="flex-item dropdown-item-icon">
															<?php echo cl_ficon('note_edit'); ?>
														</span>
														<span class="flex-item">
															<?php echo cl_translate('Edit post'); ?>
														</span>
														<span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
															<?php echo cl_ficon('text'); ?>
														</span>
													</a>
													<div class="dropdown-divider"></div>
												<?php endif; ?>
												<?php if (not_empty($cl['li']['can_delete'])): ?>
													<a onclick="SMColibri.delete_post('<?php echo $cl['li']['id']; ?>');" class="dropdown-item" href="javascript:void(0);">
														<span class="flex-item dropdown-item-icon">
															<?php echo cl_ficon('delete'); ?>
														</span>
														<span class="flex-item" style="color: #f50000 !important;">
															<?php echo cl_translate('Delete'); ?>
														</span>
													</a>
													<div class="dropdown-divider"></div>
												<?php endif; ?>
												<a onclick="SMColibri.show_likes('<?php echo $cl['li']['id']; ?>');" class="dropdown-item" href="javascript:void(0);">
													<span class="flex-item dropdown-item-icon">
														<?php echo cl_ficon('thumb_like-drop'); ?>
													</span>
													<span class="flex-item">
														<?php echo cl_translate('Show likes'); ?>
													</span>
													<span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
														<?php echo cl_ficon('users_list'); ?>
													</span>
												</a>
												<div class="dropdown-divider"></div>
												<a class="dropdown-item" href="javascript:void(0);" onclick="SMColibri.bookmark_post('<?php echo $cl['li']['id']; ?>', this);">
													<span class="flex-item dropdown-item-icon">
														<?php echo cl_ficon('bookmark'); ?>
													</span>
													<span class="flex-item" data-itag="text">
														<?php echo ((empty($cl['li']['has_saved'])) ? cl_translate('Bookmark') : cl_translate('Unbookmark')); ?>
													</span>
												</a>
												<a data-clipboard-text="<?php echo($cl['li']['url']); ?>" class="dropdown-item clip-board-copy" href="javascript:void(0);">
													<span class="flex-item dropdown-item-icon">
														<?php echo cl_ficon('clipboard_link'); ?>
													</span>
													<span class="flex-item">
														<?php echo cl_translate('Copy link'); ?>
													</span>
												</a>
												<?php if (empty($cl['li']['is_owner'])): ?>
													<div class="dropdown-divider"></div>
													<a onclick="SMColibri.report_post(<?php echo($cl['li']['id']); ?>);" class="dropdown-item" href="javascript:void(0);">
														<span class="flex-item dropdown-item-icon">
															<?php echo cl_ficon('flag'); ?>
														</span>
														<span class="flex-item">
															<?php echo cl_translate('Report post'); ?>
														</span>
														<span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
															<?php echo cl_ficon('shield_error'); ?>
														</span>
													</a>
												<?php endif; ?>
												<?php if ($cl['li']['owner']['is_premium'] == '1'): ?>
												<?php if (not_empty($cl['li']['is_owner'])): ?>
													<div class="dropdown-divider"></div>
													<a onclick="SMColibri.pin_profile_post(<?php echo($cl['li']['id']); ?>, this);" class="dropdown-item" href="javascript:void(0);">
														<span class="flex-item dropdown-item-icon">
															<?php echo cl_ficon('pin'); ?>
														</span>
														
														<span class="flex-item" data-itag="text">
															<?php if ($cl['li']['profile_pinned'] == "Y"): ?>
																<?php echo cl_translate('Unpin from my profile'); ?>
															<?php else: ?>
																<?php echo cl_translate('Pin to my profile'); ?>
															<?php endif; ?>
														</span>
														<span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
															<?php echo cl_ficon('note'); ?>
														</span>
													</a>
													<?php endif; ?>
												<?php endif; ?>
												<div class="dropdown-divider"></div>
												<a onclick="SMColibri.share_post('<?php echo $cl['li']['url']; ?>','<?php echo urlencode($cl['li']['url']); ?>');" class="dropdown-item" href="javascript:void(0);">
													<span class="flex-item dropdown-item-icon">
														<?php echo cl_ficon('share-drop'); ?>
													</span>
													<span class="flex-item">
														<?php echo cl_translate('Share'); ?>
													</span>
												</a>
												<?php if (not_empty($cl['is_admin'])): ?>
													<div class="dropdown-divider"></div>
													<a onclick="SMColibri.pin_admin_post(<?php echo($cl['li']['id']); ?>, this);" class="dropdown-item" href="javascript:void(0);">
														<span class="flex-item dropdown-item-icon">
															<?php echo cl_ficon('pin'); ?>
														</span>
														<span class="flex-item" data-itag="text">
															<?php if ($cl['li']['admin_pinned'] == "Y"): ?>
																<?php echo cl_translate('Unpin post from feeds'); ?>
															<?php else: ?>
																<?php echo cl_translate('Pin post to feeds'); ?>
															<?php endif; ?>
														</span>
														<span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
															<?php echo cl_ficon('alert_urgent'); ?>
														</span>
													</a>
												<?php endif; ?>
											</div>
										</div>
									
						</div>
									
									
								</div>
							</div>	
						</div>
					</div>
				</div>
			<?php endif;?>
		</div>

		<?php if (cl_is_feed_gad_allowed()): ?>
			<?php if (not_empty($cl["gads_horiz"])): ?>
				<?php if (cl_show_feed_gad()): ?>
					<div class="cl-google-ads centered">
						<?php echo $cl["gads_horiz"]; ?>
					</div>
				<?php endif; ?>
			<?php endif; ?>
		<?php endif; ?>
	<?php endif; ?>
<?php endif; ?>
<?php endif; ?>

