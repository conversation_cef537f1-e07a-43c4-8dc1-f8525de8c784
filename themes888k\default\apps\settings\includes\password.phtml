<div class="timeline-container">
	<div class="timeline-header" data-el="tl-header">
		<div class="timeline-header__botline">
			<div class="lp">
				<div class="nav-link-holder">
					<a href="<?php echo cl_link("settings/password"); ?>" data-spa="true">
						<?php echo cl_translate("User password"); ?>
					</a>
				</div>
			</div>
			<div class="cp">
				<a href="<?php echo cl_link("home"); ?>">
					<img src="{%config site_logo%}" alt="Logo">
				</a>
			</div>
			<div class="rp">
				<div class="nav-link-holder">
					<span class="go-back" onclick="SMColibri.go_back();">
						<?php echo cl_ficon('arrow_back'); ?>
					</span>
				</div>
			</div>
		</div>
	</div>
	<div class="profile-settings">
		<div class="profile-settings__content">
			<div class="settings-form">
				<form class="form" id="cl-password-settings-vue-app" v-on:submit="submit_form($event)">
					<div class="form-group">
		                <label>
		                    <?php echo cl_translate("Current password"); ?>
		                </label>
		                <div class="password-ctrl">
		                    <input v-model.trim.lazy="$v.password1.$model" name="curr_password" v-bind:type="password1_display" class="form-control" placeholder="<?php echo cl_translate("Enter your current password"); ?>">
		                    <button class="password-ctrl" type="button" v-on:click="password1_display_toggle">
		                        <span v-if="password1_display == 'password'">
		                            <?php echo cl_ficon("eye_show"); ?>
		                        </span>
		                        <span v-else>
		                            <?php echo cl_ficon("eye_hide"); ?>
		                        </span>
		                    </button>
		                </div>
		                <div class="invalid-main-feedback" v-if="is_valid_password1">
		                    {{invalid_feedback_pass1}}
		                </div>
		            </div>
		            <div class="form-group">
		                <label>
		                    <?php echo cl_translate("New password"); ?>
		                </label>
		                <div class="password-ctrl">
		                    <input v-model.trim.lazy="$v.password2.$model" name="new_password" v-bind:type="password2_display" class="form-control" placeholder="<?php echo cl_translate("Enter new password"); ?>">
		                    <button class="password-ctrl" type="button" v-on:click="password2_display_toggle">
		                        <span v-if="password2_display == 'password'">
		                            <?php echo cl_ficon("eye_show"); ?>
		                        </span>
		                        <span v-else>
		                            <?php echo cl_ficon("eye_hide"); ?>
		                        </span>
		                    </button>
		                </div>
		                <div class="invalid-main-feedback" v-if="is_valid_password2">
		                    {{invalid_feedback_pass2}}
		                </div>
		            </div>
		            <div class="form-group no-mb">
		                <label>
		                    <?php echo cl_translate("Confirm new password"); ?>
		                </label>
		                <div class="password-ctrl">
		                    <input v-model.trim="$v.password3.$model" name="new_conf_pass" v-bind:type="password3_display" class="form-control" placeholder="<?php echo cl_translate("Confirm new password"); ?>">
		                    <button class="password-ctrl" type="button" v-on:click="password3_display_toggle">
		                        <span v-if="password3_display == 'password'">
		                            <?php echo cl_ficon("eye_show"); ?>
		                        </span>
		                        <span v-else>
		                            <?php echo cl_ficon("eye_hide"); ?>
		                        </span>
		                    </button>
		                </div>
		                <div class="invalid-main-feedback" v-if="is_valid_password3">
		                    {{invalid_feedback_pass3}}
		                </div>
		            </div>
		            <div class="form-group" v-if="unsuccessful_attempt">
		                <div class="invalid-main-feedback">
		                    <?php echo cl_translate("Something went wrong while trying to save your changes, please try again later"); ?>
		                </div>
		            </div>
		            <div class="form-group" v-else>
		                <div class="form-info-label">
		                    <?php echo cl_translate("Before changing your current password, please follow these tips: Indicate the minimum length (6 characters). Use uppercase and lowercase letters. Use numbers and special characters (&%$)"); ?>
		                </div>
		            </div>
		            <input type="hidden" class="d-none" value="<?php echo fetch_or_get($cl['csrf_token'],'none'); ?>" name="hash">
		            <div class="form-group no-mb d-flex">
		            	<button v-if="submitting != true" v-bind:disabled="$v.$invalid == true" type="submit" class="ml-auto btn btn-custom main-inline lg">
	                        <?php echo cl_translate("Save changes"); ?>
	                    </button>
	                    <button v-else disabled="true" type="button" class="ml-auto btn btn-custom main-inline lg">
	                        <?php echo cl_translate("Please wait"); ?>
	                    </button>
		            </div>
		        </form>
			</div>
		</div>
	</div>

	<script>
		"use strict";

		$(document).ready(function($) {
			Vue.use(window.vuelidate.default);
			var VueValids = window.validators;

			new Vue({
				el: "#cl-password-settings-vue-app",
				data: {
					submitting: false,
					unsuccessful_attempt: false,
					invalid_feedback_pass1: "",
					invalid_feedback_pass2: "",
					invalid_feedback_pass3: "",
					password1: "",
					password2: "",
					password3: "",
					invalid_curr_pass: false,
					password1_display: "password",
					password2_display: "password",
					password3_display: "password"
				},
				computed: {
					is_valid_password1: function() {
						if (this.$v.password1.required == true && this.$v.password1.$error) {
							this.invalid_feedback_pass1 = "<?php echo cl_translate("Please enter your current password!"); ?>";
							return true;
						}

						else if(this.invalid_curr_pass == true) {
							this.invalid_feedback_pass1 = "<?php echo cl_translate("The current password you entered is not correct, please check your details and try again!"); ?>";
							return true;
						}

						else {
							this.invalid_feedback_pass1 = "";
							return false;
						}
					},
					is_valid_password2: function() {	
						if(this.$v.password2.required == true && this.$v.password2.$error) {
							this.invalid_feedback_pass2 = "<?php echo cl_translate("Please enter your new password, from 6 to 20 characters!"); ?>";
							return true;
						}

						else {
							this.invalid_feedback_pass2 = "";
							return false;
						}
					},
					is_valid_password3: function() {	
						if(this.$v.password3.required == true && this.$v.password3.$error) {
							this.invalid_feedback_pass3 = "<?php echo cl_translate("Passwords do not mutch!"); ?>";
							return true;
						}

						else {
							this.invalid_feedback_pass3 = "";
							return false;
						}
					}
				},
				validations: {
					password1: {
						required: VueValids.required,
						min_length: VueValids.minLength(6),
						max_length: VueValids.maxLength(20)
					},
					password2: {
						required: VueValids.required,
						min_length: VueValids.minLength(6),
						max_length: VueValids.maxLength(20)
					},
					password3: {
						required: VueValids.required,
						sameAs: VueValids.sameAs('password2')
					}
				},
				methods: {
					submit_form: function(_self = null) {
						_self.preventDefault();

						var _app_ = this;

						$(_self.target).ajaxSubmit({
							url: "<?php echo cl_link("native_api/settings/save_profile_pass"); ?>",
							type: 'POST',
							dataType: 'json',
							beforeSend: function() {
								_app_.submitting = true;
								_app_.unsuccessful_attempt = false;
								_app_.invalid_curr_pass = false;
							},
							success: function(data) {
								if (data.status == 200) {
									cl_bs_notify("<?php echo cl_translate("Your changes has been successfully saved!"); ?>", 5000, "success");

									_app_.password1 = "";
									_app_.password2 = "";
									_app_.password3 = "";
								}

								else if(data.err_code == "invalid_curr_pass") {
									_app_.invalid_curr_pass = true;
								}

								else {
									_app_.unsuccessful_attempt = true;
								}
							},
							complete: function() {
								_app_.submitting = false;
							}
						});
					},
					password1_display_toggle: function() {
						var _app_ = this;

						if (_app_.password1_display == "text") {
							_app_.password1_display = "password";
						}
						else{
							_app_.password1_display = "text";
						}
					},
					password2_display_toggle: function() {
						var _app_ = this;

						if (_app_.password2_display == "text") {
							_app_.password2_display = "password";
						}
						else{
							_app_.password2_display = "text";
						}
					},
					password3_display_toggle: function() {
						var _app_ = this;

						if (_app_.password3_display == "text") {
							_app_.password3_display = "password";
						}
						else{
							_app_.password3_display = "text";
						}
					}
				}
			});
		});
	</script>
</div>