<div class="right-sb-container sidebar" data-app="right-sidebar">
	<div class="sidebar__inner">
		<div class="right-sidebar">
			<div class="right-sidebar__header">
				<form class="form sidebar-searchbar" id="vue-main-search-app" v-on:submit="search_onsubmit" autocomplete="off">
					<div class="sidebar-searchbar__input">	
						<input v-model="search_query" v-on:input="search" type="text" class="form-control" placeholder="<?php echo cl_translate("Search"); ?>">
						
						<span class="keyword-input__right-spinner" v-if="searching">
							<?php echo cl_icon('spinner-icon'); ?>
						</span>
						<span v-on:click="cancel" class="keyword-input__right-icon" v-else-if="search_result">
							<?php echo cl_ficon('dismiss'); ?>
						</span>
						<span class="keyword-input__left-icon" v-else>
							<?php echo cl_ficon('search'); ?>
						</span>
					</div>
					<div class="sidebar-searchbar__result" v-show="search_result">
						<div class="search-result">
							<div class="search-result__body">
								<div class="search-result__list" data-an="result"></div>
							</div>
							<div class="search-result__footer" v-bind:class="{'disabled': (advanced_search != true)}">
								<a v-bind:href="search_page_url" data-spa="true">
									<button class="btn btn-custom main-inline md btn-block">
										<?php echo cl_translate('Advanced search'); ?>
									</button>
								</a>
							</div>
						</div>						
					</div>
				</form>
			</div>
			
			<?php if (cl_is_feed_gad_allowed()): ?>
				<?php if (not_empty($cl["gads_vert"])): ?>
					<div class="right-sidebar__ads">
						<div class="cl-google-ads">
							<?php echo $cl["gads_vert"]; ?>
						</div>
					</div>
				<?php endif; ?>
			<?php endif; ?>
			<div class="right-sidebar__body">
			<?php if ($cl['config']['prem_account_system_status'] == 'on'): ?>
				<?php if (not_empty($cl["is_logged"])): ?>
				<?php if ($me["is_premium"] == 1): ?>

				<?php else: ?>
					
					<div class="right-sidebar__body" style="color: var(--cl-primary-text-color); padding: 20px; margint-bottom: 10px; border-radius: 20px; border: solid 1px #4c4c4c;">
					<span style="font-size: 19px !important; font-weight: bolder !important; color: var(--cl-primary-text-color);">✨<?php echo cl_translate('premium_upgrade'); ?></span>
					<br>
					<?php echo cl_translate('premium_upgrade_text'); ?>
			<p>
			<div class="send-money-btn">
				
								<a style="color: var( --cl-primary-text-color) !important;" href="<?php echo cl_link("premium_account"); ?>" data-spa="true">
									<?php echo cl_translate('premium_upgrade'); ?>
								</a>
				</div>
				</div>
									<?php endif; ?>
					<?php endif; ?><?php endif; ?>
					<p>
			<div class="sidebar-content sidebar-content_hashtags">
					<div class="sidebar-content__header">
						<?php echo cl_translate("Hot topics for you"); ?>
					</div>
					<div class="sidebar-content__body">
						<?php if (not_empty($cl['hot_topics'])): ?>
							<div class="sidebar-hashtags">
								<?php foreach ($cl['hot_topics'] as $row): ?>
									<div class="sidebar-hashtags__item" title="<?php echo cl_translate("Posts"); ?> <?php echo($row['total']); ?>">
										<a class="sidebar-hashtags__item-data"  href="<?php echo($row['url']); ?>"  data-spa="true">
											<span class="icon">
												<?php echo cl_icon("tag"); ?>
											</span>
											<span class="text">
											 <?php echo($row['tag']); ?> 
											
										</a>
										</span> <span style="margin-left: -10px; color: var(--cl-uname-color) !important; font-size: 11px;"><?php echo($row['total']); ?> <?php echo cl_translate("Posts"); ?></span>
									</div>
								<?php endforeach; ?>
							</div>
						<?php else: ?>
							<div class="sidebar-content-empty">
								<?php echo cl_translate("Here will be a (#hashtag) list of the most relevant topics and events"); ?>
							</div>
						<?php endif; ?>
					</div>
				</div>
			
				<div class="sidebar-content sidebar-content_users">
					<div class="sidebar-content__header">
						<?php echo cl_translate("Who to follow"); ?>
					</div>
					<div class="sidebar-content__body">
						<?php if (not_empty($cl['follow_suggestion'])): ?>
							<div class="sidebar-users">
								<?php foreach ($cl['follow_suggestion'] as $row): ?>
									<div class="sidebar-users__item">
										<span style="position: absolute; left: 75% !important;">
											 <?php if ($row['follow_privacy'] == "approved"): ?>
										<?php if (not_empty($row['follow_requested'])): ?>
											<button onclick="SMColibri.req_follow(this);" class="btn btn-custom main-inline md" data-action="unfollow" data-user-name="<?php echo($row['name']); ?>" data-id="<?php echo($row['id']); ?>">
												<?php echo cl_translate("Pending"); ?>
											</button>
										<?php else: ?>
											<button onclick="SMColibri.req_follow(this);" class="btn btn-custom main-outline md" data-action="follow" data-user-name="<?php echo($row['name']); ?>" data-id="<?php echo($row['id']); ?>">
												<?php echo cl_translate("Follow"); ?>
											</button>
										<?php endif; ?>
									<?php else: ?>
										<button onclick="SMColibri.follow(this);" class="btn btn-custom sm main-outline" data-action="follow" data-user-name="<?php echo($row['name']); ?>" data-id="<?php echo($row['id']); ?>">
											<?php echo cl_translate("Follow"); ?>
										</button>
									<?php endif; ?>
											
											</span>
										<div class="sidebar-users__item-avatar <?php if(cl_is_online($row['is_online'])) {echo "sidebar-users__item-avatar-online";} ?>">
											<a href="<?php echo($row['url']); ?>" class="block-link" data-spa="true">
												<img src="<?php echo($row['avatar']); ?>" alt="avatar">
											</a>
										</div>
										<div class="sidebar-users__item-data">
											<div class="sidebar-users__item-name text-limit" data-uinfo-lbox="<?php echo($row['id']); ?>" data-toggle="popover" data-placement="top">
												<a href="<?php echo($row['url']); ?>" class="block-link" data-spa="true">
													<span class="user-name-holder">
														<span class="user-name-holder__name">
															<?php echo $row['name']; ?>
														</span>

														<?php if ($row['verified'] == '1'): ?>
															<span class="user-name-holder__badge" style="width: 14px; height: 14px; margin-top: -3px; margin-left: 4px;">
																<?php echo cl_icon("verified_user_badge"); ?>
															</span>
														<?php endif; ?>
													</span>
												</a>
											</div>
											<div class="sidebar-users__item-followers">
												<span><?php echo($row['followers']); ?></span> <?php echo cl_translate('Followers'); ?>
											</div>
										</div>
									</div>
								<?php endforeach; ?>
							</div>
						<?php else: ?>
							<div class="sidebar-content-empty">
								<?php echo cl_translate("Here will be a list of the most recommended people to follow"); ?>
							</div>
						<?php endif; ?>
					</div>
					<?php if (not_empty($cl['follow_suggestion'])): ?>
						<div class="sidebar-content__footer">
							<a href="<?php echo cl_link("suggested"); ?>" data-spa="true">
								<?php echo cl_translate("Show more"); ?>
							</a>
						</div>
					<?php endif; ?>
				</div>
				
			<div class="right-sidebar__footer">
				<?php echo cl_template('main/footer'); ?>
			</div>
		</div>
	</div>
</div>

<script>
	new Vue({
		el: "#vue-main-search-app",
		data: {
			searching: false,
			search_query: "",
			advanced_search: false,
			search_result: false
		},
		computed: {
			search_page_url: function() {
				if (this.search_query.length > 2) {
					var search_htags_url = "<?php echo cl_link("search/htags?q={0}"); ?>";
					var search_users_url = "<?php echo cl_link("search/people?q={0}"); ?>";
					this.advanced_search = true;

					if (this.search_query.charAt(0) == '#') {
						return search_htags_url.format(this.search_query.replace('#',''));
					}
					else {
						return search_users_url.format(this.search_query);
					}
				}
				else {

					this.advanced_search = false;

					return "javascript:void(0);";
				}
			},
			query_type: function() {
				if (this.search_query.charAt(0) == '#') {
					return 'htags';
				}

				else {
					return 'users';
				}
			}
		},
		methods: {
			search_onsubmit: function(e = false) {
				e.preventDefault();

				this.search();
			},
			search: function() {
				if (this.search_query.length > 2) {
					var _app_ = this;
					delay(function() {
						$.ajax({
							url: '<?php echo cl_link("native_api/main/search"); ?>',
							type: 'GET',
							dataType: 'json',
							data: {
								query: _app_.search_query.replace('#',''),
								type: _app_.query_type
							},
							beforeSend: function() {
								_app_.searching = true;
							}
						}).done(function(data) {
							if (data.status == 200) {
								_app_.search_result = true;
								$(_app_.$el).find('[data-an="result"]').html($(data.html));
							}
							else {
								$(_app_.$el).find('[data-an="result"]').empty('');
								_app_.search_result = false;
							}
						}).always(function() {
							_app_.searching = false;
						});
					}, 800);
				}
			},
			cancel: function() {
				var _app_             = this;
				_app_.searching       = false;
				_app_.search_query    = "";
				_app_.advanced_search = false;
				_app_.search_result   = false;
				
				$(_app_.$el).find('[data-an="result"]').empty('');
			}
		}
	});
</script>