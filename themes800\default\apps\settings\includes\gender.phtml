<div class="timeline-container">
	<div class="timeline-header" data-el="tl-header">
		<div class="timeline-header__botline">
			<div class="lp">
				<div class="nav-link-holder">
					<a href="<?php echo cl_link("settings/gender"); ?>" data-spa="true">
						<?php echo cl_translate("User gender"); ?>
					</a>
				</div>
			</div>
			<div class="cp">
				<a href="<?php echo cl_link("home"); ?>">
					<img src="{%config site_logo%}" alt="Logo">
				</a>
			</div>
			<div class="rp">
				<div class="nav-link-holder">
					<span class="go-back" onclick="SMColibri.go_back();">
						<?php echo cl_ficon('arrow_back'); ?>
					</span>
				</div>
			</div>
		</div>
	</div>
	<div class="profile-settings">
		<div class="profile-settings__content">
			<div class="settings-form">
				<form class="form" id="cl-gender-settings-vue-app" v-on:submit="submit_form($event)">
					<div class="form-group">
                        <label>
                            <?php echo cl_translate("Select your gender"); ?>   
                        </label>
                        <div class="dropdown vue-dropdown-select">
                            <button class="btn btn-secondary dropdown-toggle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                {{dropdowns.gender}}
                            </button>
                            <div class="dropdown-menu">
                                <a v-on:click="dropdown_select('gender', 'M', $event)" class="dropdown-item" href="javascript:void(0);">
                                    <?php echo cl_translate("Male"); ?>
                                </a>
                                <a v-on:click="dropdown_select('gender', 'F', $event)" class="dropdown-item" href="javascript:void(0);">
                                    <?php echo cl_translate("Female"); ?>
                                </a>

                                <?php if ($cl['config']['non_binary_gender'] == 'on'): ?>
	                                <a v-on:click="dropdown_select('gender', 'T', $event)" class="dropdown-item" href="javascript:void(0);">
	                                    <?php echo cl_translate("They"); ?>
	                                </a>
	                                <a v-on:click="dropdown_select('gender', 'O', $event)" class="dropdown-item" href="javascript:void(0);">
	                                    <?php echo cl_translate("Other"); ?>
	                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <div class="form-group" v-if="unsuccessful_attempt">
                        <div class="invalid-main-feedback">
                            <?php echo cl_translate("Something went wrong while trying to save your changes, please try again later"); ?>
                        </div>
                    </div>
                    <div class="form-group" v-else>
                        <div class="form-info-label">
                            <?php echo cl_translate("Please choose your gender, this is necessary for a more complete identification of your profile. (Default user gender is Male)"); ?>
                        </div>
                    </div>
                    <input type="hidden" class="d-none" value="<?php echo fetch_or_get($cl['csrf_token'],'none'); ?>" name="hash">
                    <input v-model="gender" type="hidden" class="d-none" name="gender">
                    <div class="form-group no-mb d-flex">
                    	<button v-if="submitting != true" v-bind:disabled="$v.$invalid == true" type="submit" class="ml-auto btn btn-custom main-inline lg">
	                        <?php echo cl_translate("Save changes"); ?>
	                    </button>
	                    <button v-else disabled="true" type="button" class="ml-auto btn btn-custom main-inline lg">
	                        <?php echo cl_translate("Please wait"); ?>
	                    </button>
                    </div>
				</form>
			</div>
		</div>
	</div>

	<script>
		"use strict";

		$(document).ready(function($) {
			Vue.use(window.vuelidate.default);
			var VueValids = window.validators;

			new Vue({
				el: "#cl-gender-settings-vue-app",
				data: {
					submitting: false,
					unsuccessful_attempt: false,
					gender: "<?php echo($me['gender']); ?>",
					dropdowns: {
						gender: "<?php echo cl_translate("Select your gender"); ?>"
					}
				},
				validations: {
					gender: {
						required: VueValids.required
					}
				},
				methods: {
					submit_form: function(_self = null) {
						_self.preventDefault();

						var _app_ = this;

						$(_self.target).ajaxSubmit({
							url: "<?php echo cl_link("native_api/settings/save_profile_gender"); ?>",
							type: 'POST',
							dataType: 'json',
							beforeSend: function() {
								_app_.submitting = true;
								_app_.unsuccessful_attempt = false;
							},
							success: function(data) {
								if (data.status == 200) {
									cl_bs_notify("<?php echo cl_translate("Your changes has been successfully saved!"); ?>", 5000, "success");
								}

								else {
									_app_.unsuccessful_attempt = true;
								}
							},
							complete: function() {
								_app_.submitting = false;
							}
						});
					},
					dropdown_select: function(key = "none", val = false, e = false, on_change = false) {
	                    var _app_ = this;

	                    _app_.$v[key].$model = val;
	                    _app_.dropdowns[key] = $.trim($(e.target).text());

	                    if (on_change) {
	                        on_change();
	                    }
	                }
				},
				mounted: function() {
					if (this.gender == 'F') {
						this.dropdowns.gender = "<?php echo cl_translate("Female"); ?>";
					}
					else if (this.gender == 'M') {
						this.dropdowns.gender = "<?php echo cl_translate("Male"); ?>";
					}
					else if (this.gender == 'T') {
						this.dropdowns.gender = "<?php echo cl_translate("They"); ?>";
					}
					else if (this.gender == 'O') {
						this.dropdowns.gender = "<?php echo cl_translate("Other"); ?>";
					}
				}
			});
		});
	</script>
</div>