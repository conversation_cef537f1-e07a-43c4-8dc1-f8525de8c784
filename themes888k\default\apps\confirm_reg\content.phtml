<div class="timeline-container" data-app="confirm_registration">
	<div class="timeline-header" data-el="tl-header">
		<div class="timeline-header__botline">
			<div class="lp">
				<div class="nav-link-holder">
					<a href="<?php echo cl_link("confirm_registration"); ?>" data-spa="true">
						<?php echo cl_translate("Confirm registration"); ?>
					</a>
				</div>
			</div>
			<div class="cp">
				<a href="<?php echo cl_link("/"); ?>">
					<img src="{%config site_logo%}" alt="Logo">
				</a>
			</div>
			<div class="rp">
				<div class="nav-link-holder">
					<a href="<?php echo cl_link("about_us"); ?>" class="go-forward">
						<?php echo cl_ficon('book_information'); ?>
					</a>
				</div>
			</div>
		</div>
	</div>
	
	<div class="confirm-registration">
		<div class="timeline-placeholder">
			<div class="icon">
				<div class="icon__bg">
					<?php echo cl_ficon('checkbox_person'); ?>
				</div>
			</div>
			<div class="pl-message">
				<h4>
					<?php echo cl_translate("Thank you for Signing-Up!"); ?>
				</h4>
				<?php if ($cl["config"]["signup_conf_system"] == "phone"): ?>
					<p>
						<?php echo cl_translate("We have just sent you an SMS message with your account activation code to your phone number, enter this code in the (Activation Code) field and click Confirm!"); ?>
					</p>
				<?php else: ?>
					<p>
						<?php echo cl_translate("Please check your email inbox, we just sent you an email with the activation code of your account, enter this code in the (Activation code) field and click confirm!"); ?>
					</p>
				<?php endif; ?>
			</div>
		</div>
		<form class="form" id="vue-confirm-reg-app" v-on:submit="submit_form($event)">
			<div class="form-body">
				<div class="form-group">
					<label>
						<?php echo cl_translate("Activation code"); ?>
					</label>
					<input v-model.trim="$v.code.$model" type="number" name="code" class="form-control" placeholder="<?php echo cl_translate("Enter your account activation code"); ?> ...">
					<div class="invalid-main-feedback" v-if="is_invalid_code">
						{{invalid_feedback_code}}
					</div>
					<div class="invalid-main-feedback" v-else-if="unsuccessful_attempt">
						<?php echo cl_translate("An error occurred while processing your request. Please try again later."); ?>
					</div>
				</div>
				<div class="form-group no-mb">
					<button v-if="done" disabled="true" class="btn btn-custom main-inline lg btn-block">
						<?php echo cl_translate("Done! Please wait.."); ?>
					</button>
					<button v-else-if="submitting" disabled="true" class="btn btn-custom main-inline lg btn-block">
						<?php echo cl_translate("Please wait"); ?>
					</button>
					<button v-else v-bind:disabled="is_invalid_form" class="btn btn-custom main-inline lg btn-block">
						<?php echo cl_translate("Confirm registration!"); ?>
					</button>
				</div>
			</div>
			<input type="hidden" class="d-none" value="<?php echo fetch_or_get($cl['csrf_token'],'none'); ?>" name="hash">
		</form>
	</div>

	<?php echo cl_template("confirm_reg/scripts/app_master_script"); ?>
</div>
