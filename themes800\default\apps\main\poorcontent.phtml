<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>
		<?php if (not_empty($me['new_notifs'])): ?>
			(<?php echo fetch_or_get($me['new_notifs']); ?>)
		<?php endif; ?>
		
		<?php echo fetch_or_get($cl["page_title"],$cl["config"]["title"]); ?>
	</title>

	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta name="title" content="<?php echo fetch_or_get($cl["page_title"], ''); ?>"/>
    <meta name="description" content="<?php echo fetch_or_get($cl["page_desc"], ''); ?>"/>
    <meta name="keywords" content="<?php echo fetch_or_get($cl['page_kw'], '');?>"/>
    <meta name="image" content="<?php echo fetch_or_get($cl['page_img'], $cl["config"]["site_logo"]);?>"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">

	<?php if ($cl["pn"] == 'thread'): ?>
		<meta property="og:type" content="article" />
		<meta property="og:image" content="<?php echo fetch_or_get($cl["page_image"], ''); ?>"/>
		<meta property="og:image" content="<?php echo fetch_or_get($cl["page_image"], ''); ?>" />
		<meta property="og:image:secure_url" content="<?php echo fetch_or_get($cl["page_image"], ''); ?>" />
		<meta property="og:description" content="<?php echo fetch_or_get($cl["page_desc"], ''); ?>" />
		<meta property="og:title" content="<?php echo fetch_or_get($cl["page_title"], ''); ?>" />
		<meta property="og:url" content="<?php echo fetch_or_get($cl["page_url"], $cl["config"]["url"]); ?>" />
		<meta name="twitter:card" content="summary">
		<meta name="twitter:title" content="<?php echo fetch_or_get($cl["page_title"], ''); ?>" />
		<meta name="twitter:description" content="<?php echo fetch_or_get($cl["page_desc"], ''); ?>" />
		<meta name="twitter:image" content="<?php echo fetch_or_get($cl["page_image"], ''); ?>" />
	<?php endif; ?>

	<?php if (in_array($cl["pn"], array("profile", "connections"))): ?>
		<?php if ($cl['prof_user']['index_privacy'] == 'N'): ?>
		    <meta name="robots" content="noindex">
		    <meta name="googlebot" content="noindex">
	    <?php endif; ?>
    <?php endif; ?>

	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css?v=<?php echo($cl["update_date"]); ?>">
	<link rel="stylesheet" href="{%config theme_url%}/statics/css/css-libs.css">
	<link rel="stylesheet" href="{%config theme_url%}/statics/css/libs/jquery.toast.min.css">
	<link rel="stylesheet" href="{%config theme_url%}/statics/css/libs/main.css">
	<link rel="stylesheet" href="{%config theme_url%}/statics/css/master.styles.css?version=485">

	<?php if ($cl["curr_lang"]["lang_data"]["is_rtl"] == "Y"): ?>
		<link rel="stylesheet" href="{%config theme_url%}/statics/css/master.styles.rtl.css?v=<?php echo($cl["update_date"]); ?>">
	<?php endif; ?>

	<link rel="stylesheet" href="{%config theme_url%}/statics/css/libs/jquery.fancybox.css?v=<?php echo($cl["update_date"]); ?>">

	<link rel="icon" href="{%config site_fav%}" type="image/png">
	<link rel="icon" href="{%config site_fav%}" type="image/x-icon">
	<link rel="manifest" href="/manifest.json">

	    <script type="module" src="{%config theme_url%}/statics/js/wave-audio-path-player.umd.js?version=20"></script>
	<script src="{%config theme_url%}/statics/js/libs/jquery-3.5.1.min.js?v=<?php echo($cl["update_date"]); ?>"></script>
	<?php if ($cl["server_mode"] == 'dev'): ?>
		<script src="{%config theme_url%}/statics/js/libs/vuejs/vue-v2.6.11.dev.min.js?v=<?php echo($cl["update_date"]); ?>"></script>
	<?php else: ?>
		<script src="{%config theme_url%}/statics/js/libs/vuejs/vue-v2.6.11.min.js?v=<?php echo($cl["update_date"]); ?>"></script>
	<?php endif; ?>

	<?php if (not_empty($cl["is_logged"]) && $cl["config"]["push_notifs"] == 'on'): ?>
		<script src="{%config theme_url%}/statics/js/libs/OneSignal/OneSignalSDK.js?v=<?php echo($cl["update_date"]); ?>"></script>


		<script>
		  window.OneSignal = window.OneSignal || [];
		  var cl_web_push_user_id = "<?php echo $cl["me"]["web_device_id"]; ?>";
		  var onesig_push_user_id = "";

		  OneSignal.push(function() {
		    OneSignal.init({
		      appId: "<?php echo $cl["config"]["onesignal_app_id"]; ?>",
		      autoRegister: true,
		      notifyButton: {
		        enable: true
		      },
		      persistNotification: false,
		      allowLocalhostAsSecureOrigin: true,
		      promptOptions: {
			      slidedown: {
			        prompts: [
			          {
			            type: "push",
			            autoPrompt: true,
			            text: {
			              actionMessage: "<?php echo cl_translate("{@site_name@} would like to send you notifications", array("site_name" => $cl["config"]["name"])); ?>",
			              acceptButton: "<?php cl_translate("Allow"); ?>",
			              cancelButton: "<?php cl_translate("Cancel"); ?>"
			            },
			            delay: {
			              pageViews: 1,
			              timeDelay: 20
			            }
			          }
			        ]
			      }
			    }
		    });

		    OneSignal.getUserId(function(onesig_user_id) {
	            onesig_push_user_id = onesig_user_id;

	            if (onesig_user_id != cl_web_push_user_id) {
	            	$.ajax({
	            		url: "<?php echo cl_link("native_api/main/update_web_device_id"); ?>",
	            		type: "POST",
	            		dataType: "json",
	            		data: {id: onesig_push_user_id},
	            	}).done(function(data) {
	            		if (data.status == 200) {
	            			console.log("Web device ID has been successfully updated. [OneSignal push notifications alert]");
	            		}
	            		else{
	            			console.log("Failed to update web device ID. [OneSignal push notifications alert]");
	            		}
	            	});
	            }
	        });

	        OneSignal.on('subscriptionChange', function (isSubscribed) {
				if (isSubscribed == true) {
					$.ajax({
	            		url: "<?php echo cl_link("native_api/main/update_web_device_id"); ?>",
	            		type: "POST",
	            		dataType: "json",
	            		data: {id: onesig_push_user_id},
	            	}).done(function(data) {
	            		if (data.status == 200) {
	            			console.log("Web device ID has been successfully updated. [OneSignal push notifications alert]");
	            		}
	            		else{
	            			console.log("Failed to update web device ID. [OneSignal push notifications alert]");
	            		}
	            	});
				} 

				else {
				    $.ajax({
	            		url: "<?php echo cl_link("native_api/main/delete_web_device_id"); ?>",
	            		type: "POST",
	            		dataType: "json"
	            	}).done(function(data) {
	            		if (data.status == 200) {
	            			console.log("Web device ID has been successfully deleted. [OneSignal push notifications alert]");
	            		}
	            		else{
	            			console.log("Failed to delete web device ID. [OneSignal push notifications alert]");
	            		}
	            	});
				}
			});
		  });
		</script>
	<?php endif; ?>
	
	<script src="{%config theme_url%}/statics/js/libs/vuejs/vue-plugins/validators.min.js?v=<?php echo($cl["update_date"]); ?>"></script>

	<?php if ($cl['config']['stripe_method_status'] == 'on'): ?>
		<script src="https://js.stripe.com/v3/"></script>
	<?php endif; ?>

	<?php if ($cl['config']['rzp_method_status'] == 'on'): ?>
		<script src="{%config theme_url%}/statics/js/libs/Razorpay/checkout.js?v=<?php echo($cl["update_date"]); ?>"></script>
	<?php endif; ?>
	
	<script src="{%config theme_url%}/statics/js/libs/vuejs/vue-plugins/vuelidate.min.js?v=<?php echo($cl["update_date"]); ?>"></script>
	<script src="{%config theme_url%}/statics/js/libs/jquery-plugins/jquery.form-v4.2.2.min.js?v=<?php echo($cl["update_date"]); ?>"></script>
	<script src="{%config theme_url%}/statics/js/libs/popper.1.12.9.min.js?v=<?php echo($cl["update_date"]); ?>"></script>
	<script src="{%config theme_url%}/statics/js/libs/bootstrap.v4.0.0.min.js?v=<?php echo($cl["update_date"]); ?>"></script>
	<script src="{%config theme_url%}/statics/js/libs/sticky-sidebar/source/jquery.sticky-sidebar.js?v=<?php echo($cl["update_date"]); ?>"></script>
	<script src="{%config theme_url%}/statics/js/master.script.js?v=<?php echo($cl["update_date"]); ?>"></script>
	<script src="{%config theme_url%}/statics/js/custom.js?version=20"></script>
	<script src="{%config theme_url%}/statics/js/libs/clipboard.min.js?v=<?php echo($cl["update_date"]); ?>"></script>
	<script src="{%config theme_url%}/statics/js/libs/jquery-plugins/jquery.fancybox.min.js?v=<?php echo($cl["update_date"]); ?>"></script>
	<script src="https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.umd.js"></script>
	<script src="{%config theme_url%}/statics/js/libs/lozad.min.js"></script>
	<script src="{%config theme_url%}/statics/js/libs/waitme/waitMe.min.js"></script>
	<script src="{%config theme_url%}/statics/js/libs/txt.autoresize.min.js"></script>
	<script src="{%config theme_url%}/statics/js/libs/audio-recorder.js"></script>
	<script src="{%config theme_url%}/statics/js/libs/readmore.js"></script>
	<script src="{%config theme_url%}/statics/js/libs/jquery-plugins/jquery-toast.min.js"></script>
	<script src="{%config theme_url%}/statics/js/libs/afterglow/afterglow.min.js?v={%config version%}"></script>
	
	<script>
		if ('serviceWorker' in navigator) {
		    window.addEventListener('load', function() {
		      navigator.serviceWorker.register('/themes/default/statics/js/service-worker.js').then(function(registration) {
		        console.log('Service Worker registered with scope:', registration.scope);
		      }).catch(function(error) {
		        console.log('Service Worker registration failed:', error);
		      });
		    });
		}
	</script>

	<?php if ($cl["pn"] == "guest" && $cl["auth_type"] == "signup"): ?>
		<?php if ($cl["config"]["google_recaptcha"] == "on"): ?>
			<script src='https://www.google.com/recaptcha/api.js' async defer></script>
		<?php endif; ?>
	<?php endif; ?>

	<script>
		<?php echo(cl_get_custom_code("headerjs")); ?>
	</script>
	<style>
		<?php echo(cl_get_custom_code("headercss")); ?>
	</style>

	<?php echo cl_template('custom/header_tags'); ?>

	<!-- Reels-style Video Player CSS (replaces Plyr) -->
	<style id="reels-style-global">
		.cl-plyr-video video, video.plyr {width:100%;height:100%;object-fit:cover;background:#000;display:block}
		.cl-plyr-video video.aspect-wide, video.plyr.aspect-wide{object-fit:contain}
		.cl-plyr-video video:fullscreen,.cl-plyr-video video:-webkit-full-screen{object-fit:contain;background:#000}
	</style>

	<!-- Aspect Ratio Video Container CSS -->
	<style id="aspect-ratio-video-containers">
		/* Modern aspect-ratio support */
		.cl-plyr-video {
			position: relative;
			width: 100%;
			background: #000;
			border-radius: var(--cl-primary-border-radius, 8px);
			overflow: hidden;
		}

		/* Ensure video fills container properly */
		.cl-plyr-video video {
			width: 100%;
			height: 100%;
			object-fit: contain;
			background: #000;
			display: block;
		}

		/* Fallback for browsers without aspect-ratio support */
		@supports not (aspect-ratio: 16/9) {
			.cl-plyr-video,
			.shorts-slide > div[style*="aspect-ratio"] {
				position: relative;
				height: 0;
				padding-bottom: 56.25%; /* Default 16:9 */
			}

			.cl-plyr-video[style*="aspect-ratio: 16:9"],
			.shorts-slide > div[style*="aspect-ratio: 16:9"] {
				padding-bottom: 56.25%; /* 16:9 */
			}

			.cl-plyr-video[style*="aspect-ratio: 9:16"],
			.shorts-slide > div[style*="aspect-ratio: 9:16"] {
				padding-bottom: 177.78%; /* 9:16 */
			}

			.cl-plyr-video[style*="aspect-ratio: 4:3"],
			.shorts-slide > div[style*="aspect-ratio: 4:3"] {
				padding-bottom: 75%; /* 4:3 */
			}

			.cl-plyr-video[style*="aspect-ratio: 1:1"],
			.shorts-slide > div[style*="aspect-ratio: 1:1"] {
				padding-bottom: 100%; /* 1:1 */
			}

			.cl-plyr-video[style*="aspect-ratio: 3:4"],
			.shorts-slide > div[style*="aspect-ratio: 3:4"] {
				padding-bottom: 133.33%; /* 3:4 */
			}
		}

		/* Container classes for different orientations */
		.publication-video.video-container-vertical {
			max-width: 400px; /* Limit width for vertical videos */
			margin: 0 auto; /* Center vertical videos */
		}

		.publication-video.video-container-horizontal {
			width: 100%;
		}

		/* Responsive adjustments */
		@media (max-width: 768px) {
			.publication-video.video-container-vertical {
				max-width: 100%;
			}
		}

		/* Simple background for loading state */
		.cl-plyr-video {
			background: #000;
		}
	</style>

	<!-- Shorts / Reels overlay styles -->
	<style id="shorts-style">
		#shortsViewer{position:fixed;inset:0;background:#000;z-index:9999;display:none;overflow-y:auto;scroll-snap-type:y mandatory;scroll-snap-stop:always;-webkit-overflow-scrolling:touch;scroll-behavior:smooth;transition:opacity 0.3s ease;overscroll-behavior:contain}
		#shortsViewer.loading{opacity:0.8}
		/* Hide descriptions during loading to prevent center positioning flash */
		#shortsViewer.loading .shorts-description-box{opacity:0;visibility:hidden;transition:opacity 0.2s ease}
		/* Hide all descriptions by default until positioned */
		#shortsViewer .shorts-description-box{opacity:0;visibility:hidden;transition:opacity 0.2s ease}
		/* Show descriptions only when they have been positioned */
		#shortsViewer .shorts-description-box.positioned{opacity:1;visibility:visible}
		#shortsViewer .shorts-slide{height:100vh;scroll-snap-align:start;scroll-snap-stop:always;display:flex;align-items:center;justify-content:center;position:relative}

		/* Desktop: Keep original working behavior */
		#shortsViewer video{max-width:100%;max-height:100vh;width:auto;height:auto;object-fit:contain;background:#000;position:relative;z-index:1;cursor:pointer}
		#shortsViewer video:not([poster=""]):not([poster]):not(.show-poster){background:#000 !important}
		#shortsViewer video.buffering{background:#000 !important}

		/* Mobile: Proper aspect ratio handling */
		@media(max-width:768px){
			/* Tall/Portrait videos (9:16, 3:4, etc.) - Fill full screen with no black bars */
			#shortsViewer video.aspect-tall{
				width:100vw !important;
				height:100vh !important;
				object-fit:cover !important;
				max-width:none !important;
				max-height:none !important;
			}

			/* Square videos (1:1) - Full width, maintain aspect ratio */
			#shortsViewer video.aspect-square{
				width:100vw !important;
				height:auto !important;
				max-height:100vh !important;
				object-fit:contain !important;
				max-width:none !important;
			}

			/* Wide/Landscape videos (16:9, 4:3, etc.) - Full width, maintain aspect ratio */
			#shortsViewer video.aspect-wide{
				width:100vw !important;
				height:auto !important;
				max-height:100vh !important;
				object-fit:contain !important;
				max-width:none !important;
			}

			/* Fallback: Videos without aspect classes default to tall behavior */
			#shortsViewer video:not(.aspect-tall):not(.aspect-square):not(.aspect-wide){
				width:100vw !important;
				height:100vh !important;
				object-fit:cover !important;
				max-width:none !important;
				max-height:none !important;
			}
		}

		#shortsCloseBtn{position:fixed;top:15px;right:15px;z-index:10000;background:rgba(0,0,0,.6);color:#fff;border:none;width:40px;height:40px;border-radius:50%;font-size:26px;line-height:40px;text-align:center}

		/* Pause indicator styles */
		.pause-indicator {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			width: 80px;
			height: 80px;
			background: rgba(0, 0, 0, 0.7);
			border-radius: 50%;
			display: none;
			align-items: center;
			justify-content: center;
			z-index: 1000;
			cursor: pointer;
			backdrop-filter: blur(10px);
			transition: all 0.2s ease;
		}

		.pause-indicator:hover {
			background: rgba(0, 0, 0, 0.8);
			transform: translate(-50%, -50%) scale(1.1);
		}

		.pause-indicator::before {
			content: '';
			width: 0;
			height: 0;
			border-left: 20px solid #fff;
			border-top: 12px solid transparent;
			border-bottom: 12px solid transparent;
			margin-left: 4px;
		}



		/* Mobile responsiveness - ONLY for shorts viewer, NOT timeline */
		/* Cover all mobile devices including iPhone 12, 13, 14, Android, etc. */
		@media screen and (max-width: 768px),
		       screen and (max-device-width: 768px),
		       screen and (orientation: portrait) and (max-width: 1024px) {

			/* Force shorts viewer to actual screen size */
			#shortsViewer {
				position: fixed !important;
				top: 0 !important;
				left: 0 !important;
				right: 0 !important;
				bottom: 0 !important;
				width: 100vw !important;
				height: 100vh !important;
				min-height: 100vh !important;
				z-index: 9999 !important;
			}

			/* Force shorts slides to actual screen size */
			#shortsViewer .shorts-slide {
				position: relative !important;
				width: 100vw !important;
				height: 100vh !important;
				min-height: 100vh !important;
				display: flex !important;
				align-items: center !important;
				justify-content: center !important;
			}

			/* Default for ALL videos - responsive sizing */
			#shortsViewer video {
				width: 100vw !important;
				max-width: 100vw !important;
				height: auto !important;
				max-height: 100vh !important;
				object-fit: contain !important; /* Safe default - shows full video */
				z-index: 1 !important; /* Keep video behind UI elements */
			}



			/* Ensure UI elements stay on top of videos on mobile */
			#shortsViewer .shorts-publisher {
				z-index: 10 !important;
				bottom: 12% !important;
				left: 15px !important;
				max-width: calc(100% - 85px) !important;
			}
			#shortsViewer .shorts-publisher.description-expanded {
				bottom: 18% !important;
			}

			#shortsViewer .shorts-actions {
				z-index: 10 !important;
			}

			#shortsCloseBtn {
				z-index: 10000 !important;
			}

			/* Shorts slide container - full screen */
			#shortsViewer .shorts-slide {
				display: flex !important;
				align-items: center !important;
				justify-content: center !important;
				padding: 0 !important;
				margin: 0 !important;
				width: 100vw !important;
				height: 100vh !important;
				min-height: 100vh !important;
				overflow: hidden !important;
			}

			/* Ensure shorts viewer itself is full screen */
			#shortsViewer {
				width: 100vw !important;
				height: 100vh !important;
				position: fixed !important;
				top: 0 !important;
				left: 0 !important;
				right: 0 !important;
				bottom: 0 !important;
			}

			/* Timeline videos remain unchanged on mobile */
			/* No changes to .publication-video, .cl-plyr-video, or timeline containers */
		}

		/* Timeline heart styling - full red when liked, proper outline when unliked */
		/* Target only timeline hearts (not shorts viewer) with maximum specificity */
		body main.main-content-container div.main-content-container-inner div.timeline-container-wrapper div.timeline-container-inner div.timeline-container div.timeline-posts-container div.timeline-posts-ls div.post-list-item div.post-list-item__content div.post-data div.post-data__content div.post-data__content-inner div.post-data-layout div.post-data-layout__controls button.ctrls-item.liked[onclick*="like_post"] span.ctrls-item__icon svg path[d*="9.1371"],
		body main.main-content-container div.main-content-container-inner div.timeline-container-wrapper div.timeline-container-inner div.timeline-container div.timeline-posts-container div.timeline-posts-ls div.post-list-item div.post-list-item__content div.post-data div.post-data__content div.post-data__content-inner div.post-data-layout div.post-data-layout__controls button.ctrls-item.liked[onclick*="like_post"] div.ctrls-item__icon svg path[d*="9.1371"] {
			fill: #ff3355 !important;
			stroke: #ff3355 !important;
			stroke-width: 0 !important;
		}

		/* Timeline hearts when unliked - proper outline in light mode */
		body[data-bg="default"] main.main-content-container div.main-content-container-inner div.timeline-container-wrapper div.timeline-container-inner div.timeline-container div.timeline-posts-container div.timeline-posts-ls div.post-list-item div.post-list-item__content div.post-data div.post-data__content div.post-data__content-inner div.post-data-layout div.post-data-layout__controls button.ctrls-item:not(.liked)[onclick*="like_post"] span.ctrls-item__icon svg path[d*="9.1371"],
		body[data-bg="default"] main.main-content-container div.main-content-container-inner div.timeline-container-wrapper div.timeline-container-inner div.timeline-container div.timeline-posts-container div.timeline-posts-ls div.post-list-item div.post-list-item__content div.post-data div.post-data__content div.post-data__content-inner div.post-data-layout div.post-data-layout__controls button.ctrls-item:not(.liked)[onclick*="like_post"] div.ctrls-item__icon svg path[d*="9.1371"] {
			fill: none !important;
			stroke: #000000 !important;
			stroke-width: 1.5 !important;
		}

		/* Timeline hearts when unliked - proper outline in dark mode */
		body[data-bg="dark"] main.main-content-container div.main-content-container-inner div.timeline-container-wrapper div.timeline-container-inner div.timeline-container div.timeline-posts-container div.timeline-posts-ls div.post-list-item div.post-list-item__content div.post-data div.post-data__content div.post-data__content-inner div.post-data-layout div.post-data-layout__controls button.ctrls-item:not(.liked)[onclick*="like_post"] span.ctrls-item__icon svg path[d*="9.1371"],
		body[data-bg="dark"] main.main-content-container div.main-content-container-inner div.timeline-container-wrapper div.timeline-container-inner div.timeline-container div.timeline-posts-container div.timeline-posts-ls div.post-list-item div.post-list-item__content div.post-data div.post-data__content div.post-data__content-inner div.post-data-layout div.post-data-layout__controls button.ctrls-item:not(.liked)[onclick*="like_post"] div.ctrls-item__icon svg path[d*="9.1371"] {
			fill: none !important;
			stroke: #ffffff !important;
			stroke-width: 1.5 !important;
		}

		/* Publisher styles - Premium & Modern */
		.shorts-publisher {
			position: absolute !important;
			left: 20px !important;
			bottom: 60px !important;
			top: auto !important;
			transform: none !important;
			display: flex !important;
			align-items: center !important;
			gap: 12px !important;
			z-index: 10 !important;
			color: #fff !important;
			background: linear-gradient(135deg, rgba(0,0,0,0.25), rgba(0,0,0,0.15)) !important;
			backdrop-filter: blur(20px) saturate(1.2) !important;
			border-radius: 25px !important;
			padding: 10px 14px !important;
			box-shadow: 0 8px 32px rgba(0,0,0,0.15), 0 2px 8px rgba(0,0,0,0.1) !important;
			border: 1px solid rgba(255,255,255,0.15) !important;
			transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
			max-width: calc(100% - 100px) !important;
			width: fit-content !important;
			/* Prevent flex centering from affecting publisher positioning */
			align-self: flex-end !important;
			justify-self: flex-start !important;
		}
		/* Instagram-style: Publisher moves up when description expands */
		.shorts-publisher.description-expanded {
			bottom: 14%;
			transform: none;
		}
		/* When no description, move publisher down for better spacing */
		.shorts-slide:not(.has-description) .shorts-publisher {
			bottom: 80px !important;
		}
		/* Remove dropdown arrow from three dots button */
		.shorts-actions .dropdown-toggle::after {
			display: none !important;
		}
		.shorts-actions .dropdown-toggle::before {
			display: none !important;
		}
		/* Style close button with left arrow */
		#shortsCloseBtn {
			position: fixed !important;
			top: 15px !important;
			left: 15px !important;
			z-index: 10000 !important;
			background: transparent !important;
			color: #fff !important;
			border: none !important;
			width: 40px !important;
			height: 40px !important;
			display: flex !important;
			align-items: center !important;
			justify-content: center !important;
			cursor: pointer !important;
			transition: all 0.2s ease !important;
		}
		#shortsCloseBtn:hover {
			background: rgba(255, 255, 255, 0.1) !important;
			transform: scale(1.1) !important;
		}
		#shortsCloseBtn svg {
			width: 24px !important;
			height: 24px !important;
		}
		.shorts-publisher:hover {
			transform: translateY(-2px);
			box-shadow: 0 12px 40px rgba(0,0,0,0.2), 0 4px 16px rgba(0,0,0,0.15);
			background: linear-gradient(135deg, rgba(0,0,0,0.35), rgba(0,0,0,0.25));
			backdrop-filter: blur(25px) saturate(1.3);
		}
		.shorts-publisher .avatar-holder {
			width: 40px;
			height: 40px;
			border-radius: 50%;
			overflow: hidden;
			flex-shrink: 0;
			position: relative;
			transition: all 0.3s ease;
		}
		.shorts-publisher .avatar-holder::before {
			content: "";
			position: absolute;
			inset: -3px;
			background: linear-gradient(45deg, rgba(255,255,255,0.4), rgba(255,255,255,0.2));
			border-radius: 50%;
			z-index: -1;
			transition: all 0.3s ease;
		}
		.shorts-publisher .avatar-holder:hover::before {
			background: linear-gradient(45deg, rgba(255,255,255,0.6), rgba(255,255,255,0.3));
			transform: scale(1.1);
		}
		.shorts-publisher .avatar-holder img {
			width: 100%;
			height: 100%;
			object-fit: cover;
			border: 2px solid rgba(255,255,255,0.4);
			border-radius: 50%;
			transition: all 0.3s ease;
		}
		.shorts-publisher .avatar-holder:hover img {
			border-color: rgba(255,255,255,0.7);
			transform: scale(1.05);
		}
		.shorts-publisher .username {
			color: #fff;
			font-weight: 700;
			text-decoration: none;
			font-size: 15px;
			flex: 1;
			min-width: 0;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			text-shadow: 0 2px 12px rgba(0,0,0,0.8), 0 1px 4px rgba(0,0,0,0.6);
			transition: all 0.2s ease;
			letter-spacing: 0.3px;
		}
		.shorts-publisher .username:hover {
			text-decoration: none;
			color: #fff;
			transform: translateX(2px);
			text-shadow: 0 2px 16px rgba(0,0,0,0.9), 0 1px 6px rgba(0,0,0,0.7);
		}
		.shorts-publisher .user-name-holder {
			display: inline-flex !important;
			align-items: center;
			gap: 4px;
		}
		.shorts-publisher .user-name-holder__name {
			color: inherit;
			font-weight: inherit;
			font-size: inherit;
		}
		.shorts-publisher .user-name-holder__badge {
			width: 16px;
			height: 16px;
			line-height: 0;
			margin-left: 2px;
			flex-shrink: 0;
		}
		.shorts-publisher .user-name-holder__badge svg {
			width: 100%;
			height: 100%;
			fill: var(--cl-primary-color, #2494f4);
		}
		.shorts-description-box {
			position: absolute !important;
			left: 20px !important;
			bottom: 0 !important;
			right: 80px !important;
			z-index: 25 !important;
			max-width: calc(100% - 100px) !important;
			transition: none !important;
			max-height: none !important;
			overflow: visible !important;
			background: transparent !important;
			padding: 10px 0 !important;
			border-radius: 0 !important;
			/* Ensure descriptions start at bottom, not center - override flex centering */
			top: auto !important;
			transform: none !important;
			/* Prevent flex centering from affecting description positioning */
			align-self: flex-end !important;
			justify-self: flex-start !important;
		}
		/* Description stays at bottom */
		.shorts-description-box.expanded {
			bottom: 0;
			max-height: none;
			overflow-y: visible;
			background: transparent;
		}
		.shorts-description {
			font-size: 15px !important; /* Override inline font-size - increased for better readability */
			line-height: 1.4 !important; /* Override inline line-height */
			color: #fff;
			word-wrap: break-word;
			overflow-wrap: break-word;
			margin: 0;
			padding: 0 !important; /* Override inline padding */
			background: transparent;
			border: none;
			box-shadow: none;
			text-shadow: 0 1px 2px rgba(0,0,0,0.8);
			transition: none;
			border-radius: 0;
			backdrop-filter: none;
			position: relative !important;
			z-index: 20;
			transform: none !important;
			top: 0px !important;
			margin-top: 0px !important;
			display: block !important;
			visibility: visible !important;
			opacity: 1 !important;
			width: 100% !important;
			max-height: 45px !important; /* Increased to accommodate larger font size */
			overflow: hidden !important; /* Force hidden overflow initially */
		}
		/* Instagram-style truncation - force collapsed state */
		.shorts-description.collapsed {
			display: -webkit-box !important;
			-webkit-line-clamp: 2 !important;
			-webkit-box-orient: vertical !important;
			overflow: hidden !important;
			max-height: 45px !important; /* Increased to accommodate larger font */
			font-size: 15px !important; /* Increased from 13px for better readability */
			line-height: 1.4 !important; /* Override any inline line-height */
			padding: 0 !important; /* Override any inline padding */
		}
		.shorts-description.expanded {
			display: block;
			max-height: none; /* Let container handle height limits */
			overflow: visible; /* Text flows naturally within container */
		}
		/* Specific styling for expanded publication text descriptions */
		.shorts-description.publication-text.expanded {
			display: block;
			max-height: none;
			overflow: visible;
			transform: none;
			transition: transform 0.3s ease;
		}
		/* Specific styling for collapsed publication text descriptions */
		.shorts-description.publication-text.collapsed {
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
			overflow: hidden;
			max-height: 40px;
			transform: none;
			transition: transform 0.3s ease;
		}
		.shorts-description .readmore-toggle {
			color: rgba(255,255,255,0.8);
			font-weight: 600;
			cursor: pointer;
			text-decoration: none;
			margin-left: 4px;
			transition: all 0.2s ease;
			display: inline-block;
			text-shadow: 0 1px 3px rgba(0,0,0,0.8);
			font-size: 15px; /* Match the description font size */
		}
		.shorts-description .readmore-toggle:hover {
			color: #fff;
			text-decoration: none;
			transform: scale(1.05);
		}
		/* Instagram-style dropdown icon */
		.instagram-more-btn {
			color: #ffffff !important;
			font-weight: 900 !important;
			cursor: pointer !important;
			text-decoration: none !important;
			margin-left: 8px !important;
			font-size: 18px !important;
			line-height: 1 !important;
			text-shadow: 0 2px 4px rgba(0,0,0,0.9) !important;
			transition: all 0.2s ease !important;
			padding: 6px !important;
			border-radius: 50% !important;
			background: rgba(0,0,0,0.7) !important;
			border: 2px solid rgba(255,255,255,0.4) !important;
			display: inline-flex !important;
			align-items: center !important;
			justify-content: center !important;
			width: 28px !important;
			height: 28px !important;
			user-select: none !important;
			-webkit-user-select: none !important;
			-moz-user-select: none !important;
			-ms-user-select: none !important;
			position: relative !important;
			z-index: 1000 !important;
			vertical-align: middle !important;
		}
		.instagram-more-btn:hover {
			color: #fff !important;
			background: rgba(0,0,0,0.9) !important;
			border-color: rgba(255,255,255,0.7) !important;
			transform: scale(1.15) !important;
			box-shadow: 0 4px 12px rgba(0,0,0,0.5) !important;
		}
		.instagram-more-btn:active {
			transform: scale(0.9) !important;
			background: rgba(255,255,255,0.2) !important;
		}

		/* Mobile responsive adjustments - Override with consistent positioning */
		@media (max-width: 768px) {
			.shorts-description-box {
				left: 20px !important;
				right: 80px !important;
				bottom: auto !important;
				max-width: calc(100vw - 100px) !important;
				width: auto !important;
				max-height: none;
				z-index: 25 !important;
				padding: 0 !important;
				background: transparent !important;
				word-wrap: break-word !important;
				overflow-wrap: break-word !important;
				box-sizing: border-box !important;
			}
			.shorts-description {
				font-size: 16px !important; /* Slightly larger on mobile for better readability */
				padding: 0 !important;
				line-height: 1.4 !important;
				background: transparent !important;
				text-shadow: 0 1px 2px rgba(0,0,0,0.8) !important;
				z-index: 30 !important;
			}
			.shorts-description.collapsed {
				max-height: 200px;
				-webkit-line-clamp: 2;
			}
			.shorts-publisher.description-expanded {
				bottom: 15%;
				transform: none;
			}
			.shorts-description-box.expanded {
				max-height: none;
				bottom: 0;
				background: transparent !important;
			}
			.instagram-more-btn {
				font-size: 16px !important;
				width: 24px !important;
				height: 24px !important;
				margin-left: 6px !important;
			}

			/* Mobile-specific positioning - use consistent bottom positioning */
			.shorts-description.publication-text.expanded,
			.shorts-description.publication-text.collapsed {
				transform: none !important;
				position: relative !important;
				top: 0 !important;
				margin-top: 0 !important;
			}

			/* All small screens use consistent positioning */
			@media (max-width: 480px) and (max-height: 667px) {
				.shorts-description-box {
					bottom: 55px !important;
				}
				.shorts-description.publication-text.expanded,
				.shorts-description.publication-text.collapsed {
					transform: none !important;
					position: relative !important;
					top: 0 !important;
					margin-top: 0 !important;
				}
			}

			/* iPhone SE and very small screens - same consistent positioning */
			@media (max-width: 375px) and (max-height: 667px) {
				.shorts-description-box {
					bottom: 55px !important;
				}
				.shorts-description.publication-text.expanded,
				.shorts-description.publication-text.collapsed {
					transform: none !important;
					position: relative !important;
					top: 0 !important;
					margin-top: 0 !important;
				}
			}



			/* Universal positioning - position description box with top positioning */
			.shorts-slide .shorts-description-box {
				position: absolute !important;
				left: 20px !important;
				right: 80px !important;
				bottom: auto !important;
				top: auto !important;
				z-index: 25 !important;
				max-width: calc(100% - 100px) !important;
				background: transparent !important;
				padding: 0 !important;
			}

			/* Position description text within the box - COLLAPSED ONLY */
			.shorts-slide .shorts-description.publication-text.collapsed {
				position: relative !important;
				top: 0 !important;
				transform: none !important;
				margin-top: 10px !important;
				display: block !important;
				visibility: visible !important;
				opacity: 1 !important;
				width: 100% !important;
				box-sizing: border-box !important;
				font-size: 16px !important; /* Increased for mobile readability */
				line-height: 1.4 !important;
			}

			/* Position description text within the box - EXPANDED ONLY */
			.shorts-slide .shorts-description.publication-text.expanded {
				position: relative !important;
				top: 0 !important;
				transform: none !important;
				margin-top: 10px !important;
				display: block !important;
				visibility: visible !important;
				opacity: 1 !important;
				width: 100% !important;
				box-sizing: border-box !important;
				font-size: 16px !important; /* Same size for consistency */
				line-height: 1.4 !important;
				max-height: none !important; /* Let container handle height limits */
				overflow: visible !important; /* Text flows naturally within container */
			}

			/* Ensure expanded descriptions are visible */
			.shorts-slide .shorts-description.publication-text.expanded {
				max-height: none !important;
				overflow: visible !important;
			}

			/* Ensure collapsed descriptions have enough space for more button */
			.shorts-slide .shorts-description.publication-text.collapsed {
				min-height: 30px !important; /* Increased for larger font */
				max-height: 55px !important; /* Increased to accommodate larger font size */
				overflow: visible !important;
				line-height: 1.4 !important;
				vertical-align: top !important;
				white-space: normal !important;
				word-wrap: break-word !important;
				overflow-wrap: break-word !important;
				text-overflow: ellipsis !important;
				display: block !important;
				width: 100% !important;
				box-sizing: border-box !important;
			}

			/* Make expanded descriptions grow downward with scrolling */
			.shorts-slide .shorts-description.publication-text.expanded {
				line-height: 1.4 !important;
				max-height: none !important;
				overflow-y: auto !important;
				vertical-align: top !important;
				transition: max-height 0.3s ease !important;
			}

			/* Ensure description box grows downward */
			.shorts-slide .shorts-description-box {
				display: flex !important;
				flex-direction: column !important;
				justify-content: flex-start !important;
				align-items: flex-start !important;
				transition: top 0.3s ease, max-height 0.3s ease !important;
			}

			/* Publisher smooth transitions when pushed up */
			.shorts-slide .shorts-publisher {
				transition: bottom 0.3s ease !important;
			}
		}
		.shorts-publisher .btn-follow {
			background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,255,255,0.85));
			color: #000;
			border: none;
			padding: 6px 16px;
			border-radius: 20px;
			font-size: 12px;
			font-weight: 700;
			cursor: pointer;
			transition: all 0.3s ease;
			flex-shrink: 0;
			text-transform: uppercase;
			letter-spacing: 0.3px;
			box-shadow: 0 3px 12px rgba(255,255,255,0.2);
			position: relative;
			overflow: hidden;
		}
		.shorts-publisher .btn-follow::before {
			content: "";
			position: absolute;
			top: 50%;
			left: 50%;
			width: 0;
			height: 0;
			background: rgba(0,0,0,0.1);
			border-radius: 50%;
			transition: all 0.3s ease;
			transform: translate(-50%,-50%);
		}
		.shorts-publisher .btn-follow:hover {
			background: linear-gradient(135deg, #fff, rgba(255,255,255,0.9));
			transform: translateY(-1px) scale(1.05);
			box-shadow: 0 6px 20px rgba(255,255,255,0.3);
		}
		.shorts-publisher .btn-follow:hover::before {
			width: 100px;
			height: 100px;
		}
		.shorts-publisher .btn-follow.followed {
			background: linear-gradient(135deg, rgba(255,255,255,0.15), rgba(255,255,255,0.05));
			color: #fff;
			border: 2px solid rgba(255,255,255,0.3);
			box-shadow: 0 4px 15px rgba(0,0,0,0.2);
			cursor: default;
		}
		.shorts-publisher .btn-follow.followed:hover {
			background: linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.15));
			border-color: rgba(255,255,255,0.5);
			box-shadow: 0 6px 20px rgba(0,0,0,0.3);
			transform: none;
		}

		/* Spinner & overlays */
		.loading-spinner{position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);width:40px;height:40px;border:4px solid rgba(255,255,255,.4);border-top-color:#fff;border-radius:50%;animation:spin .8s linear infinite;pointer-events:none}
		@keyframes spin{to{transform:translate(-50%,-50%) rotate(360deg);}}
		#volumeToast{position:fixed;left:50%;top:50%;transform:translate(-50%,-50%);z-index:10001;font-size:64px;color:#fff;opacity:0;transition:opacity .3s;pointer-events:none}
		.shorts-actions{position:absolute;right:8px;bottom:5%;top:auto;transform:none;display:flex;flex-direction:column;align-items:center;gap:8px;z-index:10}
		.shorts-actions .post-data-layout__controls{display:flex!important;flex-direction:column!important;align-items:center!important;justify-content:center!important;gap:6px!important;width:auto!important;padding:0!important}
		.shorts-actions .post-data-layout__controls>*{margin:0!important}
		.shorts-actions .ctrls-item{background:none!important;border:none!important;padding:8px!important;margin:0!important;width:auto!important;height:auto!important;flex:none!important;display:flex!important;flex-direction:column!important;align-items:center!important;border-radius:50%!important;transition:all 0.15s ease!important;cursor:pointer!important}
		.shorts-actions .ctrls-item:hover{background:rgba(255,255,255,0.1)!important;transform:scale(1.1)!important}
		.shorts-actions .ctrls-item a{padding:0!important;margin:0!important;display:flex!important;flex-direction:column!important;align-items:center!important;text-decoration:none!important}
		.shorts-actions .ctrls-item__icon{width:52px!important;height:52px!important;display:flex!important;align-items:center!important;justify-content:center!important;background:rgba(0,0,0,0.4)!important;border-radius:50%!important;margin-bottom:2px!important;backdrop-filter:blur(10px)!important;border:2px solid rgba(255,255,255,0.2)!important;transition:all 0.2s ease!important}
		.shorts-actions .ctrls-item:hover .ctrls-item__icon{background:rgba(0,0,0,0.6)!important;border-color:rgba(255,255,255,0.4)!important;transform:scale(1.05)!important}
		.shorts-actions .ctrls-item__icon svg{width:28px!important;height:28px!important}
		.shorts-actions .ctrls-item .ctrls-item__icon{font-size:28px!important;line-height:1!important;color:#fff!important}
		.shorts-actions .ctrls-item .num{font-size:14px!important;line-height:1!important;color:#fff!important;font-weight:600!important;text-shadow:0 1px 2px rgba(0,0,0,0.5)!important;margin-top:2px!important}
		.shorts-actions .ctrls-item.liked .ctrls-item__icon{background:rgba(255,51,85,0.2)!important;border-color:rgba(255,51,85,0.4)!important}
		.shorts-actions .ctrls-item.liked .ctrls-item__icon svg{color:#ff3355!important}
		.shorts-actions .ctrls-item.liked:hover .ctrls-item__icon{background:rgba(255,51,85,0.3)!important;border-color:rgba(255,51,85,0.6)!important}
		.shorts-actions .dropdown{display:block!important;margin-bottom:6px!important}
		.shorts-actions .dropdown .dropdown-toggle{background:none!important;border:none!important;padding:8px!important;margin:0!important;width:auto!important;height:auto!important;flex:none!important;display:flex!important;flex-direction:column!important;align-items:center!important;border-radius:50%!important;transition:all 0.2s ease!important;cursor:pointer!important}
		.shorts-actions .dropdown .dropdown-toggle:hover{background:rgba(255,255,255,0.1)!important;transform:scale(1.1)!important}
		.shorts-actions .dropdown .dropdown-toggle .ctrls-item__icon{width:52px!important;height:52px!important;display:flex!important;align-items:center!important;justify-content:center!important;background:rgba(0,0,0,0.4)!important;border-radius:50%!important;backdrop-filter:blur(10px)!important;border:2px solid rgba(255,255,255,0.2)!important;transition:all 0.2s ease!important}
		.shorts-actions .dropdown .dropdown-toggle:hover .ctrls-item__icon{background:rgba(0,0,0,0.6)!important;border-color:rgba(255,255,255,0.4)!important;transform:scale(1.05)!important}
		.shorts-actions .dropdown .dropdown-toggle .ctrls-item__icon svg{width:28px!important;height:28px!important;color:#fff!important}
		/* Mobile responsive adjustments for shorts actions */
		@media (max-width: 768px) {
			.shorts-actions{right:4px!important;bottom:40px!important;gap:8px!important}
			.shorts-actions .post-data-layout__controls{gap:6px!important}
			.shorts-actions .ctrls-item__icon{width:50px!important;height:50px!important}
			.shorts-actions .ctrls-item__icon svg{width:32px!important;height:32px!important;stroke-width:2.5px!important}
			.shorts-actions .dropdown .dropdown-toggle .ctrls-item__icon{width:50px!important;height:50px!important}
			.shorts-actions .dropdown .dropdown-toggle .ctrls-item__icon svg{width:32px!important;height:32px!important;stroke-width:2.5px!important}
			.shorts-actions .dropdown{margin-bottom:6px!important}
			.shorts-actions .ctrls-item .num{font-size:13px!important}
		}
		@media (max-width: 480px) {
			.shorts-actions{right:3px!important;bottom:35px!important;gap:10px!important}
			.shorts-actions .post-data-layout__controls{gap:8px!important}
			.shorts-actions .ctrls-item__icon{width:48px!important;height:48px!important}
			.shorts-actions .ctrls-item__icon svg{width:30px!important;height:30px!important;stroke-width:2.8px!important}
			.shorts-actions .ctrls-item .num{font-size:13px!important;font-weight:700!important}
			.shorts-actions .dropdown .dropdown-toggle .ctrls-item__icon{width:48px!important;height:48px!important}
			.shorts-actions .dropdown .dropdown-toggle .ctrls-item__icon svg{width:26px!important;height:26px!important}
			.shorts-actions .dropdown{margin-bottom:8px!important}
		}
		/* Fast like button transitions for all contexts */
		.ctrls-item{transition:transform 0.15s ease!important}
		.ctrls-item .num{transition:all 0.15s ease!important}

		/* Native Share Sheet Styles */
		.native-share-sheet {
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			z-index: 10000;
			display: flex;
			align-items: flex-end;
			opacity: 0;
			visibility: hidden;
			transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
		}

		.native-share-sheet.active {
			opacity: 1;
			visibility: visible;
		}

		.native-share-backdrop {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(0, 0, 0, 0.5);
			backdrop-filter: blur(10px);
		}

		.native-share-container {
			position: relative;
			width: 100%;
			max-width: 500px;
			margin: 0 auto;
			background: var(--bg-color, #fff);
			border-radius: 20px 20px 0 0;
			padding: 0;
			transform: translateY(100%);
			transition: transform 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
			max-height: 80vh;
			overflow-y: auto;
		}

		.native-share-sheet.active .native-share-container {
			transform: translateY(0);
		}

		.native-share-handle {
			width: 40px;
			height: 4px;
			background: #d1d5db;
			border-radius: 2px;
			margin: 12px auto 0;
			cursor: grab;
			touch-action: none;
		}

		.native-share-handle:active {
			cursor: grabbing;
		}

		.native-share-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 20px 24px 16px;
			border-bottom: 1px solid #f3f4f6;
		}

		.native-share-header h3 {
			margin: 0;
			font-size: 20px;
			font-weight: 600;
			color: var(--text-color, #111827);
		}

		.native-share-close {
			background: #f3f4f6;
			border: none;
			border-radius: 50%;
			width: 32px;
			height: 32px;
			display: flex;
			align-items: center;
			justify-content: center;
			cursor: pointer;
			transition: background 0.2s ease;
		}

		.native-share-close:hover {
			background: #e5e7eb;
		}

		.native-share-close svg {
			color: #6b7280;
		}

		.native-share-quick-actions {
			display: flex;
			gap: 16px;
			padding: 20px 24px;
			border-bottom: 1px solid #f3f4f6;
		}

		.quick-action-item {
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: 8px;
			padding: 16px;
			background: #f9fafb;
			border-radius: 16px;
			cursor: pointer;
			transition: all 0.2s ease;
		}

		.quick-action-item:hover {
			background: #f3f4f6;
			transform: scale(1.02);
		}

		.quick-action-icon {
			width: 48px;
			height: 48px;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #6b7280;
		}

		.quick-action-icon.copy-icon {
			background: #dbeafe;
			color: #2563eb;
		}

		.quick-action-icon.share-icon {
			background: #dcfce7;
			color: #16a34a;
		}

		.quick-action-item span {
			font-size: 14px;
			font-weight: 500;
			color: #374151;
		}

		.native-share-section {
			padding: 20px 24px;
		}

		.native-share-section h4 {
			margin: 0 0 16px 0;
			font-size: 16px;
			font-weight: 600;
			color: #111827;
		}

		.native-share-apps {
			display: grid;
			grid-template-columns: repeat(4, 1fr);
			gap: 16px;
		}

		.share-app-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: 8px;
			padding: 16px 8px;
			border-radius: 12px;
			cursor: pointer;
			transition: all 0.2s ease;
		}

		.share-app-item:hover {
			background: #f9fafb;
			transform: scale(1.05);
		}

		.share-app-icon {
			width: 48px;
			height: 48px;
			border-radius: 12px;
			display: flex;
			align-items: center;
			justify-content: center;
			overflow: hidden;
		}

		.share-app-icon.whatsapp {
			background: #e7f5e7;
		}

		.share-app-icon.facebook {
			background: #e3f2fd;
		}

		.share-app-icon.twitter {
			background: #f5f5f5;
		}

		.share-app-icon.telegram {
			background: #e1f5fe;
		}

		.share-app-icon.instagram {
			background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
		}

		.share-app-icon.linkedin {
			background: #e3f2fd;
		}

		.share-app-icon.reddit {
			background: #fff3e0;
		}

		.share-app-icon.pinterest {
			background: #fce4ec;
		}

		.share-app-item span {
			font-size: 12px;
			font-weight: 500;
			color: #374151;
			text-align: center;
			line-height: 1.2;
		}

		.native-share-link {
			display: flex;
			gap: 12px;
			align-items: center;
			background: #f9fafb;
			border: 1px solid #e5e7eb;
			border-radius: 12px;
			padding: 4px;
		}

		.native-share-link input {
			flex: 1;
			border: none;
			background: transparent;
			padding: 12px 16px;
			font-size: 14px;
			color: #6b7280;
			outline: none;
		}

		.native-share-copy-btn {
			background: #2563eb;
			color: white;
			border: none;
			border-radius: 8px;
			padding: 12px 16px;
			font-size: 14px;
			font-weight: 500;
			cursor: pointer;
			display: flex;
			align-items: center;
			gap: 6px;
			transition: all 0.2s ease;
			white-space: nowrap;
		}

		.native-share-copy-btn:hover {
			background: #1d4ed8;
			transform: scale(1.02);
		}

		/* Mobile responsive styles for native share sheet */
		@media (max-width: 768px) {
			.native-share-container {
				max-height: 85vh;
			}

			.native-share-apps {
				grid-template-columns: repeat(4, 1fr);
				gap: 12px;
			}

			.share-app-item {
				padding: 12px 6px;
			}

			.share-app-icon {
				width: 44px;
				height: 44px;
			}

			.share-app-item span {
				font-size: 11px;
			}

			.native-share-quick-actions {
				padding: 16px 20px;
			}

			.quick-action-item {
				padding: 12px;
			}

			.quick-action-icon {
				width: 44px;
				height: 44px;
			}

			.native-share-section {
				padding: 16px 20px;
			}
		}

		@media (max-width: 480px) {
			.native-share-container {
				max-height: 90vh;
				border-radius: 16px 16px 0 0;
			}

			.native-share-header {
				padding: 16px 20px 12px;
			}

			.native-share-header h3 {
				font-size: 18px;
			}

			.native-share-apps {
				grid-template-columns: repeat(4, 1fr);
				gap: 8px;
			}

			.share-app-item {
				padding: 10px 4px;
			}

			.share-app-icon {
				width: 40px;
				height: 40px;
			}

			.share-app-item span {
				font-size: 10px;
			}

			.quick-action-item {
				padding: 10px;
				gap: 6px;
			}

			.quick-action-icon {
				width: 40px;
				height: 40px;
			}

			.quick-action-item span {
				font-size: 13px;
			}

			.native-share-link {
				flex-direction: column;
				gap: 8px;
			}

			.native-share-link input {
				padding: 10px 12px;
				font-size: 13px;
			}

			.native-share-copy-btn {
				width: 100%;
				justify-content: center;
				padding: 10px 16px;
				font-size: 13px;
			}
		}

		/* Dark Mode Support for Native Share Sheet */
		body[data-bg="dark"] .native-share-container {
			background: var(--cl-primary-bg-color, #000);
			color: var(--cl-primary-text-color, #fff);
		}

		body[data-bg="dark"] .native-share-header {
			border-bottom-color: var(--cl-primary-border-color, #282c30);
		}

		body[data-bg="dark"] .native-share-header h3 {
			color: var(--cl-primary-text-color, #fff);
		}

		body[data-bg="dark"] .native-share-close {
			background: var(--cl-primary-border-color, #282c30);
		}

		body[data-bg="dark"] .native-share-close:hover {
			background: #4b5563;
		}

		body[data-bg="dark"] .native-share-close svg {
			color: var(--cl-primary-text-color, #fff);
		}

		body[data-bg="dark"] .native-share-quick-actions {
			border-bottom-color: var(--cl-primary-border-color, #282c30);
		}

		body[data-bg="dark"] .quick-action-item {
			background: var(--cl-primary-border-color, #282c30);
		}

		body[data-bg="dark"] .quick-action-item:hover {
			background: #4b5563;
		}

		body[data-bg="dark"] .quick-action-item span {
			color: var(--cl-primary-text-color, #fff);
		}

		body[data-bg="dark"] .native-share-section h4 {
			color: var(--cl-primary-text-color, #fff);
		}

		body[data-bg="dark"] .share-app-item:hover {
			background: var(--cl-primary-border-color, #282c30);
		}

		body[data-bg="dark"] .share-app-item span {
			color: var(--cl-primary-text-color, #fff);
		}

		body[data-bg="dark"] .native-share-link {
			background: var(--cl-primary-border-color, #282c30);
			border-color: #4b5563;
		}

		body[data-bg="dark"] .native-share-link input {
			color: var(--cl-primary-text-color, #fff);
		}

		body[data-bg="dark"] .native-share-handle {
			background: #666;
		}

		/* Dropdown items in share sheet */
		.native-share-sheet .dropdown-item {
			display: flex;
			align-items: center;
			padding: 16px 24px;
			text-decoration: none;
			color: var(--text-color, #374151);
			transition: background 0.2s ease;
			border: none;
			background: none;
			width: 100%;
			text-align: left;
		}

		.native-share-sheet .dropdown-item:hover {
			background: var(--hover-bg, #f9fafb);
			color: var(--text-color, #111827);
		}

		.native-share-sheet .dropdown-item .flex-item {
			display: flex;
			align-items: center;
		}

		.native-share-sheet .dropdown-item-icon {
			margin-right: 12px;
			width: 24px;
			height: 24px;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.native-share-sheet .dropdown-item-icon svg {
			width: 20px;
			height: 20px;
			fill: currentColor;
		}

		.native-share-sheet .dropdown-divider {
			height: 1px;
			background: var(--border-color, #e5e7eb);
			margin: 8px 24px;
		}

		/* Dark mode for dropdown items */
		body[data-bg="dark"] .native-share-sheet .dropdown-item {
			color: var(--cl-primary-text-color, #fff);
		}

		body[data-bg="dark"] .native-share-sheet .dropdown-item:hover {
			background: var(--cl-primary-border-color, #282c30);
			color: var(--cl-primary-text-color, #fff);
		}

		body[data-bg="dark"] .native-share-sheet .dropdown-divider {
			background: var(--cl-primary-border-color, #282c30);
		}
		/* Fix dropdown menu z-index in shorts viewer */
		#shortsViewer .dropdown-menu{z-index:10010!important;position:absolute!important}
		#shortsViewer .shorts-actions{overflow:visible!important}
		#shortsViewer .shorts-slide{overflow:visible!important}
		#shortsViewer .dropdown{position:relative!important}
		/* Lower publisher and actions z-index to ensure dropdown appears above them */
		#shortsViewer .shorts-publisher{z-index:5!important}
		#shortsViewer .shorts-actions{z-index:8!important}

		/* Shorts Modal System */
		.shorts-modal-container{position:fixed;inset:0;z-index:10020;display:none;align-items:center;justify-content:center;padding:20px}
		.shorts-modal-backdrop{position:absolute;inset:0;background:rgba(0,0,0,0.8);backdrop-filter:blur(4px)}
		.shorts-modal-content{position:relative;background:var(--cl-primary-bg-color);border-radius:12px;max-width:90vw;max-height:80vh;overflow:hidden;box-shadow:var(--cl-primary-box-shadow);animation:shortsModalSlideIn 0.3s ease-out;color:var(--cl-primary-text-color)}
		.shorts-modal-content.closing{animation:shortsModalSlideOut 0.2s ease-in}

		@keyframes shortsModalSlideIn{from{opacity:0;transform:scale(0.9) translateY(20px)}to{opacity:1;transform:scale(1) translateY(0)}}
		@keyframes shortsModalSlideOut{from{opacity:1;transform:scale(1) translateY(0)}to{opacity:0;transform:scale(0.9) translateY(20px)}}

		/* Modal Header */
		.shorts-modal-header{display:flex;align-items:center;justify-content:space-between;padding:16px 20px;border-bottom:1px solid var(--cl-primary-border-color);background:var(--cl-primary-bg-color)}
		.shorts-modal-title{font-size:18px;font-weight:600;color:var(--cl-primary-text-color);margin:0}
		.shorts-modal-close{background:none;border:none;font-size:24px;color:var(--cl-secondary-text-color);cursor:pointer;padding:4px;border-radius:50%;transition:background 0.2s}
		.shorts-modal-close:hover{background:var(--cl-secondary-bg-color)}

		/* Modal Body */
		.shorts-modal-body{padding:20px;max-height:60vh;overflow-y:auto;background:var(--cl-primary-bg-color);color:var(--cl-primary-text-color)}
		.shorts-modal-body::-webkit-scrollbar{width:6px}
		.shorts-modal-body::-webkit-scrollbar-track{background:#f1f1f1;border-radius:3px}
		.shorts-modal-body::-webkit-scrollbar-thumb{background:#ccc;border-radius:3px}
		.shorts-modal-body::-webkit-scrollbar-thumb:hover{background:#999}

		/* Share Modal Specific Styles */
		.shorts-modal-body .social-media-links .row{display:flex;flex-wrap:wrap;margin:0 -5px}
		.shorts-modal-body .social-media-links .col-3{flex:0 0 50%;padding:0 5px;margin-bottom:10px}
		.shorts-modal-body .social-media-links .link-item{display:flex;align-items:center;justify-content:center;height:40px;background:#f8f9fa;border-radius:20px;text-decoration:none;color:#333;font-size:14px;transition:background 0.2s;border:1px solid #e9ecef}
		.shorts-modal-body .social-media-links .link-item:hover{background:#e9ecef;text-decoration:none;color:#333}
		.shorts-modal-body .social-media-links .link-item svg{margin-right:6px;flex-shrink:0}

		/* Likes Modal Specific Styles */
		.shorts-modal-body .post-likes{padding:0}
		.shorts-modal-body .timeline-users-container{background:transparent}
		.shorts-modal-body .timeline-user-ls{padding:0;margin:0}
		.shorts-modal-body .timeline-user-ls .timeline-user-item{display:flex;align-items:center;padding:12px 0;border-bottom:1px solid var(--cl-primary-border-color);margin:0;background:transparent}
		.shorts-modal-body .timeline-user-ls .timeline-user-item:last-child{border-bottom:none}
		.shorts-modal-body .timeline-user-ls .timeline-user-item .user-data{display:flex;align-items:center;flex:1;text-decoration:none;color:inherit}
		.shorts-modal-body .timeline-user-ls .timeline-user-item .user-data:hover{text-decoration:none;background:var(--cl-secondary-bg-color);border-radius:8px;margin:0 -8px;padding:0 8px}
		.shorts-modal-body .timeline-user-ls .timeline-user-item .user-avatar{width:40px;height:40px;border-radius:50%;overflow:hidden;margin-right:12px;flex-shrink:0}
		.shorts-modal-body .timeline-user-ls .timeline-user-item .user-avatar img{width:100%;height:100%;object-fit:cover}
		.shorts-modal-body .timeline-user-ls .timeline-user-item .user-info{flex:1;min-width:0}
		.shorts-modal-body .timeline-user-ls .timeline-user-item .user-info .user-name{font-weight:500;color:var(--cl-primary-text-color);font-size:14px;margin:0;line-height:1.2}
		.shorts-modal-body .timeline-user-ls .timeline-user-item .user-info .user-username{color:var(--cl-secondary-text-color);font-size:12px;margin:2px 0 0 0;line-height:1.2}
		.shorts-modal-body .timeline-user-ls .timeline-user-item .follow-btn{background:var(--cl-primary-color);color:white;border:none;padding:6px 16px;border-radius:20px;font-size:12px;font-weight:500;cursor:pointer;transition:background 0.2s}
		.shorts-modal-body .timeline-user-ls .timeline-user-item .follow-btn:hover{background:var(--cl-primary-color-darken-5)}
		.shorts-modal-body .timeline-user-ls .timeline-user-item .follow-btn.following{background:var(--cl-success-color)}
		.shorts-modal-body .timeline-user-ls .timeline-user-item .follow-btn.following:hover{background:var(--cl-success-color)}

		/* Hide original timeline user styling that might interfere */
		.shorts-modal-body .timeline-user-ls .timeline-user-item > a{display:none}
		.shorts-modal-body .timeline-user-ls .timeline-user-item .user-data__avatar{display:none}
		.shorts-modal-body .timeline-user-ls .timeline-user-item .user-data__info{display:none}

		/* Report Modal Specific Styles */
		.shorts-modal-body .report-form{max-width:100%}
		.shorts-modal-body .report-form .form-group{margin-bottom:20px}
		.shorts-modal-body .report-form .form-group:last-child{margin-bottom:0}
		.shorts-modal-body .report-form label{display:block;font-weight:500;color:var(--cl-primary-text-color);margin-bottom:12px;font-size:15px}
		.shorts-modal-body .report-form .report-reasons{display:grid;gap:8px}
		.shorts-modal-body .report-form .report-reasons .reason-option{display:flex;align-items:center;padding:12px 16px;border:2px solid var(--cl-primary-border-color);border-radius:8px;cursor:pointer;transition:all 0.2s ease;background:var(--cl-primary-bg-color)}
		.shorts-modal-body .report-form .report-reasons .reason-option:hover{border-color:var(--cl-primary-color);background:var(--cl-secondary-bg-color)}
		.shorts-modal-body .report-form .report-reasons .reason-option.selected{border-color:var(--cl-primary-color);background:var(--cl-primary-color);color:white}
		.shorts-modal-body .report-form .report-reasons .reason-option input[type="radio"]{margin:0 12px 0 0;width:18px;height:18px;accent-color:var(--cl-primary-color)}
		.shorts-modal-body .report-form .report-reasons .reason-option label{margin:0;font-weight:400;cursor:pointer;font-size:14px;flex:1}
		.shorts-modal-body .report-form textarea{width:100%;padding:12px 16px;border:2px solid var(--cl-primary-border-color);border-radius:8px;resize:vertical;min-height:80px;font-family:inherit;font-size:14px;background:var(--cl-primary-bg-color);color:var(--cl-primary-text-color);transition:border-color 0.2s ease}
		.shorts-modal-body .report-form textarea:focus{outline:none;border-color:var(--cl-primary-color)}
		.shorts-modal-body .report-form textarea::placeholder{color:var(--cl-secondary-text-color)}
		.shorts-modal-body .report-form .form-actions{display:flex;gap:12px;justify-content:flex-end;margin-top:24px;padding-top:20px;border-top:1px solid var(--cl-primary-border-color)}
		.shorts-modal-body .report-form .btn{padding:10px 20px;border-radius:6px;font-size:14px;font-weight:500;cursor:pointer;transition:all 0.2s ease;border:none;min-width:80px}
		.shorts-modal-body .report-form .btn-cancel{background:var(--cl-secondary-bg-color);color:var(--cl-secondary-text-color)}
		.shorts-modal-body .report-form .btn-cancel:hover{background:var(--cl-primary-border-color)}
		.shorts-modal-body .report-form .btn-submit{background:var(--cl-danger-color);color:white}
		.shorts-modal-body .report-form .btn-submit:hover{background:var(--cl-danger-color-lighten-5)}
		.shorts-modal-body .report-form .btn-submit:disabled{opacity:0.6;cursor:not-allowed}

		/* Edit Post Modal Specific Styles */
		.shorts-modal-body .edit-form{max-width:100%;position:relative}
		.shorts-modal-body .edit-form .form-group{margin-bottom:24px;position:relative}
		.shorts-modal-body .edit-form .form-group:last-child{margin-bottom:0}
		.shorts-modal-body .edit-form .form-header{display:flex;align-items:center;justify-content:space-between;margin-bottom:16px}
		.shorts-modal-body .edit-form .form-header h3{margin:0;font-size:16px;font-weight:600;color:var(--cl-primary-text-color);display:flex;align-items:center}
		.shorts-modal-body .edit-form .form-header h3::before{content:"✏️";margin-right:8px;font-size:18px}
		.shorts-modal-body .edit-form .char-counter{font-size:13px;color:var(--cl-secondary-text-color);font-weight:500;background:var(--cl-secondary-bg-color);padding:4px 12px;border-radius:20px;transition:all 0.2s ease}
		.shorts-modal-body .edit-form .char-counter.over-limit{color:white;background:var(--cl-danger-color);animation:pulse 1s infinite}
		.shorts-modal-body .edit-form .textarea-container{position:relative;background:var(--cl-secondary-bg-color);border-radius:12px;padding:4px;transition:all 0.3s ease}
		.shorts-modal-body .edit-form .textarea-container:focus-within{background:var(--cl-primary-color);box-shadow:0 0 0 4px rgba(var(--cl-primary-color-rgb), 0.1)}
		.shorts-modal-body .edit-form textarea{width:100%;padding:20px;border:none;border-radius:8px;resize:vertical;min-height:140px;font-family:inherit;font-size:15px;background:var(--cl-primary-bg-color);color:var(--cl-primary-text-color);transition:all 0.2s ease;line-height:1.6;box-shadow:0 2px 8px rgba(0,0,0,0.05)}
		.shorts-modal-body .edit-form textarea:focus{outline:none;box-shadow:0 4px 20px rgba(0,0,0,0.1)}
		.shorts-modal-body .edit-form textarea::placeholder{color:var(--cl-secondary-text-color);font-style:italic}
		.shorts-modal-body .edit-form .form-info{background:linear-gradient(135deg, var(--cl-secondary-bg-color), var(--cl-primary-bg-color));padding:16px 20px;border-radius:10px;font-size:13px;color:var(--cl-secondary-text-color);margin-bottom:24px;border-left:4px solid var(--cl-primary-color);position:relative;overflow:hidden}
		.shorts-modal-body .edit-form .form-info::before{content:"💡";position:absolute;top:16px;left:16px;font-size:16px}
		.shorts-modal-body .edit-form .form-info p{margin:0 0 0 28px;line-height:1.5}
		.shorts-modal-body .edit-form .form-actions{display:flex;gap:16px;justify-content:flex-end;margin-top:32px;padding-top:24px;border-top:1px solid var(--cl-primary-border-color);position:relative}
		.shorts-modal-body .edit-form .form-actions::before{content:"";position:absolute;top:0;left:50%;transform:translateX(-50%);width:60px;height:1px;background:var(--cl-primary-color);opacity:0.3}
		.shorts-modal-body .edit-form .btn{padding:12px 28px;border-radius:25px;font-size:14px;font-weight:600;cursor:pointer;transition:all 0.3s ease;border:none;min-width:120px;position:relative;overflow:hidden;text-transform:uppercase;letter-spacing:0.5px}
		.shorts-modal-body .edit-form .btn::before{content:"";position:absolute;top:50%;left:50%;width:0;height:0;background:rgba(255,255,255,0.2);border-radius:50%;transition:all 0.3s ease;transform:translate(-50%,-50%)}
		.shorts-modal-body .edit-form .btn:hover::before{width:300px;height:300px}
		.shorts-modal-body .edit-form .btn-cancel{background:var(--cl-secondary-bg-color);color:var(--cl-secondary-text-color);border:2px solid var(--cl-primary-border-color)}
		.shorts-modal-body .edit-form .btn-cancel:hover{background:var(--cl-primary-border-color);transform:translateY(-2px);box-shadow:0 4px 12px rgba(0,0,0,0.15)}
		.shorts-modal-body .edit-form .btn-save{background:linear-gradient(135deg, var(--cl-primary-color), var(--cl-primary-color-darken-5));color:white;box-shadow:0 4px 15px rgba(var(--cl-primary-color-rgb), 0.3)}
		.shorts-modal-body .edit-form .btn-save:hover{transform:translateY(-2px);box-shadow:0 6px 20px rgba(var(--cl-primary-color-rgb), 0.4)}
		.shorts-modal-body .edit-form .btn-save:disabled{opacity:0.6;cursor:not-allowed;transform:none;box-shadow:none}
		.shorts-modal-body .edit-form .btn-save:disabled:hover{transform:none}

		/* Animations */
		@keyframes pulse{0%,100%{opacity:1}50%{opacity:0.7}}
		@keyframes fadeInUp{from{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}
		.shorts-modal-body .edit-form{animation:fadeInUp 0.3s ease-out}

		/* Double Tap Love Animation */
		.love-splash{position:absolute !important;pointer-events:none !important;z-index:99999 !important;font-size:60px !important;color:#ff3355 !important;animation:loveSplash 3s ease-out forwards !important;display:block !important;visibility:visible !important}
		@keyframes loveSplash{
			0%{opacity:1 !important;transform:scale(0) rotate(0deg) !important}
			10%{opacity:1 !important;transform:scale(1.5) rotate(-10deg) !important}
			20%{opacity:1 !important;transform:scale(1.2) rotate(5deg) !important}
			50%{opacity:1 !important;transform:scale(1) rotate(0deg) !important}
			100%{opacity:0 !important;transform:scale(0.8) rotate(0deg) translateY(-50px) !important}
		}

		/* Test animation - no animation, just static */
		.love-splash-test{position:absolute !important;pointer-events:none !important;z-index:99999 !important;font-size:60px !important;color:#ff3355 !important;display:block !important;visibility:visible !important;opacity:1 !important}





		.love-particle{position:absolute !important;pointer-events:none !important;z-index:99998 !important;width:8px !important;height:8px !important;background:#ff3355 !important;border-radius:50% !important;animation:loveParticle 1s ease-out forwards !important;display:block !important;visibility:visible !important}
		@keyframes loveParticle{
			0%{opacity:1 !important;transform:scale(1) !important}
			100%{opacity:0 !important;transform:scale(0) translateY(-30px) !important}
		}

		.love-trail{position:absolute !important;pointer-events:none !important;z-index:99997 !important;font-size:24px !important;color:#ff3355 !important;animation:loveTrail 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards !important;display:block !important;visibility:visible !important}
		@keyframes loveTrail{
			0%{opacity:1 !important;transform:scale(1) !important}
			100%{opacity:0 !important;transform:scale(0.3) !important}
		}

		/* Like Button Pulse Animation */
		.like-button-pulse{animation:likeButtonPulse 0.6s ease-out}
		@keyframes likeButtonPulse{
			0%{transform:scale(1)}
			50%{transform:scale(1.3)}
			100%{transform:scale(1)}
		}

		/* Confirm Modal Specific Styles */
		.shorts-modal-body .confirm-content{text-align:center;padding:20px 0}
		.shorts-modal-body .confirm-content h4{margin-bottom:16px;color:var(--cl-primary-text-color);font-size:18px;font-weight:600}
		.shorts-modal-body .confirm-content p{margin-bottom:24px;color:var(--cl-secondary-text-color);line-height:1.5}
		.shorts-modal-body .confirm-content .confirm-actions{display:flex;gap:12px;justify-content:center}
		.shorts-modal-body .confirm-content .btn{padding:10px 24px;border-radius:6px;font-size:14px;font-weight:500;cursor:pointer;transition:all 0.2s ease;border:none;min-width:100px}
		.shorts-modal-body .confirm-content .btn-cancel{background:var(--cl-secondary-bg-color);color:var(--cl-secondary-text-color)}
		.shorts-modal-body .confirm-content .btn-cancel:hover{background:var(--cl-primary-border-color)}
		.shorts-modal-body .confirm-content .btn-confirm{background:var(--cl-danger-color);color:white}
		.shorts-modal-body .confirm-content .btn-confirm:hover{background:var(--cl-danger-color-lighten-5)}
		.shorts-modal-body .confirm-content .btn-confirm:disabled{opacity:0.6;cursor:not-allowed}

		/* Info Modal Specific Styles */
		.shorts-modal-body .info-content{text-align:center;padding:20px 0}
		.shorts-modal-body .info-content h4{margin-bottom:16px;color:var(--cl-primary-text-color);font-size:18px;font-weight:600}
		.shorts-modal-body .info-content p{margin-bottom:24px;color:var(--cl-secondary-text-color);line-height:1.5}
		.shorts-modal-body .info-content .btn{padding:10px 24px;border-radius:6px;font-size:14px;font-weight:500;cursor:pointer;transition:all 0.2s ease;border:none;min-width:100px;background:var(--cl-primary-color);color:white}
		.shorts-modal-body .info-content .btn:hover{background:var(--cl-primary-color-darken-5)}



		/* Loading Animation */
		@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}

		/* Responsive */
		@media(max-width:768px){
			.shorts-modal-content{max-width:95vw;max-height:85vh;margin:10px;border-radius:8px}
			.shorts-modal-header{padding:12px 16px}
			.shorts-modal-body{padding:16px}
			.shorts-modal-body .social-media-links .col-3{flex:0 0 100%;margin-bottom:8px}
			.shorts-modal-body .timeline-user-ls .timeline-user-item{padding:10px 0}
			.shorts-modal-body .timeline-user-ls .timeline-user-item .user-avatar{width:36px;height:36px;margin-right:10px}
			.shorts-modal-body .timeline-user-ls .timeline-user-item .user-info .user-name{font-size:13px}
			.shorts-modal-body .timeline-user-ls .timeline-user-item .user-info .user-username{font-size:11px}
			.shorts-modal-body .timeline-user-ls .timeline-user-item .follow-btn{padding:5px 12px;font-size:11px}
			/* Report Modal Mobile */
			.shorts-modal-body .report-form .form-group{margin-bottom:16px}
			.shorts-modal-body .report-form .report-reasons .reason-option{padding:10px 12px}
			.shorts-modal-body .report-form .form-actions{flex-direction:column-reverse;gap:8px}
			.shorts-modal-body .report-form .btn{width:100%;padding:12px 20px}
			/* Edit Modal Mobile */
			.shorts-modal-body .edit-form .form-group{margin-bottom:20px}
			.shorts-modal-body .edit-form .form-header{flex-direction:column;align-items:flex-start;gap:12px}
			.shorts-modal-body .edit-form .char-counter{align-self:flex-end}
			.shorts-modal-body .edit-form textarea{min-height:120px;padding:16px;font-size:14px}
			.shorts-modal-body .edit-form .form-actions{flex-direction:column-reverse;gap:12px;margin-top:24px}
			.shorts-modal-body .edit-form .btn{width:100%;padding:14px 20px;border-radius:12px}
			/* Confirm Modal Mobile */
			.shorts-modal-body .confirm-content .confirm-actions{flex-direction:column;gap:8px}
			.shorts-modal-body .confirm-content .btn{width:100%;padding:12px 20px}
			/* Publisher Mobile */
			.shorts-publisher{left:16px;bottom:30px;padding:8px 12px;gap:10px;max-width:calc(100% - 32px)}
			.shorts-publisher .avatar-holder{width:36px;height:36px}
			.shorts-publisher .username{font-size:13px}
			.shorts-publisher .btn-follow{padding:5px 14px;font-size:11px;border-radius:18px}
		}
		/* Publisher avatar & follow - Duplicate removed (using main styles above) */
		#swipeHint{position:absolute;bottom:20px;left:50%;transform:translateX(-50%);color:#fff;font-size:14px;opacity:.8;animation:fadeOut 3s forwards;pointer-events:none}
		@keyframes fadeOut{0%{opacity:.8;}80%{opacity:.8;}100%{opacity:0;}}
	</style>
<script src="vue_fix_injection.js?v=1751490584"></script>
</head>
<body <?php if ($cl["curr_lang"]["lang_data"]["is_rtl"] == "Y"){echo('dir="rtl"');} else{echo('dir="ltr"');} ?> class="cl-app-<?php echo fetch_or_get($cl["pn"], "none"); ?> <?php echo (($cl["is_logged"]) ? "" : "body-guest-mode" ); ?>" data-page-tab="<?php echo fetch_or_get($cl["page_tab"], "none"); ?>" data-skin="<?php echo($cl["display_set"]["color_scheme"]); ?>" data-bg="<?php echo($cl["display_set"]["background"]); ?>">
	<div class="main-preloader-holder" id="main-preloader-holder">
		<div class="main-preloader">
			<div class="loading">
				<img src="/themes/default/statics/img/logo_welcome.png" alt="Logo">
			</div>
		</div>
	</div>
	<div data-el="main-content-holder">
		<main class="main-content-container <?php echo (($cl["is_logged"]) ? "" : "guest-mode" ); ?>">
			<?php if ($cl["pn"] == "guest"): ?>
				<?php echo $cl["http_res"]; ?>
			<?php else: ?>
				<div class="main-content-container-inner">
					<?php if (not_empty($cl["sbl"])): ?>
						<?php if (not_empty($cl["is_logged"])): ?>
							<?php echo cl_template('main/left_sidebar'); ?>
						<?php else: ?>
							<?php echo cl_template('main/left_sidebar_guest'); ?>
						<?php endif; ?>
					<?php endif; ?>

					<div data-el="timeline-container-wrapper" class="timeline-container-wrapper">
						<div data-el="timeline-content" class="timeline-container-inner">
							<?php echo $cl["http_res"]; ?>
						</div>

						<div data-el="spa-preloader" class="spa-preloader d-none">
							<div class="spa-preloader-inner">
								<span class="spinner-icon">
									<?php echo cl_icon("spinner-icon"); ?>
								</span>
							</div>
						</div>
					</div>

					<?php if (not_empty($cl["sbr"])): ?>
						<?php echo cl_template('main/right_sidebar'); ?>
					<?php endif; ?>
				</div>
			<?php endif; ?>
		</main>

		<?php if (not_empty($cl["is_logged"]) && in_array($cl["pn"], array("conversation")) != true): ?>
			<?php echo cl_template('main/bottom_navbar'); ?>
		<?php endif; ?>

		<input id="csrf-token" type="hidden" class="hidden d-none" value="<?php echo fetch_or_get($cl['csrf_token'],'none'); ?>">

		<?php if ($cl['is_logged']): ?>
			<div class="modal fade modal-pubbox" data-app="modal-pubbox" id="add_new_post" tabindex="-1" role="dialog" aria-hidden="true">
			    <div class="modal-dialog" role="document">
			        <div class="modal-content">
			            <div class="modal-header">
			                <div class="modal-header__inner">
			                    <h5 class="modal-title" data-an="modal-title">
			                    	<?php echo cl_translate('New post'); ?>
			                   	</h5>
			                    <span class="dismiss-modal" data-dismiss="modal">
			                        <?php echo cl_ficon('dismiss'); ?>
			                    </span>
			                </div>
			            </div>
			            <div class="modal-body">
			                <?php $cl['pb_id'] = 2; echo cl_template('timeline/pubbox2'); ?>
			            </div>
			        </div>
			    </div>
			</div>
			<?php echo cl_template('timeline/scripts/pubbox_master_script'); ?>	
		<?php endif; ?>
		<div data-app="black-hole"></div>
		<?php if (not_empty($cl['config']['google_analytics'])): ?>
			<?php echo htmlspecialchars_decode($cl['config']['google_analytics']); ?>
		<?php endif; ?>

		<?php if ($cl["pn"] != 'guest'): ?>
			<?php echo cl_template("main/scripts/app_master_script"); ?>
		<?php endif; ?>

		<?php if ($cl['is_logged']): ?>
			<?php if (in_array($cl["pn"], array("bookmarks", "home", "profile", "search", "thread"))): ?>
				<?php echo cl_template("main/modals/report_post"); ?>
			<?php endif; ?>

			<?php echo cl_template("main/modals/display_settings"); ?>
		<?php endif; ?>
	</div>

	<?php if ($cl['config']['cookie_warning_popup'] == "on"): ?>
		<?php if (empty($_COOKIE['__c_u_a__'])): ?>
			<?php if ($cl["pn"] != "stat_pages"): ?>
				<?php echo cl_template("main/modals/cookies_alert"); ?>
			<?php endif; ?>
		<?php endif; ?>
	<?php endif; ?>

	<script>
		<?php echo(cl_get_custom_code("footerjs")); ?>
	</script>

	<?php echo cl_template('custom/footer_tags'); ?>

	<!-- Shorts overlay -->
	<div id="shortsViewer">
		<button id="shortsCloseBtn">
			<svg width="24" height="24" fill="none" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
				<path d="M15 18L9 12L15 6" stroke="#ffffff" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
			</svg>
		</button>
		<!-- Shorts Modal Container -->
		<div id="shortsModalContainer" class="shorts-modal-container">
			<div id="shortsModalBackdrop" class="shorts-modal-backdrop"></div>
			<div id="shortsModalContent" class="shorts-modal-content"></div>
		</div>
	</div>

	<!-- Global HLS Video Player for Data-Efficient Streaming -->
	<script src="{%config theme_url%}/statics/js/global-hls-player.js"></script>

	<!-- Smart Video Preloader for Instagram/TikTok-style loading -->
	<script src="{%config theme_url%}/statics/js/smart-video-preloader.js"></script>

	<!-- Aspect Ratio Video Container Handler -->
	<script>
	(function() {
		'use strict';

		// Aspect ratio handler for preventing layout shifts
		window.AspectRatioVideoHandler = {
			// Parse ratio string to decimal
			parseRatio(ratioString) {
				if (!ratioString) return 16/9; // Default
				const parts = ratioString.split(':');
				if (parts.length !== 2) return 16/9;
				const width = parseFloat(parts[0]);
				const height = parseFloat(parts[1]);
				return width / height;
			},

			// Apply aspect ratio to container
			applyAspectRatio(container, ratio) {
				if (!container) return;

				// Use CSS aspect-ratio if supported
				if (CSS.supports && CSS.supports('aspect-ratio', '16/9')) {
					container.style.aspectRatio = ratio;
					container.style.height = 'auto';
					container.style.paddingBottom = '0';
				} else {
					// Fallback for older browsers
					const decimal = this.parseRatio(ratio);
					const paddingBottom = (100 / decimal) + '%';
					container.style.aspectRatio = '';
					container.style.height = '0';
					container.style.paddingBottom = paddingBottom;
				}
			},

			// Initialize all video containers
			initializeContainers() {
				const containers = document.querySelectorAll('.cl-plyr-video[style*="aspect-ratio"]');
				containers.forEach(container => {
					const video = container.querySelector('video');
					if (!video) return;

					const ratio = video.dataset.videoRatio || '16:9';
					this.applyAspectRatio(container, ratio);

					// Mark video as loaded when it's ready
					video.addEventListener('loadedmetadata', () => {
						video.dataset.loaded = 'true';

						// Double-check aspect ratio from actual video
						if (video.videoWidth && video.videoHeight) {
							const actualRatio = video.videoWidth / video.videoHeight;
							const expectedRatio = this.parseRatio(ratio);

							// If ratios differ significantly, update container
							if (Math.abs(actualRatio - expectedRatio) > 0.1) {
								const newRatio = `${video.videoWidth}:${video.videoHeight}`;
								this.applyAspectRatio(container, newRatio);
								console.log('Updated aspect ratio for video:', video.id, 'to:', newRatio);
							}
						}
					});
				});
			},

			// Handle new videos added dynamically
			observeNewVideos() {
				const observer = new MutationObserver(mutations => {
					mutations.forEach(mutation => {
						mutation.addedNodes.forEach(node => {
							if (node.nodeType === 1) { // Element node
								const containers = node.querySelectorAll ?
									node.querySelectorAll('.cl-plyr-video') :
									(node.classList && node.classList.contains('cl-plyr-video') ? [node] : []);

								containers.forEach(container => {
									const video = container.querySelector('video');
									if (video && video.dataset.videoRatio) {
										this.applyAspectRatio(container, video.dataset.videoRatio);
									}
								});
							}
						});
					});
				});

				observer.observe(document.body, {
					childList: true,
					subtree: true
				});
			}
		};

		// Initialize when DOM is ready
		if (document.readyState === 'loading') {
			document.addEventListener('DOMContentLoaded', () => {
				window.AspectRatioVideoHandler.initializeContainers();
				window.AspectRatioVideoHandler.observeNewVideos();
			});
		} else {
			window.AspectRatioVideoHandler.initializeContainers();
			window.AspectRatioVideoHandler.observeNewVideos();
		}

		// Re-initialize on window resize for responsive adjustments
		window.addEventListener('resize', () => {
			window.AspectRatioVideoHandler.initializeContainers();
		});
	})();
	</script>

	<!-- Shorts / Reels overlay script -->
	<script>
	(function(){
	  console.log('🚀 ORIGINAL SHORTS SCRIPT LOADING...');
	  const FEED_SEL = '.publication-video video, .cl-plyr-video video';
	  const overlay = document.getElementById('shortsViewer');
	  const closeBtn = document.getElementById('shortsCloseBtn');
	  console.log('🚀 SHORTS: Elements found - overlay:', !!overlay, 'closeBtn:', !!closeBtn);

	  // Icon SVGs for use in JavaScript-generated HTML
	  const ICONS = {
	    thumb_like: '<?php echo str_replace(array("\n", "\r", "\t"), "", cl_ficon("thumb_like")); ?>',
	    comment: '<?php echo str_replace(array("\n", "\r", "\t"), "", cl_ficon("comment")); ?>',
	    repeat: '<?php echo str_replace(array("\n", "\r", "\t"), "", cl_ficon("repeat")); ?>',
	    share: '<?php echo str_replace(array("\n", "\r", "\t"), "", cl_ficon("share")); ?>',
	    more_horiz: '<?php echo str_replace(array("\n", "\r", "\t"), "", cl_ficon("more_horiz")); ?>',
	    open: '<?php echo str_replace(array("\n", "\r", "\t"), "", cl_ficon("open")); ?>',
	    note_edit: '<?php echo str_replace(array("\n", "\r", "\t"), "", cl_ficon("note_edit")); ?>',
	    delete: '<?php echo str_replace(array("\n", "\r", "\t"), "", cl_ficon("delete")); ?>',
	    bookmark: '<?php echo str_replace(array("\n", "\r", "\t"), "", cl_ficon("bookmark")); ?>',
	    clipboard_link: '<?php echo str_replace(array("\n", "\r", "\t"), "", cl_ficon("clipboard_link")); ?>',
	    flag: '<?php echo str_replace(array("\n", "\r", "\t"), "", cl_ficon("flag")); ?>',
	    pin: '<?php echo str_replace(array("\n", "\r", "\t"), "", cl_ficon("pin")); ?>',
	    arrow_download: '<?php echo str_replace(array("\n", "\r", "\t"), "", cl_ficon("arrow_download")); ?>',
	    lock_closed: '<?php echo str_replace(array("\n", "\r", "\t"), "", cl_ficon("lock_closed")); ?>',
	    lock_open: '<?php echo str_replace(array("\n", "\r", "\t"), "", cl_ficon("lock_open")); ?>',
	    users_list: '<?php echo str_replace(array("\n", "\r", "\t"), "", cl_ficon("users_list")); ?>',
	    shield_error: '<?php echo str_replace(array("\n", "\r", "\t"), "", cl_ficon("shield_error")); ?>',
	    note: '<?php echo str_replace(array("\n", "\r", "\t"), "", cl_ficon("note")); ?>',
	    alert_urgent: '<?php echo str_replace(array("\n", "\r", "\t"), "", cl_ficon("alert_urgent")); ?>',
	    text: '<?php echo str_replace(array("\n", "\r", "\t"), "", cl_ficon("text")); ?>',
	    video: '<?php echo str_replace(array("\n", "\r", "\t"), "", cl_ficon("video")); ?>',
	    poll: '<?php echo str_replace(array("\n", "\r", "\t"), "", cl_ficon("poll")); ?>',
	    timer_off: '<?php echo str_replace(array("\n", "\r", "\t"), "", cl_ficon("timer_off")); ?>',
	    document: '<?php echo str_replace(array("\n", "\r", "\t"), "", cl_ficon("document")); ?>',
	    music: '<?php echo str_replace(array("\n", "\r", "\t"), "", cl_ficon("music")); ?>'
	  };

	  // Helper function to detect and apply aspect ratio classes to videos (INSTANT)
	  function detectAndApplyAspectRatio(video) {
	    // Check if aspect ratio was already pre-calculated
	    if (video.dataset.aspectPrecalculated === 'true') {
	      console.log(`⚡ Aspect ratio already pre-calculated for ${video.id}, skipping detection`);
	      return; // Already applied during creation
	    }

	    if (video.videoWidth && video.videoHeight) {
	      const aspectRatio = video.videoWidth / video.videoHeight;
	      console.log(`🔍 Detecting aspect ratio for ${video.id}: ${video.videoWidth}x${video.videoHeight}, ratio: ${aspectRatio.toFixed(2)}`);

	      // Remove any existing aspect ratio classes
	      video.classList.remove('aspect-tall', 'aspect-square', 'aspect-wide');

	      // Apply appropriate class based on aspect ratio
	      if (aspectRatio < 0.8) {
	        // Tall/Portrait videos (9:16 = 0.5625, 3:4 = 0.75)
	        video.classList.add('aspect-tall');
	        console.log(`Applied aspect-tall to video ${video.id}`);
	      } else if (aspectRatio >= 0.8 && aspectRatio <= 1.2) {
	        // Square videos (1:1 = 1.0)
	        video.classList.add('aspect-square');
	        console.log(`Applied aspect-square to video ${video.id}`);
	      } else {
	        // Wide/Landscape videos (16:9 = 1.777, 4:3 = 1.333)
	        video.classList.add('aspect-wide');
	        console.log(`Applied aspect-wide to video ${video.id}`);
	      }

	      // Mark as calculated to prevent future recalculation
	      video.dataset.aspectPrecalculated = 'true';
	    }
	  }

	  // Helper function to setup aspect ratio detection for a video (INSTANT)
	  function setupAspectRatioDetection(video) {
	    // If aspect ratio was pre-calculated, no need to wait for loadedmetadata
	    if (video.dataset.aspectPrecalculated === 'true') {
	      console.log(`⚡ Aspect ratio pre-calculated for ${video.id}, no detection needed`);
	      return; // Already applied during creation
	    }

	    // Listen for loadedmetadata to detect real aspect ratio (fallback)
	    video.addEventListener('loadedmetadata', () => detectAndApplyAspectRatio(video));

	    // If metadata is already loaded, apply immediately
	    if (video.readyState >= 1) {
	      detectAndApplyAspectRatio(video);
	    }
	  }

	  // Helper function to create a video slide from a video element
	  function createVideoSlide(videoElement, index) {
	    const slide = document.createElement('div');
	    slide.className = 'shorts-slide';
	    slide.dataset.videoIndex = index;

	    // Get MP4 source URL from various possible sources
	    let src = videoElement.src;

	    // If no direct src, try to extract from data attributes (for newly loaded videos)
	    if (!src) {
	      // First try data-hls and convert to MP4
	      if (videoElement.dataset.hls && videoElement.dataset.hls.includes('.m3u8')) {
	        src = videoElement.dataset.hls.replace('.m3u8', '.mp4');
	        console.log('Converted HLS to MP4 source:', src);
	      }
	      // Try data-src attribute
	      else if (videoElement.dataset.src) {
	        src = videoElement.dataset.src;
	        console.log('Using data-src:', src);
	      }
	      // Try to find source element
	      else {
	        const sourceEl = videoElement.querySelector('source[type="video/mp4"]');
	        if (sourceEl && sourceEl.src) {
	          src = sourceEl.src;
	          console.log('Using source element src:', src);
	        }
	      }
	    }

	    if (!src) {
	      console.error('No MP4 source found for video:', videoElement.id, 'Available attributes:', {
	        src: videoElement.src,
	        dataSrc: videoElement.dataset.src,
	        dataHls: videoElement.dataset.hls,
	        hasSourceElements: videoElement.querySelectorAll('source').length
	      });
	      return null;
	    }
	    console.log('Using MP4 source:', src);

	    const poster = videoElement.getAttribute('poster') || '';

	    // Spinner element
	    const spinner = '<div class="loading-spinner"></div>';

	    // Use original video ID for state management
	    const originalVideoId = videoElement.id || `video-${index}`;
	    const videoId = `shorts-${originalVideoId}`;

	    // Get video ratio for fallback (real aspect ratio will be detected dynamically)
	    const videoRatio = videoElement.dataset.videoRatio || '16:9';

	    // Data-efficient preloading: Only current video gets metadata, others get none
	    const preloadType = index === 0 ? 'metadata' : 'none'; // Only current video preloads metadata
	    const muteAttribute = window.__globalMute !== false ? 'muted' : ''; // Respect global mute state
	    console.log('🔊 TIMELINE VIDEO CREATION: Global mute state:', window.__globalMute, 'Mute attribute:', muteAttribute);

	    // Pre-calculate aspect ratio from video ratio to prevent page jumping
	    let aspectRatioClass = 'aspect-tall'; // Default to tall for shorts (most common)
	    if (videoRatio) {
	      const [width, height] = videoRatio.split(':').map(Number);
	      if (width && height) {
	        const aspectRatio = width / height;
	        // Use same thresholds as API for consistency
	        if (aspectRatio < 0.8) {
	          aspectRatioClass = 'aspect-tall';  // Portrait videos (9:16, 3:4, etc.)
	        } else if (aspectRatio > 1.3) {
	          aspectRatioClass = 'aspect-wide';  // Landscape videos (16:9, 4:3, etc.)
	        } else {
	          aspectRatioClass = 'aspect-square'; // Square-ish videos (1:1, etc.)
	        }
	        console.log(`📐 TIMELINE: Pre-calculated aspect ratio for ${videoId}: ${videoRatio} (${aspectRatio.toFixed(2)}) -> ${aspectRatioClass}`);

	        // Debug specific case
	        if (videoRatio === '16:9' && aspectRatioClass !== 'aspect-wide') {
	          console.error(`🚨 BUG: 16:9 video ${videoId} got ${aspectRatioClass} instead of aspect-wide! Calculation: ${width}/${height} = ${aspectRatio}`);
	        }
	      } else {
	        console.log(`📐 TIMELINE: Invalid video ratio ${videoRatio}, defaulting to aspect-tall for ${videoId}`);
	      }
	    } else {
	      console.log(`📐 TIMELINE: No video ratio data, defaulting to aspect-tall for ${videoId}`);
	    }

	    // Debug: Show if this should be a full-screen tall video
	    if (aspectRatioClass === 'aspect-tall') {
	      console.log(`📱 FULL SCREEN: Video ${videoId} will take full screen height on mobile`);
	    }

	    const videoHTML = `
	      <div class="shorts-video-wrapper" style="position: relative; width: 100%; height: 100vh; display: flex; align-items: center; justify-content: center;">
	        <video id="${videoId}" data-original-id="${originalVideoId}" data-video-ratio="${videoRatio}" data-aspect-precalculated="true" playsinline ${muteAttribute} loop poster="${poster}" src="${src}" preload="${preloadType}" class="shorts-video ${aspectRatioClass}" style="background:#000;" crossorigin="anonymous"></video>
	        <div class="pause-indicator" style="display: none;"></div>
	      </div>
	    `;

	    // Clone control actions if available
	    let actionsHTML = '';
	    const ctrl = videoElement.closest('.post-data-layout')?.querySelector('.post-data-layout__controls');
	    if (ctrl) { actionsHTML = `<div class="shorts-actions">${ctrl.outerHTML}</div>`; }

	    // Build publisher header (avatar + username + follow button)
	    let publisherHTML = '';
	    const postItem = videoElement.closest('.post-list-item');
	    if (postItem) {
	      const avatarImg = postItem.querySelector('.post-data__avatar img');
	      const usernameLink = postItem.querySelector('.post-data-layout__publisher .post-username a');
	      if (avatarImg && usernameLink) {
	        const avatarSrc = avatarImg.getAttribute('data-src') || avatarImg.src || '';
	        const userHref = usernameLink.href || '#';
	        const userName = (usernameLink.querySelector('.user-name-holder__name') || usernameLink).textContent.trim();
	        const userId = usernameLink.getAttribute('data-uinfo-lbox') || '';
        // Extract verification badge if present
        const verificationBadge = usernameLink.querySelector('.user-name-holder__badge');
        const verificationBadgeHTML = verificationBadge ? verificationBadge.outerHTML : '';
        // Extract post description from timeline (already processed HTML)
        const postTextElement = postItem.querySelector('.publication-text');
        const postDescription = postTextElement ? postTextElement.innerHTML.trim() : '';

	        // Read owner/following state from data attributes (set in PHP)
	        const isOwner = postItem.getAttribute('data-is-owner') === '1';
	        let isFollowing = postItem.getAttribute('data-is-following') === '1';

	        // Check follow status cache for real-time updates
	        // First check localStorage for persistent storage
	        try {
	          var followCache = JSON.parse(localStorage.getItem('followStatusCache') || '{}');
	          if (followCache.hasOwnProperty(userId)) {
	            isFollowing = followCache[userId];
	            console.log('Using localStorage follow status for user ' + userId + ': ' + isFollowing);
	          }
	        } catch (e) {
	          console.log('Error reading follow status from localStorage:', e);
	        }

	        // Then check memory cache as fallback
	        if (window.followStatusCache && window.followStatusCache.hasOwnProperty(userId)) {
	          isFollowing = window.followStatusCache[userId];
	          console.log('Using memory cached follow status for user ' + userId + ': ' + isFollowing);
	        }

	        let followBtn = '';
	        if (!isOwner && userId && !isFollowing) {
	          // Only show follow button if user is NOT already being followed
	          followBtn = `<button class="btn-follow" onclick="SMColibri.follow(this);" data-action="follow" data-user-name="${userName}" data-id="${userId}">Follow</button>`;
	          console.log('Showing follow button for user ' + userName + ' (ID: ' + userId + ')');
	        } else if (!isOwner && userId && isFollowing) {
	          console.log('Hiding follow button for followed user ' + userName + ' (ID: ' + userId + ')');
	        }
	        const descriptionHTML = postDescription ? `<div class="shorts-description-box"><div class="shorts-description publication-text" data-post-text="shorts-${postItem.getAttribute('data-list-item')}">${postDescription}</div></div>` : '';
        publisherHTML = `<div class="shorts-publisher"><a href="${userHref}" data-spa="true" class="avatar-holder"><img src="${avatarSrc}" alt="Avatar"></a><a href="${userHref}" data-spa="true" class="username"><span class="user-name-holder"><span class="user-name-holder__name">${userName}</span>${verificationBadgeHTML}</span></a>${followBtn}</div>${descriptionHTML}`;
	      }
	    }

	    // Place publisherHTML at the bottom, after actionsHTML
	    slide.innerHTML = spinner + videoHTML + actionsHTML + publisherHTML;

	    // Immediately position description to prevent center flash
	    setTimeout(() => {
	      const descriptionBox = slide.querySelector('.shorts-description-box');
	      if (descriptionBox) {
	        // Force immediate bottom positioning
	        descriptionBox.style.setProperty('position', 'absolute', 'important');
	        descriptionBox.style.setProperty('bottom', '0', 'important');
	        descriptionBox.style.setProperty('left', '20px', 'important');
	        descriptionBox.style.setProperty('right', '80px', 'important');
	        descriptionBox.style.setProperty('top', 'auto', 'important');
	        descriptionBox.style.setProperty('transform', 'none', 'important');
	        descriptionBox.classList.add('positioned');
	        console.log('🎯 Immediately positioned description for timeline slide');
	      }
	    }, 0);

	    // Setup aspect ratio detection for the video
	    setTimeout(() => {
	      const video = slide.querySelector('video');
	      if (video) {
	        setupAspectRatioDetection(video);
	      }
	    }, 0);

	    // Initialize Instagram-style description expansion
	    setTimeout(() => {
	      const description = slide.querySelector('.shorts-description');
	      const descriptionBox = slide.querySelector('.shorts-description-box');
	      const publisher = slide.querySelector('.shorts-publisher');

	      if (description && descriptionBox && publisher) {
	        // Force reset any existing readmore or expanded states
	        if (description.hasAttribute('data-readmore')) {
	          $(description).readmore('destroy');
	        }
	        initInstagramStyleDescription(description, descriptionBox, publisher);
	      }
	    }, 200);

	    // Ensure transforms are applied to any expanded or collapsed descriptions
	    setTimeout(() => {
	      ensureDescriptionTransforms();
	    }, 300);

	    return slide;
	  }

	  // Smart HTML truncation function that preserves links and hashtags
	  function truncateHTML(html, maxChars) {
	    const tempDiv = document.createElement('div');
	    tempDiv.innerHTML = html;

	    let charCount = 0;
	    let result = '';

	    function processNode(node) {
	      if (charCount >= maxChars) return false; // Stop processing

	      if (node.nodeType === Node.TEXT_NODE) {
	        const text = node.textContent;
	        const remainingChars = maxChars - charCount;

	        if (text.length <= remainingChars) {
	          // Entire text fits
	          result += text;
	          charCount += text.length;
	          return true;
	        } else {
	          // Truncate text at word boundary
	          let truncated = text.substring(0, remainingChars);
	          const lastSpace = truncated.lastIndexOf(' ');
	          if (lastSpace > remainingChars - 20) {
	            truncated = truncated.substring(0, lastSpace);
	          }
	          result += truncated;
	          charCount = maxChars;
	          return false; // Stop processing
	        }
	      } else if (node.nodeType === Node.ELEMENT_NODE) {
	        // For elements like <a>, <span>, etc.
	        const tagName = node.tagName.toLowerCase();
	        const attributes = Array.from(node.attributes)
	          .map(attr => `${attr.name}="${attr.value}"`)
	          .join(' ');

	        result += `<${tagName}${attributes ? ' ' + attributes : ''}>`;

	        // Process child nodes
	        for (let child of node.childNodes) {
	          if (!processNode(child)) {
	            break; // Stop if we've reached the limit
	          }
	        }

	        result += `</${tagName}>`;
	        return charCount < maxChars;
	      }
	      return true;
	    }

	    // Process all child nodes
	    for (let child of tempDiv.childNodes) {
	      if (!processNode(child)) {
	        break;
	      }
	    }

	    return result;
	  }

	  // Instagram-style description expansion function
	  function initInstagramStyleDescription(description, descriptionBox, publisher) {
	    console.log('🚀 Initializing Instagram-style description');

	    // PREVENT ALL AUTOMATIC STYLING - only allow user-triggered changes
	    // Add a data attribute to track initialization state
	    description.setAttribute('data-initialized', 'true');

	    // Reset any existing classes and styles but don't add any automatic ones
	    description.classList.remove('expanded', 'collapsed');
	    description.style.maxHeight = '';
	    description.style.overflow = '';

	    // Mark description box as positioned so it becomes visible
	    descriptionBox.classList.add('positioned');
	    descriptionBox.classList.remove('expanded');
	    publisher.classList.remove('description-expanded');

	    let originalHTML = description.innerHTML.replace(/<span class="instagram-more-btn">.*?<\/span>/g, '').trim();

	    // Get plain text for length calculation (without HTML tags)
	    const tempDiv = document.createElement('div');
	    tempDiv.innerHTML = originalHTML;
	    let plainText = tempDiv.textContent || tempDiv.innerText || '';

	    // Clean up plain text: remove extra spaces, line breaks, and normalize whitespace
	    plainText = plainText
	      .replace(/\s+/g, ' ')                   // Replace multiple spaces/tabs/newlines with single space
	      .replace(/\n+/g, ' ')                   // Replace newlines with spaces
	      .replace(/\r+/g, ' ')                   // Replace carriage returns with spaces
	      .trim();                                // Remove leading/trailing whitespace

	    const maxCharacters = 65; // Show only 65 characters before "read more"
	    console.log('📏 Original HTML:', originalHTML.substring(0, 100) + '...');
	    console.log('📏 Plain text:', plainText.substring(0, 100) + '...');
	    console.log('📏 Character limit:', maxCharacters, '- Text length:', plainText.length);

	    // Check if text is long enough to need truncation
	    console.log('📝 Total characters:', plainText.length);

	    if (plainText.length > maxCharacters) {
	      // Text is long, needs truncation
	      let isExpanded = false;

	      function truncateText() {
	        console.log('✂️ Truncating text...');
	        description.classList.add('collapsed');
	        description.classList.remove('expanded');
	        descriptionBox.classList.remove('expanded');
	        publisher.classList.remove('description-expanded');

	        // Reset all custom styles to default - remove important styles
	        publisher.style.removeProperty('bottom');
	        descriptionBox.style.removeProperty('height');
	        descriptionBox.style.removeProperty('max-height');
	        descriptionBox.style.removeProperty('overflow-y');
	        descriptionBox.style.removeProperty('bottom');
	        descriptionBox.style.removeProperty('top');
	        descriptionBox.style.removeProperty('left');
	        descriptionBox.style.removeProperty('right');
	        descriptionBox.style.removeProperty('background');
	        descriptionBox.style.removeProperty('backdrop-filter');
	        descriptionBox.style.removeProperty('border-radius');
	        descriptionBox.style.removeProperty('border');
	        description.style.removeProperty('max-height');
	        description.style.removeProperty('overflow-y');
	        description.style.removeProperty('padding');
	        description.style.removeProperty('line-height');
	        description.style.removeProperty('height');

	        // Smart HTML truncation that preserves links and hashtags
	        const truncatedHTML = truncateHTML(originalHTML, maxCharacters);
	        description.innerHTML = truncatedHTML + '... <span class="instagram-more-btn" title="Show more">⋯</span>';
	        console.log('✅ HTML truncated to ~65 characters with dropdown icon');

	        // Add click handler to more button
	        const moreBtn = description.querySelector('.instagram-more-btn');
	        if (moreBtn) {
	          console.log('🔘 More button found, adding click handler');
	          console.log('🔘 Button HTML:', moreBtn.outerHTML);
	          moreBtn.addEventListener('click', function(e) {
	            console.log('🖱️ More button clicked!');
	            e.preventDefault();
	            e.stopPropagation();
	            e.stopImmediatePropagation();

	            // Instant expansion - no delay
	            expandText(true); // User triggered
	          });
	          // Also add touch events for mobile
	          moreBtn.addEventListener('touchend', function(e) {
	            console.log('👆 More button touched!');
	            e.preventDefault();
	            e.stopPropagation();
	            e.stopImmediatePropagation();
	            expandText(true); // User triggered
	          });
	          console.log('✅ Click handlers added to more button');
	        } else {
	          console.warn('⚠️ More button not found in:', description.innerHTML);
	        }
	      }

	      function expandText(userTriggered = false) {
	        console.log('📖 Expanding description...', userTriggered ? '(user clicked)' : '(auto)');

	        // Only apply dynamic positioning if user actually clicked "more"
	        if (!userTriggered) {
	          console.log('⏭️ Auto-expansion (failed video) - using simple full expansion');
	          isExpanded = true;
	          description.classList.remove('collapsed');
	          description.classList.add('expanded');
	          descriptionBox.classList.add('expanded');
	          publisher.classList.add('description-expanded');
	          description.innerHTML = originalHTML + ' <span class="instagram-more-btn" title="Show less">⌃</span>';

	          // Ensure full description is visible for failed videos
	          description.style.setProperty('max-height', 'none', 'important');
	          description.style.setProperty('height', 'auto', 'important');
	          descriptionBox.style.setProperty('max-height', '60vh', 'important');
	          descriptionBox.style.setProperty('overflow-y', 'auto', 'important');
	          descriptionBox.style.setProperty('background', 'rgba(0,0,0,0.8)', 'important');
	          descriptionBox.style.setProperty('backdrop-filter', 'blur(15px)', 'important');
	          descriptionBox.style.setProperty('border-radius', '12px', 'important');
	          descriptionBox.style.setProperty('padding', '15px', 'important');

	          return;
	        }

	        isExpanded = true;
	        description.classList.remove('collapsed');
	        description.classList.add('expanded');
	        descriptionBox.classList.add('expanded');
	        publisher.classList.add('description-expanded');

	        description.innerHTML = originalHTML + ' <span class="instagram-more-btn" title="Show less">⌃</span>';
	        console.log('✅ Description expanded by user');

	        // Make description container size based on actual text content - INSTANT
	        // Check if shorts viewer is fully loaded to prevent positioning during loading
	        const shortsViewer = document.getElementById('shortsViewer');
	        if (!shortsViewer || shortsViewer.style.display === 'none') {
	          console.log('⏳ Shorts viewer not ready, using fallback expansion');
	          // Fallback: Simple expansion without complex positioning
	          description.style.setProperty('max-height', 'none', 'important');
	          description.style.setProperty('height', 'auto', 'important');
	          descriptionBox.style.setProperty('max-height', '60vh', 'important');
	          descriptionBox.style.setProperty('overflow-y', 'auto', 'important');
	          return;
	        }

	        const viewportHeight = window.innerHeight;

	        // Use fallback if elements aren't properly positioned yet
	        if (!description.offsetParent || !descriptionBox.offsetParent || !publisher.offsetParent) {
	          console.log('⏳ Elements not properly positioned, using fallback expansion');
	          // Fallback: Simple expansion that always works
	          description.style.setProperty('max-height', 'none', 'important');
	          description.style.setProperty('height', 'auto', 'important');
	          descriptionBox.style.setProperty('max-height', '60vh', 'important');
	          descriptionBox.style.setProperty('overflow-y', 'auto', 'important');
	          descriptionBox.style.setProperty('background', 'rgba(0,0,0,0.8)', 'important');
	          descriptionBox.style.setProperty('backdrop-filter', 'blur(15px)', 'important');
	          descriptionBox.style.setProperty('border-radius', '12px', 'important');
	          descriptionBox.style.setProperty('padding', '15px', 'important');
	          return;
	        }

	        // First, let the text flow naturally to measure its height
	        description.style.setProperty('height', 'auto', 'important');
	        description.style.setProperty('max-height', 'none', 'important');
	        const naturalTextHeight = description.scrollHeight;

	        // Calculate container height based on text content
	        const minHeight = 100; // Minimum container height
	        const maxHeight = viewportHeight * 0.5; // Maximum 50% of screen
	        const paddingAndMargin = 40; // Account for padding and margins

	        // Use natural text height but cap it at reasonable limits
	        const idealHeight = naturalTextHeight + paddingAndMargin;
	        const containerHeight = Math.min(Math.max(idealHeight, minHeight), maxHeight);

	        // Push publisher up enough to make room for the dynamically sized description
	        const publisherBottom = containerHeight + 80; // Push publisher above the expanded description
	        publisher.style.setProperty('bottom', `${publisherBottom}px`, 'important');

	        // Set description container to grow upward from its current bottom position
	        // For expanded descriptions, allow reasonable height with scrolling if needed
	        descriptionBox.style.setProperty('height', `${containerHeight}px`, 'important');
	        descriptionBox.style.setProperty('max-height', `${containerHeight}px`, 'important');
	        descriptionBox.style.setProperty('overflow-y', idealHeight > maxHeight ? 'auto' : 'visible', 'important'); // Scroll if content is too long
	        descriptionBox.style.setProperty('bottom', '0px', 'important'); // Stay at bottom, grow upward
	        descriptionBox.style.setProperty('top', 'auto', 'important');
	        descriptionBox.style.setProperty('left', '20px', 'important');
	        descriptionBox.style.setProperty('right', '80px', 'important');
	        descriptionBox.style.setProperty('background', 'rgba(0,0,0,0.8)', 'important');
	        descriptionBox.style.setProperty('backdrop-filter', 'blur(15px)', 'important');
	        descriptionBox.style.setProperty('border-radius', '12px', 'important');
	        descriptionBox.style.setProperty('border', '1px solid rgba(255,255,255,0.2)', 'important');

	        // Style the description text - let it flow naturally or scroll if too long
	        if (idealHeight > maxHeight) {
	          // Text is very long, make it scrollable
	          description.style.setProperty('max-height', `${containerHeight - 30}px`, 'important');
	          description.style.setProperty('overflow-y', 'auto', 'important');
	          description.style.setProperty('height', `${containerHeight - 30}px`, 'important');
	        } else {
	          // Text fits naturally, no scrolling needed
	          description.style.setProperty('max-height', 'none', 'important');
	          description.style.setProperty('overflow-y', 'visible', 'important');
	          description.style.setProperty('height', 'auto', 'important');
	        }
	        description.style.setProperty('padding', '15px', 'important');
	        description.style.setProperty('line-height', '1.6', 'important');

	        console.log(`📏 Dynamic container: natural=${naturalTextHeight}px, container=${containerHeight}px, scrollable=${idealHeight > maxHeight}`);

	        // Add click handler to less button
	        const lessBtn = description.querySelector('.instagram-more-btn');
	        if (lessBtn) {
	          lessBtn.addEventListener('click', function(e) {
	            e.preventDefault();
	            e.stopPropagation();
	            e.stopImmediatePropagation();
	            isExpanded = false;
	            truncateText();
	          });
	        }

	        // Retry mechanism: If expansion seems incomplete, try again after a short delay
	        setTimeout(() => {
	          if (description.scrollHeight > description.clientHeight + 10) {
	            console.log('🔄 Description seems cut off, retrying expansion...');
	            description.style.setProperty('max-height', 'none', 'important');
	            description.style.setProperty('height', 'auto', 'important');
	            descriptionBox.style.setProperty('max-height', '60vh', 'important');
	            descriptionBox.style.setProperty('overflow-y', 'auto', 'important');
	          }
	        }, 100);
	      }

	      // Initialize with truncated text
	      truncateText();
	    } else {
	      // Text is short, no truncation needed - but DON'T add expanded class automatically
	      console.log('📝 Text is short (', words.length, 'words), no automatic styling applied');
	      // Just leave it as is - no automatic classes that could cause jumping
	    }
	    console.log('✅ Instagram-style description initialization complete');
	  }

	  // Global function to reinitialize all descriptions (for debugging/fixing)
	  function reinitializeAllDescriptions() {
	    console.log('🔄 Reinitializing all descriptions...');
	    const allSlides = document.querySelectorAll('.shorts-slide');
	    let count = 0;

	    allSlides.forEach(slide => {
	      const description = slide.querySelector('.shorts-description');
	      const descriptionBox = slide.querySelector('.shorts-description-box');
	      const publisher = slide.querySelector('.shorts-publisher');

	      if (description && descriptionBox && publisher) {
	        // Force reset any existing readmore or expanded states
	        if (description.hasAttribute('data-readmore')) {
	          $(description).readmore('destroy');
	        }
	        initInstagramStyleDescription(description, descriptionBox, publisher);
	        count++;
	      }
	    });

	    console.log('✅ Reinitialized', count, 'descriptions');
	    return count;
	  }

	  // Make it available globally for debugging
	  window.reinitializeAllDescriptions = reinitializeAllDescriptions;

	  // Auto-check for uninitialized descriptions every 2 seconds
	  setInterval(() => {
	    const uninitializedDescriptions = document.querySelectorAll('.shorts-description:not(.collapsed):not(.expanded)');
	    if (uninitializedDescriptions.length > 0) {
	      console.log('🔄 Found', uninitializedDescriptions.length, 'uninitialized descriptions, fixing...');
	      uninitializedDescriptions.forEach(description => {
	        const slide = description.closest('.shorts-slide');
	        if (slide) {
	          const descriptionBox = slide.querySelector('.shorts-description-box');
	          const publisher = slide.querySelector('.shorts-publisher');
	          if (descriptionBox && publisher) {
	            initInstagramStyleDescription(description, descriptionBox, publisher);
	          }
	        }
	      });
	    }

	    // Fallback: ensure all description boxes are visible after 3 seconds
	    const hiddenDescriptions = document.querySelectorAll('#shortsViewer .shorts-description-box:not(.positioned)');
	    if (hiddenDescriptions.length > 0) {
	      console.log('🔄 Found', hiddenDescriptions.length, 'hidden descriptions, making visible...');
	      hiddenDescriptions.forEach(descriptionBox => {
	        descriptionBox.classList.add('positioned');
	      });
	    }
	  }, 2000);

	  // Position description box right under publisher
	  function ensureDescriptionPositioning() {
	    const allSlides = document.querySelectorAll('.shorts-slide');
	    allSlides.forEach(slide => {
	      const publisher = slide.querySelector('.shorts-publisher');
	      const descriptionBox = slide.querySelector('.shorts-description-box');
	      const description = slide.querySelector('.shorts-description.publication-text');

	      if (publisher && descriptionBox && description) {
	        // Get publisher's position and calculate where description should start
	        const publisherRect = publisher.getBoundingClientRect();
	        const slideRect = slide.getBoundingClientRect();

	        // Calculate the top position for description (publisher bottom + 10px gap)
	        const publisherBottomRelative = publisherRect.bottom - slideRect.top;
	        const descriptionTop = publisherBottomRelative + 10;

	        // Ensure enough space for description content (minimum 60px for collapsed, more for expanded)
	        const minHeight = description.classList.contains('expanded') ? 100 : 60;
	        const availableHeight = Math.max(minHeight, slideRect.height - descriptionTop - 10);

	        // Calculate proper width to avoid going across screen
	        const isMobile = window.innerWidth <= 768;
	        const rightMargin = isMobile ? '80px' : '80px';
	        const maxWidth = isMobile ? 'calc(100vw - 100px)' : 'calc(100% - 100px)';

	        descriptionBox.style.setProperty('position', 'absolute', 'important');
	        descriptionBox.style.setProperty('left', '20px', 'important');
	        descriptionBox.style.setProperty('right', rightMargin, 'important');
	        descriptionBox.style.setProperty('top', descriptionTop + 'px', 'important');
	        descriptionBox.style.setProperty('bottom', 'auto', 'important');
	        descriptionBox.style.setProperty('min-height', minHeight + 'px', 'important');

	        // Set appropriate max-height for both collapsed and expanded descriptions
	        if (description.classList.contains('expanded')) {
	          // For expanded descriptions, use more height but still within reasonable bounds
	          const expandedMaxHeight = Math.min(availableHeight * 2, window.innerHeight * 0.6);
	          descriptionBox.style.setProperty('max-height', expandedMaxHeight + 'px', 'important');
	          descriptionBox.style.setProperty('overflow-y', 'auto', 'important');
	        } else {
	          descriptionBox.style.setProperty('max-height', availableHeight + 'px', 'important');
	          descriptionBox.style.setProperty('overflow-y', 'auto', 'important');
	        }

	        descriptionBox.style.setProperty('z-index', '25', 'important');
	        descriptionBox.style.setProperty('max-width', maxWidth, 'important');
	        descriptionBox.style.setProperty('width', 'auto', 'important');
	        descriptionBox.style.setProperty('background', 'transparent', 'important');
	        descriptionBox.style.setProperty('padding', '0', 'important');
	        descriptionBox.style.setProperty('display', 'block', 'important');
	        descriptionBox.style.setProperty('word-wrap', 'break-word', 'important');
	        descriptionBox.style.setProperty('overflow-wrap', 'break-word', 'important');

	        // Ensure description is visible and properly positioned
	        description.style.removeProperty('transform');
	        description.style.setProperty('position', 'relative', 'important');
	        description.style.setProperty('top', '0', 'important');
	        description.style.setProperty('margin-top', '0', 'important');
	        description.style.setProperty('display', 'block', 'important');
	        description.style.setProperty('visibility', 'visible', 'important');
	        description.style.setProperty('opacity', '1', 'important');

	        // Mark description box as positioned so it becomes visible
	        descriptionBox.classList.add('positioned');
	        description.style.setProperty('width', '100%', 'important');
	        // Only apply font-size if description is expanded (not during initial loading)
	        if (description.classList.contains('expanded')) {
	          description.style.setProperty('font-size', '16px', 'important');
	          description.style.setProperty('line-height', '1.4', 'important');
	          // Let the container handle the height limits, not the text itself
	          description.style.setProperty('max-height', 'none', 'important');
	          description.style.setProperty('overflow', 'visible', 'important');
	        } else {
	          // Keep collapsed styling with increased font size
	          description.style.setProperty('font-size', '15px', 'important');
	          description.style.setProperty('line-height', '1.4', 'important');
	        }

	        console.log('🔧 Positioned description under publisher:', {
	          publisherBottom: publisherBottomRelative + 'px',
	          descriptionTop: descriptionTop + 'px',
	          minHeight: minHeight + 'px',
	          availableHeight: availableHeight + 'px',
	          isExpanded: description.classList.contains('expanded'),
	          gap: '10px',
	          text: description.textContent?.substring(0, 30)
	        });
	      }
	    });
	  }

	  // Apply initial positioning
	  ensureDescriptionPositioning();

	  // Watch for loading class removal to immediately position descriptions
	  const shortsViewer = document.getElementById('shortsViewer');
	  if (shortsViewer) {
	    const observer = new MutationObserver((mutations) => {
	      mutations.forEach((mutation) => {
	        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
	          const target = mutation.target;
	          if (target.id === 'shortsViewer' && !target.classList.contains('loading')) {
	            console.log('🎯 Loading class removed, positioning descriptions immediately');
	            setTimeout(() => {
	              ensureDescriptionPositioning();
	            }, 50);
	          }
	        }
	      });
	    });
	    observer.observe(shortsViewer, { attributes: true, attributeFilter: ['class'] });
	  }

	  // Debug function to check if descriptions exist
	  window.checkDescriptions = function() {
	    const descriptions = document.querySelectorAll('.shorts-description.publication-text');
	    console.log('📋 Found descriptions:', descriptions.length);
	    descriptions.forEach((desc, i) => {
	      console.log(`Description ${i}:`, {
	        text: desc.textContent?.substring(0, 50),
	        display: getComputedStyle(desc).display,
	        visibility: getComputedStyle(desc).visibility,
	        opacity: getComputedStyle(desc).opacity,
	        position: getComputedStyle(desc).position,
	        top: getComputedStyle(desc).top
	      });
	    });
	  };

	  // Run check after a short delay
	  setTimeout(() => {
	    window.checkDescriptions();
	  }, 2000);

	  // Run positioning check periodically for dynamic content
	  setInterval(ensureDescriptionPositioning, 1000);

	  // Handle window resize events
	  window.addEventListener('resize', () => {
	    setTimeout(ensureDescriptionPositioning, 100);
	  });

	  // Handle orientation change events
	  window.addEventListener('orientationchange', () => {
	    setTimeout(ensureDescriptionPositioning, 200);
	  });

	  // iOS-specific positioning enforcement
	  function forceIOSPositioning() {
	    if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
	      // Use the same positioning logic as the main function
	      ensureDescriptionPositioning();
	    }
	  }

	  // Run iOS-specific positioning more frequently
	  if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
	    setInterval(forceIOSPositioning, 500);
	    // Also run on orientation change
	    window.addEventListener('orientationchange', () => {
	      setTimeout(forceIOSPositioning, 100);
	    });

	    // MutationObserver to catch inline style changes and override them
	    const observer = new MutationObserver((mutations) => {
	      mutations.forEach((mutation) => {
	        if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
	          const target = mutation.target;
	          if (target.classList.contains('shorts-description') && target.classList.contains('publication-text')) {
	            // Re-apply positioning when styles change
	            setTimeout(() => {
	              ensureDescriptionPositioning();
	            }, 10);
	          }
	        }
	      });
	    });

	    // Observe all shorts slides for style changes
	    const observeSlides = () => {
	      document.querySelectorAll('.shorts-slide').forEach(slide => {
	        observer.observe(slide, {
	          attributes: true,
	          attributeFilter: ['style'],
	          subtree: true
	        });
	      });
	    };

	    // Start observing
	    observeSlides();
	    // Re-observe when new slides are added
	    setInterval(observeSlides, 2000);
	  }

	  // Debug function to check current description positioning
	  window.debugDescriptions = function() {
	    console.log('🔍 Debugging all description positioning...');
	    const allSlides = document.querySelectorAll('.shorts-slide');

	    allSlides.forEach((slide, index) => {
	      const description = slide.querySelector('.shorts-description');
	      if (description) {
	        console.log(`📝 Slide ${index}:`);
	        console.log('   - Text length:', description.textContent.length);
	        console.log('   - Word count:', description.textContent.split(' ').length);
	        console.log('   - Has more button:', !!description.querySelector('.instagram-more-btn'));
	        console.log('   - Classes:', description.className);
	        console.log('   - HTML:', description.innerHTML.substring(0, 100) + '...');
	      }
	    });
	  };

	  // Test function to manually click the first "more" button
	  window.testMoreButton = function() {
	    const moreBtn = document.querySelector('.instagram-more-btn');
	    if (moreBtn) {
	      console.log('🧪 Testing more button click...');
	      moreBtn.click();
	      return 'More button clicked!';
	    } else {
	      console.log('❌ No more button found');
	      return 'No more button found';
	    }
	  };

	

  // Helper function to create complete HTML structure from API data
	  function createVideoSlideFromApiData(videoData, index) {
	    console.log('🎬 Creating video slide from API data:', videoData.id);

	    const slide = document.createElement('div');
	    slide.className = 'shorts-slide';
	    slide.dataset.videoIndex = index;

	    // Get video information
	    const videoId = `shorts-${videoData.id}`;
	    const postId = videoData.post_id;
	    const videoSrc = videoData.src;
	    const posterSrc = videoData.poster || '';

	    // User information
	    const userName = videoData.user_name || '';
	    const userUsername = videoData.user_username || '';
	    const userAvatar = videoData.user_avatar || '';
	    const userId = videoData.user_id;
	    const userVerified = videoData.user_verified || false;

	    // Post data
	    const postText = videoData.post_text || '';
	    const postTime = videoData.post_time || '';
	    const postType = videoData.post_type || 'video';
	    const likesCount = videoData.likes_count || 0;
	    const commentsCount = videoData.comments_count || 0;
	    const repostsCount = videoData.reposts_count || 0;

	    // User states
	    const isLiked = videoData.is_liked || false;
	    const isBookmarked = videoData.is_bookmarked || false;
	    const isReposted = videoData.is_reposted || false;
	    const isFollowing = videoData.is_following || false;
	    const isOwner = videoData.is_owner || false;
	    const canEdit = videoData.can_edit || false;
	    const canDelete = videoData.can_delete || false;
	    const isAdmin = videoData.is_admin || false;
	    const isFreePost = videoData.is_free_post || false;
	    const contMonetization = videoData.cont_monetization || false;
	    const profilePinned = videoData.profile_pinned || false;
	    const adminPinned = videoData.admin_pinned || false;

	    // Create video element with MP4 source and instant aspect ratio detection
	    const muteAttribute = window.__globalMute !== false ? 'muted' : ''; // Respect global mute state
	    console.log('🔊 API VIDEO CREATION: Global mute state:', window.__globalMute, 'Mute attribute:', muteAttribute);

	    // Pre-calculate aspect ratio from video metadata if available
	    let videoWidth = videoData.video_width || videoData.width || null;
	    let videoHeight = videoData.video_height || videoData.height || null;
	    let aspectRatioClass = 'aspect-tall'; // Default to tall for shorts (most common)
	    let dataVideoRatio = '16:9'; // Default

	    // Check cache first for instant aspect ratio
	    const cachedMetadata = window.videoMetadataCache && window.videoMetadataCache[videoData.id];
	    if (cachedMetadata) {
	      videoWidth = cachedMetadata.width;
	      videoHeight = cachedMetadata.height;
	      aspectRatioClass = cachedMetadata.aspectClass;
	      dataVideoRatio = `${videoWidth}:${videoHeight}`;
	      console.log(`⚡ Using cached aspect ratio for ${videoId}: ${cachedMetadata.aspectRatio.toFixed(2)} (${aspectRatioClass})`);
	    } else if (videoWidth && videoHeight) {
	      const aspectRatio = videoWidth / videoHeight;
	      dataVideoRatio = `${videoWidth}:${videoHeight}`;

	      if (aspectRatio > 1.3) {
	        aspectRatioClass = 'aspect-wide'; // Landscape
	      } else if (aspectRatio < 0.8) {
	        aspectRatioClass = 'aspect-tall'; // Portrait
	      } else {
	        aspectRatioClass = 'aspect-square'; // Square-ish
	      }
	      console.log(`⚡ Pre-calculated aspect ratio for ${videoId}: ${aspectRatio.toFixed(2)} (${aspectRatioClass})`);

	      // Debug specific case
	      if (dataVideoRatio === '16:9' && aspectRatioClass !== 'aspect-wide') {
	        console.error(`🚨 BUG: 16:9 video ${videoId} got ${aspectRatioClass} instead of aspect-wide! Calculation: ${videoWidth}/${videoHeight} = ${aspectRatio}`);
	      }

	      // Cache for future use
	      cacheVideoMetadata(videoData);
	    } else {
	      console.log(`📐 API: No video dimensions available, using default aspect-tall for ${videoId}`);
	    }

	    // Ensure we always have an aspect ratio class
	    if (!aspectRatioClass) {
	      aspectRatioClass = 'aspect-tall';
	      console.log(`📐 API: Fallback to aspect-tall for ${videoId}`);
	    }

	    // Debug: Show if this should be a full-screen tall video
	    if (aspectRatioClass === 'aspect-tall') {
	      console.log(`📱 FULL SCREEN: Video ${videoId} will take full screen height on mobile`);
	    }

	    const videoHTML = `
	      <div class="shorts-video-wrapper" style="position: relative; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center;">
	        <video id="${videoId}"
	               data-original-id="${videoData.id}"
	               data-video-ratio="${dataVideoRatio}"
	               data-aspect-precalculated="${videoWidth && videoHeight ? 'true' : 'false'}"
	               playsinline
	               ${muteAttribute}
	               loop
	               poster="${posterSrc}"
	               src="${videoSrc}"
	               preload="${index === 0 ? 'metadata' : 'none'}"
	               class="shorts-video ${aspectRatioClass}"
	               style="background:#000;"
	>
	        </video>
	        <div class="pause-indicator" style="display: none;"></div>
	      </div>
	    `;

	    // Create complete post controls structure matching timeline design
	    const likeButtonClass = isLiked ? 'ctrls-item liked' : 'ctrls-item';
	    const repostButtonClass = isReposted ? 'ctrls-item reposted' : 'ctrls-item';

	    const controlsHTML = `
	      <div class="shorts-actions">
	        <div class="post-data-layout__controls">
	          <button class="${likeButtonClass}" onclick="SMColibri.like_post('${postId}', this);">
	            <span class="ctrls-item__icon">
	              ${ICONS.thumb_like}
	            </span>
	            <span class="num" data-an="likes-count">${likesCount}</span>
	          </button>

	          <button class="ctrls-item">
	            <a class="ctrls-item__inner-link" href="<?php echo cl_link('thread'); ?>/${postId}" data-spa="true">
	              <span class="ctrls-item__icon">
	                ${ICONS.comment}
	              </span>
	              <span class="num">${commentsCount}</span>
	            </a>
	          </button>

	          <button onclick="SMColibri.repost('${postId}', this);" class="${repostButtonClass}" data-an="repost-ctrl">
	            <span class="ctrls-item__icon">
	              ${ICONS.repeat}
	            </span>
	            <span class="num" data-an="reposts-count">${repostsCount}</span>
	          </button>

	          <button class="ctrls-item" onclick="SMColibri.share_post('<?php echo cl_link('thread'); ?>/${postId}','<?php echo urlencode(cl_link('thread')); ?>/${postId}');">
	            <span class="ctrls-item__icon">
	              ${ICONS.share}
	            </span>
	          </button>

	          ${createDropdownMenuHTML(postId, isOwner, isAdmin, isBookmarked, videoData)}
	        </div>
	      </div>
	    `;

	    // Create publisher info matching timeline design
	    const userProfileUrl = `<?php echo cl_link('profile'); ?>/${userUsername}`;
	    const followButtonText = isFollowing ? 'Following' : 'Follow';
	    const followButtonClass = isFollowing ? 'btn-follow following' : 'btn-follow';
	    // Create verification badge HTML if user is verified using the same system as timeline
	    const verificationBadgeHTML = userVerified ? `<span class="user-name-holder__badge"><?php echo cl_icon("verified_user_badge"); ?></span>` : '';
	    // Post text is now already processed by the API with proper hashtags, mentions, and URLs
	    const descriptionHTML = postText ? `<div class="shorts-description-box"><div class="shorts-description publication-text" data-post-text="shorts-${postId}">${postText}</div></div>` : '';
	    const publisherHTML = `
	      <div class="shorts-publisher">
	        <a href="${userProfileUrl}" data-spa="true" class="avatar-holder">
	          <img src="${userAvatar}" alt="Avatar">
	        </a>
	        <a href="${userProfileUrl}" data-spa="true" class="username">
	          <span class="user-name-holder">
	            <span class="user-name-holder__name">${userName}</span>
	            ${verificationBadgeHTML}
	          </span>
	        </a>
	        ${!isOwner ? `<button class="${followButtonClass}" onclick="SMColibri.follow(this);" data-action="${isFollowing ? 'unfollow' : 'follow'}" data-user-name="${userName}" data-id="${userId}">${followButtonText}</button>` : ''}
	      </div>
	      ${descriptionHTML}
	    `;

	    // Combine all elements
	    const spinner = '<div class="loading-spinner"></div>';
	    slide.innerHTML = spinner + videoHTML + controlsHTML + publisherHTML;

	    // Immediately position description to prevent center flash
	    setTimeout(() => {
	      const descriptionBox = slide.querySelector('.shorts-description-box');
	      if (descriptionBox) {
	        // Force immediate bottom positioning
	        descriptionBox.style.setProperty('position', 'absolute', 'important');
	        descriptionBox.style.setProperty('bottom', '0', 'important');
	        descriptionBox.style.setProperty('left', '20px', 'important');
	        descriptionBox.style.setProperty('right', '80px', 'important');
	        descriptionBox.style.setProperty('top', 'auto', 'important');
	        descriptionBox.style.setProperty('transform', 'none', 'important');
	        descriptionBox.classList.add('positioned');
	        console.log('🎯 Immediately positioned description for slide');
	      }
	    }, 0);

	    // Setup aspect ratio detection for the video
	    setTimeout(() => {
	      const video = slide.querySelector('video');
	      if (video) {
	        setupAspectRatioDetection(video);
	      }
	    }, 0);

	    // Initialize Instagram-style description expansion
	    setTimeout(() => {
	      const description = slide.querySelector('.shorts-description');
	      const descriptionBox = slide.querySelector('.shorts-description-box');
	      const publisher = slide.querySelector('.shorts-publisher');

	      if (description && descriptionBox && publisher) {
	        // Force reset any existing readmore or expanded states
	        if (description.hasAttribute('data-readmore')) {
	          $(description).readmore('destroy');
	        }
	        initInstagramStyleDescription(description, descriptionBox, publisher);
	      }
	    }, 200);

	    // Ensure transforms are applied to any expanded or collapsed descriptions
	    setTimeout(() => {
	      ensureDescriptionTransforms();
	    }, 300);

	    return slide;
	  }

	  // Helper function to create dropdown menu HTML - EXACT MATCH TO TIMELINE
	  function createDropdownMenuHTML(postId, isOwner, isAdmin, isBookmarked, videoData) {
	    const threadUrl = videoData.url || `<?php echo cl_link('thread'); ?>/${postId}`;
	    const owner = videoData.owner || {};
	    const contMonetization = owner.cont_monetization || false;
	    const isFreePost = videoData.is_free_post || false;
	    const postType = videoData.post_type || 'video';
	    const profilePinned = videoData.profile_pinned || false;
	    const adminPinned = videoData.admin_pinned || false;
	    const videoSrc = videoData.src || '';
	    const canEdit = videoData.can_edit || false;
	    const canDelete = videoData.can_delete || false;
	    const hasSaved = videoData.has_saved || isBookmarked;

	    return `
	      <div class="dropleft dropdown">
	        <button class="ctrls-item dropdown-toggle" data-toggle="dropdown" type="button">
	          <span class="ctrls-item__icon">
	            ${ICONS.more_horiz}
	          </span>
	        </button>
	        <div class="dropdown-menu dropdown-icons">
	          ${isOwner ? `
	            ${contMonetization ? `
	              ${isFreePost ? `
	                <a class="dropdown-item" href="javascript:void(0);" onclick="SMColibri.freefy_post('${postId}', this);">
	                  <span class="flex-item dropdown-item-icon">
	                    ${ICONS.lock_closed}
	                  </span>
	                  <span class="flex-item" data-itag="text">
	                    Close free access
	                  </span>
	                </a>
	                <div class="dropdown-divider"></div>
	              ` : `
	                <a class="dropdown-item" href="javascript:void(0);" onclick="SMColibri.freefy_post('${postId}', this);">
	                  <span class="flex-item dropdown-item-icon">
	                    ${ICONS.lock_open}
	                  </span>
	                  <span class="flex-item" data-itag="text">
	                    Open free access
	                  </span>
	                </a>
	                <div class="dropdown-divider"></div>
	              `}
	            ` : ''}
	            ${postType === 'poll' ? `
	              <a onclick="SMColibri.stop_poll(${postId});" class="dropdown-item" href="javascript:void(0);">
	                <span class="flex-item dropdown-item-icon">
	                  ${ICONS.poll}
	                </span>
	                <span class="flex-item">
	                  Stop this poll
	                </span>
	                <span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
	                  ${ICONS.timer_off}
	                </span>
	              </a>
	              <div class="dropdown-divider"></div>
	            ` : ''}
	          ` : ''}

	          <a class="dropdown-item" href="${threadUrl}" data-spa="true">
	            <span class="flex-item dropdown-item-icon">
	              ${ICONS.open}
	            </span>
	            <span class="flex-item">Show thread</span>
	          </a>

	          ${postType === 'document' ? `
	            <div class="dropdown-divider"></div>
	            <a download="${videoData.filename || 'document'}" class="dropdown-item" href="${videoSrc}">
	              <span class="flex-item dropdown-item-icon">
	                ${ICONS.arrow_download}
	              </span>
	              <span class="flex-item">Download document</span>
	              <span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
	                ${ICONS.document}
	              </span>
	            </a>
	          ` : ''}

	          ${postType === 'video' && videoSrc ? `
	            <div class="dropdown-divider"></div>
	            <a download="<?php echo cl_strf('%s-Video-Publication', $cl['config']['name']); ?>" class="dropdown-item" href="${videoSrc}">
	              <span class="flex-item dropdown-item-icon">
	                ${ICONS.arrow_download}
	              </span>
	              <span class="flex-item">Download video</span>
	              <span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
	                ${ICONS.video}
	              </span>
	            </a>
	          ` : ''}

	          ${postType === 'audio' && videoSrc ? `
	            <div class="dropdown-divider"></div>
	            <a download="<?php echo cl_strf('%s-Audio-Publication', $cl['config']['name']); ?>" class="dropdown-item" href="${videoSrc}">
	              <span class="flex-item dropdown-item-icon">
	                ${ICONS.arrow_download}
	              </span>
	              <span class="flex-item">Download audio</span>
	              <span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
	                ${ICONS.music}
	              </span>
	            </a>
	          ` : ''}

	          <div class="dropdown-divider"></div>
	          ${canEdit ? `
	            <a onclick="SMColibri.edit_post('${postId}');" class="dropdown-item" href="javascript:void(0);">
	              <span class="flex-item dropdown-item-icon">
	                ${ICONS.note_edit}
	              </span>
	              <span class="flex-item">Edit post</span>
	              <span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
	                ${ICONS.text}
	              </span>
	            </a>
	            <div class="dropdown-divider"></div>
	          ` : ''}

	          ${canDelete ? `
	            <a onclick="SMColibri.delete_post('${postId}');" class="dropdown-item" href="javascript:void(0);">
	              <span class="flex-item dropdown-item-icon">
	                ${ICONS.delete}
	              </span>
	              <span class="flex-item">Delete</span>
	            </a>
	            <div class="dropdown-divider"></div>
	          ` : ''}

	          <a onclick="SMColibri.show_likes('${postId}');" class="dropdown-item" href="javascript:void(0);">
	            <span class="flex-item dropdown-item-icon">
	              ${ICONS.thumb_like}
	            </span>
	            <span class="flex-item">Show likes</span>
	            <span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
	              ${ICONS.users_list}
	            </span>
	          </a>
	          <div class="dropdown-divider"></div>

	          <a class="dropdown-item" href="javascript:void(0);" onclick="SMColibri.bookmark_post('${postId}', this);">
	            <span class="flex-item dropdown-item-icon">
	              ${ICONS.bookmark}
	            </span>
	            <span class="flex-item" data-itag="text">
	              ${hasSaved ? 'Unbookmark' : 'Bookmark'}
	            </span>
	          </a>

	          <a data-clipboard-text="${threadUrl}" class="dropdown-item clip-board-copy" href="javascript:void(0);">
	            <span class="flex-item dropdown-item-icon">
	              ${ICONS.clipboard_link}
	            </span>
	            <span class="flex-item">Copy link</span>
	          </a>

	          ${!isOwner ? `
	            <div class="dropdown-divider"></div>
	            <a onclick="SMColibri.report_post(${postId});" class="dropdown-item" href="javascript:void(0);">
	              <span class="flex-item dropdown-item-icon">
	                ${ICONS.flag}
	              </span>
	              <span class="flex-item">Report post</span>
	              <span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
	                ${ICONS.shield_error}
	              </span>
	            </a>
	          ` : ''}

	          ${isOwner ? `
	            <div class="dropdown-divider"></div>
	            <a onclick="SMColibri.pin_profile_post(${postId}, this);" class="dropdown-item" href="javascript:void(0);">
	              <span class="flex-item dropdown-item-icon">
	                ${ICONS.pin}
	              </span>
	              <span class="flex-item" data-itag="text">
	                ${profilePinned ? 'Unpin from my profile' : 'Pin to my profile'}
	              </span>
	              <span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
	                ${ICONS.note}
	              </span>
	            </a>
	          ` : ''}

	          ${isAdmin ? `
	            <div class="dropdown-divider"></div>
	            <a onclick="SMColibri.pin_admin_post(${postId}, this);" class="dropdown-item" href="javascript:void(0);">
	              <span class="flex-item dropdown-item-icon">
	                ${ICONS.pin}
	              </span>
	              <span class="flex-item" data-itag="text">
	                ${adminPinned ? 'Unpin post from feeds' : 'Pin post to feeds'}
	              </span>
	              <span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
	                ${ICONS.alert_urgent}
	              </span>
	            </a>
	          ` : ''}
	        </div>
	      </div>
	    `;
	  }

	  // Function to load clicked video from API
	  function loadClickedVideoFromAPI(videoId) {
	    console.log('🎯 Loading clicked video from API, video ID:', videoId);

	    // Extract post ID from video ID (remove 'video-player-' prefix)
	    const postId = videoId.replace('video-player-', '');

	    const apiUrl = '<?php echo cl_link("native_api/home/<USER>"); ?>';
	    console.log('🎯 Clicked Video API URL:', apiUrl);

	    return $.ajax({
	      url: apiUrl,
	      type: 'GET',
	      dataType: 'json',
	      timeout: 10000,
	      data: {
	        clicked_video_id: postId,
	        offset: 0,
	        exclude_ids: ''
	      }
	    }).done(function(data) {
	      console.log('✅ Clicked video API request completed');
	      console.log('📡 API Response:', data);

	      if (data && data.status === 200 && data.videos && data.videos.length > 0) {
	        console.log('🎯 Found clicked video in API response');
	        const clickedVideoData = data.videos[0]; // First video should be the clicked one

	        // Create slide from API data
	        const slide = createVideoSlideFromApiData(clickedVideoData, 0);
	        if (slide) {
	          overlay.appendChild(slide);
	          slide.dataset.postId = clickedVideoData.post_id;
	          window.shortsState.loadedVideos.push(slide);
	          console.log('🎯 Added clicked video from API:', clickedVideoData.id);

	          // Hide spinner for initial video immediately
	          const firstSpinner = slide.querySelector('.loading-spinner');
	          if (firstSpinner) {
	            firstSpinner.style.display = 'none';
	            console.log('Hidden spinner for clicked video immediately');
	          }

	          // CRITICAL: Start the clicked video immediately since it's now created
	          setTimeout(() => {
	            const clickedVideo = slide.querySelector('video');
	            if (clickedVideo && window.shortsAutoplayManager) {
	              console.log('🚀 TIKTOK: IMMEDIATE START for clicked video from API:', clickedVideo.id);
	              // Set correct mute state based on global mute (like themes699)
	              clickedVideo.muted = window.__globalMute !== false;
	              console.log('🔊 API VIDEO: Set mute state to:', clickedVideo.muted, '(global mute:', window.__globalMute, ')');
	              currentPlayingVideo = clickedVideo;

	              // Start playing and ensure unmute takes effect
	              window.shortsAutoplayManager.playVideo(clickedVideo).then(() => {
	                // Double-check mute state after playback starts
	                if (window.__globalMute === false && clickedVideo.muted) {
	                  console.log('🔊 FIXING: Video still muted after play, forcing unmute');
	                  clickedVideo.muted = false;
	                }
	              });
	            } else {
	              console.log('⚠️ TIKTOK: Video or manager not ready for immediate start');
	            }
	          }, 200);

	          // Add any remaining videos from the response (excluding the clicked one)
	          data.videos.slice(1).forEach((videoData, index) => {
	            const slideIndex = window.shortsState.loadedVideos.length;
	            const otherSlide = createVideoSlideFromApiData(videoData, slideIndex);
	            if (otherSlide) {
	              overlay.appendChild(otherSlide);
	              otherSlide.dataset.postId = videoData.post_id;
	              window.shortsState.loadedVideos.push(otherSlide);
	              console.log('🎬 Added additional video from API:', videoData.id);
	            }
	          });

	          // Update load offset
	          window.shortsState.loadOffset += data.videos.length;
	        }
	      } else {
	        console.log('❌ Clicked video not found in API response');
	        throw new Error('Clicked video not found in API response');
	      }
	    }).fail(function(xhr, status, error) {
	      console.log('❌ Failed to load clicked video from API:', error);
	      throw new Error('Failed to load clicked video: ' + error);
	    });
	  }

	  // Cache for video metadata to enable instant aspect ratio detection
	  window.videoMetadataCache = window.videoMetadataCache || {};

	  // Function to cache video metadata for instant aspect ratio detection
	  function cacheVideoMetadata(videoData) {
	    if (videoData.id && (videoData.video_width || videoData.width) && (videoData.video_height || videoData.height)) {
	      const width = videoData.video_width || videoData.width;
	      const height = videoData.video_height || videoData.height;
	      const aspectRatio = width / height;

	      window.videoMetadataCache[videoData.id] = {
	        width: width,
	        height: height,
	        aspectRatio: aspectRatio,
	        aspectClass: aspectRatio > 1.3 ? 'aspect-wide' : (aspectRatio < 0.8 ? 'aspect-tall' : 'aspect-square')
	      };

	      console.log(`💾 Cached metadata for video ${videoData.id}: ${width}x${height} (${aspectRatio.toFixed(2)})`);
	    }
	  }

	  // Function to load more videos dynamically using API data
	  function loadMoreShorts() {
	    console.log('🔍 loadMoreShorts called - isLoading:', window.shortsState.isLoading, 'hasMoreContent:', window.shortsState.hasMoreContent);

	    if (window.shortsState.isLoading || !window.shortsState.hasMoreContent) {
	      console.log('❌ Already loading or no more content available');
	      console.log('   - isLoading:', window.shortsState.isLoading);
	      console.log('   - hasMoreContent:', window.shortsState.hasMoreContent);
	      return Promise.resolve();
	    }

	    // Prevent rapid successive calls
	    const now = Date.now();
	    if (window.shortsState.lastLoadTime && (now - window.shortsState.lastLoadTime) < 1000) {
	      console.log('⏳ Debouncing load request - too soon since last load');
	      return Promise.resolve();
	    }
	    window.shortsState.lastLoadTime = now;

	    console.log('🔄 Loading more shorts with offset:', window.shortsState.loadOffset);
	    window.shortsState.isLoading = true;
	    console.log('✅ Set isLoading to true');

	    // Show loading indicator
	    showLoadingIndicator();

	    const apiUrl = '<?php echo cl_link("native_api/home/<USER>"); ?>';
	    console.log('📡 API URL:', apiUrl);

	    // Collect IDs of already loaded videos to avoid duplicates
	    const loadedVideoIds = window.shortsState.loadedVideos.map(v => v.dataset ? v.dataset.postId : v.id).filter(id => id);

	    console.log('📊 Request data:', {
	      offset: window.shortsState.loadOffset,
	      exclude_ids: loadedVideoIds.join(',')
	    });

	    console.log('🚀 Starting AJAX request...');

	    return $.ajax({
	      url: apiUrl,
	      type: 'GET',
	      dataType: 'json',
	      timeout: 15000, // 15 second timeout
	      data: {
	        offset: window.shortsState.loadOffset,
	        exclude_ids: loadedVideoIds.join(',')
	      }
	    }).done(function(data) {
	      console.log('✅ AJAX request completed successfully');
	      console.log('📡 API Response:', data);
	      console.log('📊 Response analysis:');
	      console.log('   - Status:', data ? data.status : 'undefined');
	      console.log('   - Has videos:', data ? !!data.videos : 'undefined');
	      console.log('   - Videos count:', data && data.videos ? data.videos.length : 0);

	      // Handle API response with video data
	      if (data && data.status == 200 && data.videos && data.videos.length > 0) {
	        console.log('🎬 Processing', data.videos.length, 'videos from API');

	        // Add new video slides from API data
	        data.videos.forEach((videoData, i) => {
	          // Cache video metadata for instant aspect ratio detection
	          cacheVideoMetadata(videoData);

	          const slideIndex = window.shortsState.loadedVideos.length;
	          const slide = createVideoSlideFromApiData(videoData, slideIndex);
	          if (slide) {
	            overlay.appendChild(slide);
	            // Store the slide element with post ID for tracking
	            slide.dataset.postId = videoData.post_id;
	            window.shortsState.loadedVideos.push(slide);
	            console.log('🎬 Added video from API:', videoData.id);
	          } else {
	            console.warn('Failed to create slide for video:', videoData.id);
	          }
	        });

	        // Register new videos with unified manager and set up event handlers
	        setTimeout(() => {
	          const newSlides = overlay.querySelectorAll('.shorts-slide:nth-last-child(-n+' + data.videos.length + ')');

	          newSlides.forEach(slide => {
	            const video = slide.querySelector('video');
	            if (!video) return;

	            video.controls = false;

	            // Ensure Instagram-style description is initialized for dynamic videos
	            const description = slide.querySelector('.shorts-description');
	            const descriptionBox = slide.querySelector('.shorts-description-box');
	            const publisher = slide.querySelector('.shorts-publisher');

	            if (description && descriptionBox && publisher) {
	              console.log('🔄 Re-initializing Instagram-style description for dynamic video');
	              // Force reset any existing readmore or expanded states
	              if (description.hasAttribute('data-readmore')) {
	                $(description).readmore('destroy');
	              }
	              initInstagramStyleDescription(description, descriptionBox, publisher);
	            }
	            video.loop = true;
	            video.muted = window.__globalMute === undefined ? true : window.__globalMute;

	            // Register with unified manager
	            if (window.__unifiedVideoManager && !window.__unifiedVideoManager.allVideos.has(video)) {
	              window.__unifiedVideoManager.registerVideo(video, 'shorts');
	              console.log('Registered new shorts video:', video.id);
	            }

	            // Setup aspect ratio detection for the video
	            setupAspectRatioDetection(video);

	            // Video tap handling is managed by the unified tap detection system
	            console.log('🎯 Video tap handling managed by unified system:', video.id);

	            // Ensure video mute state matches global state
	            video.muted = window.__globalMute !== false;
	            console.log('🔊 Set video mute state:', video.muted, 'based on global:', window.__globalMute);

	            // Pause indicator clicks are handled by the unified double-tap handler

	            // Register with smart preloader
	            if (window.smartVideoPreloader) {
	              window.smartVideoPreloader.addVideo(video);
	            }
	          });

	          // Re-observe new slides for autoplay
	          if (window.reobserveShorts) {
	            window.reobserveShorts();
	            console.log('Re-observed slides for autoplay');
	          }
	        }, 100);
	      } else {
	        console.log('❌ Invalid response or no video data');
	        console.log('Response details:', {
	          hasData: !!data,
	          status: data ? data.status : 'undefined',
	          hasVideos: data ? !!data.videos : 'undefined',
	          videosLength: data && data.videos ? data.videos.length : 0
	        });
	        window.shortsState.hasMoreContent = false;
	      }
	    }).fail(function(xhr, status, error) {
	      console.log('❌ AJAX request failed:');
	      console.log('- Status:', status);
	      console.log('- Error:', error);
	      console.log('- Response Text:', xhr.responseText);
	      window.shortsState.hasMoreContent = false;
	    }).always(function() {
	      window.shortsState.isLoading = false;
	      console.log('✅ Set isLoading to false');
	      hideLoadingIndicator();
	    });

	    // Safety timeout to reset loading state in case something goes wrong
	    setTimeout(() => {
	      if (window.shortsState.isLoading) {
	        console.log('⚠️ Loading state timeout - forcing reset after 3 seconds');
	        window.shortsState.isLoading = false;
	        hideLoadingIndicator();
	        console.log('✅ Forced reset isLoading to false');
	      }
	    }, 3000); // 3 second timeout
	  }

	  // Show loading indicator
	  function showLoadingIndicator() {
	    let indicator = document.getElementById('shortsLoadingIndicator');
	    if (!indicator) {
	      indicator = document.createElement('div');
	      indicator.id = 'shortsLoadingIndicator';
	      indicator.innerHTML = '<div style="width:20px;height:20px;border:2px solid #fff;border-top:2px solid transparent;border-radius:50%;animation:spin 1s linear infinite;margin:0 auto;"></div><div style="color:white;font-size:14px;margin-top:8px;">Loading vlix</div>';
	      indicator.style.cssText = 'position:fixed;bottom:60px;left:50%;transform:translateX(-50%);background:rgba(0,0,0,0.8);color:white;padding:15px 20px;border-radius:25px;z-index:10002;text-align:center;';

	      // Add spin animation
	      const style = document.createElement('style');
	      style.textContent = '@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }';
	      document.head.appendChild(style);

	      overlay.appendChild(indicator);
	    }
	    indicator.style.display = 'block';
	  }

	  // Hide loading indicator
	  function hideLoadingIndicator() {
	    const indicator = document.getElementById('shortsLoadingIndicator');
	    if (indicator) {
	      indicator.style.display = 'none';
	    }
	  }

	  // Video state management for seamless transitions
	  window.videoStateManager = {
	    states: new Map(), // Store video states (currentTime, paused, etc.)

	    // Save current state of a video
	    saveState(video) {
	      try {
	        if (!video) return;

	        const videoId = video.id || (video.dataset && video.dataset.originalId) || 'unknown';
	        if (!videoId || videoId === 'unknown') return;

	        this.states.set(videoId, {
	          currentTime: video.currentTime || 0,
	          paused: video.paused !== false,
	          muted: video.muted !== false,
	          playbackRate: video.playbackRate || 1,
	          volume: video.volume || 1,
	          src: video.src || '',
	          hlsSrc: (video.dataset && video.dataset.hls) || '',
	          timestamp: Date.now()
	        });

	        console.log('Saved video state:', videoId, 'time:', video.currentTime);
	      } catch (e) {
	        console.log('Error saving video state:', e);
	      }
	    },

	    // Restore state to a video
	    restoreState(video) {
	      if (!video) return false;

	      // Try both the video ID and original ID
	      const videoId = video.id || video.dataset?.originalId;
	      if (!videoId) return false;

	      const state = this.states.get(videoId);
	      if (!state) {
	        // Try with original ID if video ID didn't work
	        const originalId = video.dataset?.originalId;
	        if (originalId && originalId !== videoId) {
	          const originalState = this.states.get(originalId);
	          if (originalState) {
	            console.log('Found state using original ID:', originalId);
	            return this.applyState(video, originalState);
	          }
	        }
	        return false;
	      }

	      // Only restore if state is recent (within 5 minutes)
	      if (Date.now() - state.timestamp > 300000) {
	        this.states.delete(videoId);
	        return false;
	      }

	      return this.applyState(video, state);
	    },

	    // Apply state to video element
	    applyState(video, state) {
	      console.log('Restoring video state:', video.id, 'time:', state.currentTime);

	      // Wait for video to be ready before setting currentTime
	      const setTime = () => {
	        if (video.readyState >= 1) {
	          video.currentTime = state.currentTime;
	          video.muted = state.muted;
	          video.playbackRate = state.playbackRate;
	          video.volume = state.volume;
	          console.log('State applied successfully');
	        } else {
	          video.addEventListener('loadedmetadata', () => {
	            video.currentTime = state.currentTime;
	            video.muted = state.muted;
	            video.playbackRate = state.playbackRate;
	            video.volume = state.volume;
	            console.log('State applied after metadata loaded');
	          }, { once: true });
	        }
	      };

	      setTime();
	      return true;
	    },

	    // Clear old states
	    cleanup() {
	      const now = Date.now();
	      for (let [id, state] of this.states.entries()) {
	        if (now - state.timestamp > 300000) { // 5 minutes
	          this.states.delete(id);
	        }
	      }
	    },

	    // Sync current playing video state
	    syncCurrentVideo() {
	      if (window.__unifiedVideoManager && window.__unifiedVideoManager.currentVideo) {
	        this.saveState(window.__unifiedVideoManager.currentVideo);
	      }
	    }
	  };

	  // Auto-sync video states every 5 seconds
	  setInterval(() => {
	    window.videoStateManager.syncCurrentVideo();
	    window.videoStateManager.cleanup();
	  }, 5000);

	  document.addEventListener('click', function(ev){
	    const vid = ev.target.closest(FEED_SEL);
	    console.log('🎬 Video click detected:', vid ? vid.id : 'no video found');
	    if(!vid) return;
	    ev.stopPropagation();
	    console.log('🎬 About to open shorts for video:', vid.id);

	    // Set flag to prevent unauthorized play detection while shorts are active
	    window.__shortsActive = true;
	    console.log('🎬 Set shorts active flag to prevent unauthorized play detection');

	    // Save current video state before opening shorts - SIMPLE VERSION
	    window.clickedVideoState = {
	      id: vid.id,
	      currentTime: vid.currentTime,
	      paused: vid.paused,
	      muted: vid.muted
	    };
	    console.log('Saved clicked video state:', window.clickedVideoState);

	    // User clicked a video intending to watch with sound – unmute globally (like themes699)
	    console.log('🔊 Timeline video clicked - unmuting globally. Current mute state:', window.__globalMute);
	    if(window.toggleGlobalMute){
	      window.toggleGlobalMute(false);
	      console.log('✅ Called toggleGlobalMute(false). New state:', window.__globalMute);
	    } else {
	      // Fallback
	      console.log('⚠️ toggleGlobalMute not found, using fallback');
	      window.__globalMute = false;
	    }

	    console.log('🎬 About to call openOverlay with video:', vid.id);
	    try {
	      openOverlay(vid);
	    } catch (error) {
	      console.error('❌ Error in openOverlay:', error);
	      console.error('Stack trace:', error.stack);
	    }
	  }, true);

	  function openOverlay(initialVid){
	    console.log('=== OPENING SHORTS OVERLAY (DYNAMIC LOADING) ===');
	    console.log('🔧 Initial video:', initialVid ? initialVid.id : 'null');
	    console.log('🔧 Overlay element:', overlay ? 'found' : 'not found');

	    // Reset any previous shorts state for clean start
	    window.shortsVideoStates = {};
	    window.shortsInitialVideo = null;

	    // Initialize shorts state management for dynamic loading
	    window.shortsState = {
	      loadedVideos: [],
	      currentIndex: 0,
	      isLoading: false,
	      hasMoreContent: true, // Always start optimistic
	      loadOffset: 0, // Start from beginning for dynamic content
	      lastLoadTime: 0, // For debouncing load requests
	      initialVideoId: initialVid.id
	    };

	    // Get all feed videos for state management
	    const feedVids = Array.from(document.querySelectorAll(FEED_SEL));

	    // Save states of all timeline videos before pausing (like themes699)
	    feedVids.forEach(v => {
	      window.videoStateManager.saveState(v);
	      v.pause(); // Pause but don't reset time
	      v.currentTime = 0; // Reset to prevent background audio
	      v.muted = true; // Extra safety to prevent audio
	      console.log('🛑 TIKTOK: Paused, reset and muted timeline video:', v.id);
	    });

	    // Clean up any existing unified video manager state (like themes699)
	    if (window.__unifiedVideoManager) {
	      window.__unifiedVideoManager.currentVideo = null;
	      window.__unifiedVideoManager.isScrolling = false;
	      console.log('🧹 TIKTOK: Cleaned up unified video manager state');
	    }

	    // Clear overlay content but preserve structure
	    overlay.innerHTML = '';

	    // Create close button with multiple event handlers for reliability
	    const closeButton = document.createElement('button');
	    closeButton.id = 'shortsCloseBtn';
	    closeButton.innerHTML = `
	      <svg width="24" height="24" fill="none" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
	        <path d="M15 18L9 12L15 6" stroke="#ffffff" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
	      </svg>
	    `;
	    closeButton.style.cssText = 'position:fixed;top:15px;left:15px;z-index:10000;background:transparent;color:#fff;border:none;width:40px;height:40px;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all 0.2s ease;';

	    // Multiple event handlers for reliability
	    closeButton.addEventListener('click', closeOverlay);
	    closeButton.addEventListener('touchend', closeOverlay);
	    closeButton.onclick = closeOverlay;

	    overlay.appendChild(closeButton);

	    // Create modal container
	    const modalContainer = document.createElement('div');
	    modalContainer.id = 'shortsModalContainer';
	    modalContainer.className = 'shorts-modal-container';
	    modalContainer.innerHTML = `
	      <div id="shortsModalBackdrop" class="shorts-modal-backdrop"></div>
	      <div id="shortsModalContent" class="shorts-modal-content"></div>
	    `;
	    overlay.appendChild(modalContainer);

	    // Add navigation hint for both mobile and desktop
	    const hint = document.createElement('div');
	    hint.id = 'swipeHint';

	    // Detect if user is on desktop or mobile
	    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
	    hint.textContent = isMobile ? 'Swipe ↑' : 'Scroll ↑ or use ↑↓ keys';
	    hint.style.cssText = 'position:fixed;bottom:20px;left:50%;transform:translateX(-50%);background:rgba(0,0,0,0.7);color:white;padding:8px 16px;border-radius:20px;font-size:14px;z-index:10001;pointer-events:none;';

	    overlay.appendChild(hint);

	    // Show overlay immediately for responsive feel
	    console.log('🔧 Showing overlay...');
	    overlay.style.display='block';
	    document.body.style.overflow='hidden';
	    console.log('🔧 Overlay display set to:', overlay.style.display);

	    // Add loading class for smooth transition
	    overlay.classList.add('loading');
	    // Remove loading class after descriptions are positioned
	    setTimeout(() => {
	      // Ensure descriptions are positioned before showing them
	      ensureDescriptionPositioning();
	      setTimeout(() => {
	        overlay.classList.remove('loading');
	      }, 100);
	    }, 400);

	    // Start at the top
	    overlay.scrollTo(0, 0);

	    // Load the clicked video from API first, then load more videos
	    loadClickedVideoFromAPI(initialVid.id).then(() => {
	      console.log('✅ Clicked video loaded from API');
	      // Then load more videos for scrolling
	      return loadMoreShorts();
	    }).catch(error => {
	      console.error('❌ Failed to load clicked video from API:', error);
	      // Fallback: create slide from timeline video
	      const initialSlide = createVideoSlide(initialVid, 0);
	      overlay.appendChild(initialSlide);
	      window.shortsState.loadedVideos.push(initialSlide);

	      // Hide spinner for initial video immediately
	      const firstSpinner = initialSlide.querySelector('.loading-spinner');
	      if (firstSpinner) {
	        firstSpinner.style.display = 'none';
	        console.log('Hidden spinner for initial video immediately');
	      }

	      // Load more videos
	      return loadMoreShorts();
	    });

	    // Add debug functions to window for manual testing
	    window.debugShorts = {
	      resetLoading: () => {
	        window.shortsState.isLoading = false;
	        window.shortsState.hasMoreContent = true;
	        console.log('🔧 Debug: Reset loading state');
	      },
	      loadMore: () => {
	        console.log('🔧 Debug: Manual load more');
	        return loadMoreShorts();
	      },
	      getState: () => {
	        console.log('🔧 Debug: Current state:', window.shortsState);
	        return window.shortsState;
	      },
	      testVideoSources: () => {
	        const feedVids = document.querySelectorAll(FEED_SEL);
	        console.log('🔧 Debug: Testing video sources on page:');
	        feedVids.forEach((video, i) => {
	          console.log(`Video ${i}:`, {
	            id: video.id,
	            src: video.src,
	            dataSrc: video.dataset.src,
	            dataHls: video.dataset.hls,
	            sourceElements: Array.from(video.querySelectorAll('source')).map(s => ({type: s.type, src: s.src}))
	          });
	        });
	      },
	      testOpenShorts: () => {
	        const firstVideo = document.querySelector(FEED_SEL);
	        if (firstVideo) {
	          console.log('🔧 Debug: Manually opening shorts with first video:', firstVideo.id);
	          openOverlay(firstVideo);
	        } else {
	          console.log('🔧 Debug: No videos found on page');
	        }
	      }
	    };

	    console.log('🔧 Debug functions created, about to set timeout...');
	    console.log('🔧 Current shortsState before timeout:', window.shortsState);

	    // Immediately load more content for better UX
	    setTimeout(() => {
	      console.log('⏰ Timeout callback executing...');
	      console.log('Loading initial batch of shorts...');
	      console.log('Current videos on page:', feedVids.length);
	      console.log('Starting load offset:', window.shortsState.loadOffset);
	      console.log('🔧 Debug functions available: window.debugShorts.resetLoading(), window.debugShorts.loadMore(), window.debugShorts.getState()');

	      loadMoreShorts().then(() => {
	        console.log('Initial batch loaded successfully');
	        console.log('Total videos now:', window.shortsState.loadedVideos.length);
	      }).catch(err => {
	        console.log('Initial batch loading failed:', err);
	      });
	    }, 500); // Small delay to let initial video settle

	    console.log('🔧 Timeout set, continuing with initialization...');

	    // Also try immediate reset and load for testing
	    setTimeout(() => {
	      console.log('🔧 Emergency reset and load attempt...');
	      window.shortsState.isLoading = false;
	      window.shortsState.hasMoreContent = true;
	      console.log('🔧 State reset, attempting load...');
	      loadMoreShorts().then(() => {
	        console.log('🔧 Emergency load successful!');
	      }).catch(err => {
	        console.log('🔧 Emergency load failed:', err);
	      });
	    }, 1000); // Try after 1 second

	    // Store the initial video for seamless playback
	    window.shortsInitialVideo = {
	      index: 0,
	      originalId: initialVid.id,
	      currentTime: initialVid.currentTime,
	      wasPlaying: !initialVid.paused
	    };

	    // Initialize videos for MP4 playback (skip HLS)
	    console.log('Initializing shorts videos for pure MP4 playback...');

  // Force shorts viewer to actual screen size on mobile
  function forceFullScreenOnMobile() {
    if (window.innerWidth <= 768) {
      const shortsViewer = document.getElementById('shortsViewer');
      if (shortsViewer) {
        const screenWidth = window.screen.width;
        const screenHeight = window.screen.height;
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        console.log('📱 Mobile detected - forcing full screen:', {
          screen: screenWidth + 'x' + screenHeight,
          viewport: viewportWidth + 'x' + viewportHeight
        });

        // Use the larger of viewport or screen dimensions
        const targetWidth = Math.max(viewportWidth, screenWidth);
        const targetHeight = Math.max(viewportHeight, screenHeight);

        shortsViewer.style.width = targetWidth + 'px';
        shortsViewer.style.height = targetHeight + 'px';
        shortsViewer.style.position = 'fixed';
        shortsViewer.style.top = '0';
        shortsViewer.style.left = '0';
        shortsViewer.style.zIndex = '9999';

        console.log('📱 Shorts viewer forced to:', targetWidth + 'x' + targetHeight);
      }
    }
  }

  // All videos now fill container by default via CSS - no special function needed

  // Apply on load and resize
  forceFullScreenOnMobile();
  window.addEventListener('resize', () => {
    forceFullScreenOnMobile();
  });
  window.addEventListener('orientationchange', () => {
    setTimeout(() => {
      forceFullScreenOnMobile();
    }, 100);
  });
	    setTimeout(() => {

	        // Smart video initialization for reliable playback (like themes699)
	        const initializeVideosForPlayback = () => {
	          console.log('Initializing videos for playback...');

	          overlay.querySelectorAll('video').forEach((video, videoIndex) => {
	            const originalId = video.dataset.originalId;

	            // Ensure video is properly set up - EXACTLY like themes699
	            video.muted = window.__globalMute !== false;
	            video.loop = true;
	            video.playsInline = true;

	            // Register with unified manager for proper control
	            if (window.__unifiedVideoManager) {
	              window.__unifiedVideoManager.registerVideo(video, 'shorts');
	            }

	            // Setup aspect ratio detection for the video
	            setupAspectRatioDetection(video);

	            // Restore state if available
	            if (originalId && window.clickedVideoState && window.clickedVideoState.id === originalId) {
	              if (window.clickedVideoState.currentTime > 0) {
	                video.currentTime = window.clickedVideoState.currentTime;
	                console.log('Restored time for video:', originalId, 'to:', video.currentTime);
	              }
	            }

	            // If this is the initial video, ensure it plays (like themes699)
	            if (window.shortsInitialVideo && videoIndex === window.shortsInitialVideo.index) {
	              console.log('Setting up initial video for autoplay');

	              // Mark this video as the initial video that should auto-play
	              video.dataset.isInitialVideo = 'true';
	              console.log('🎯 INSTANT: Marked video as initial video:', video.id);

	              // Force remove poster for immediate display
	              console.log('🎬 INSTANT: Removing poster for immediate video display');
	              video.removeAttribute('poster');
	              video.style.background = '#000';

	              // Force play the initial video through unified manager (like themes699)
	              setTimeout(() => {
	                if (window.__unifiedVideoManager) {
	                  window.__unifiedVideoManager.playVideo(video, 'shorts', 1.0);
	                  console.log('Triggered initial video play through unified manager');
	                } else {
	                  // Fallback direct play
	                  video.play().then(() => {
	                    console.log('Initial video playing directly');
	                    const spinner = video.parentElement.querySelector('.loading-spinner');
	                    if (spinner) spinner.style.display = 'none';
	                  }).catch(e => console.log('Direct play failed:', e));
	                }
	              }, 300);
	            }

	            // Register with smart preloader
	            if (window.smartVideoPreloader) {
	              window.smartVideoPreloader.addVideo(video);
	            }
	          });
	        };

	        // Initialize after a short delay to ensure global unmute has taken effect (like themes699)
	        setTimeout(initializeVideosForPlayback, 200);
	      }, 100);

	    // Data saving indicator removed for better iOS compatibility

	    // Initialize shorts modal system and setup overrides
	    shortsModal.init();
	    setupShortsModalOverrides();

	    // Initialize shorts modal
	    if (window.shortsModal && typeof window.shortsModal.init === 'function') {
	      window.shortsModal.init();
	    }

	    // Sync like states from timeline to shorts viewer
	    syncLikeStatesOnShortsOpen();

	    // Sync bookmark and pin states from timeline to shorts viewer
	    syncStatesOnShortsOpen();

	    initSlides();

	    // Set up mobile-friendly dropdown handling
	    setupMobileDropdownHandling();

	    // Hide hint on first scroll and hide any open dropdowns
	    overlay.addEventListener('scroll', () => {
	      hint.style.display='none';
	      hideAllDropdowns();
	    }, { once:true });
	  }

	  // Track if we're in the middle of a dropdown action to prevent premature closing
	  let dropdownActionInProgress = false;

	  function hideAllDropdowns() {
	    // Don't hide dropdowns if an action is in progress (mobile fix)
	    if (dropdownActionInProgress) {
	      console.log('Dropdown action in progress, skipping hide');
	      return;
	    }

	    // Hide all open dropdown menus in the shorts viewer
	    const openDropdowns = overlay.querySelectorAll('.dropdown-menu.show');
	    openDropdowns.forEach(dropdown => {
	      dropdown.classList.remove('show');
	      // Don't set display:none permanently, let CSS handle it
	    });

	    // Hide dropdowns that might be visible but not have 'show' class
	    const visibleDropdowns = overlay.querySelectorAll('.dropdown-menu[style*="display: block"], .dropdown-menu[style*="display:block"]');
	    visibleDropdowns.forEach(dropdown => {
	      // Remove inline display style to let CSS take over
	      dropdown.style.removeProperty('display');
	    });

	    // Reset dropdown toggles (but preserve their functionality)
	    const dropdownToggles = overlay.querySelectorAll('.dropdown-toggle[aria-expanded="true"], [data-toggle="dropdown"][aria-expanded="true"], [data-bs-toggle="dropdown"][aria-expanded="true"]');
	    dropdownToggles.forEach(toggle => {
	      toggle.setAttribute('aria-expanded', 'false');
	      // Don't remove 'show' class from toggles as it might break functionality
	    });

	    // Reset dropdown containers
	    const dropdownContainers = overlay.querySelectorAll('.dropdown.show');
	    dropdownContainers.forEach(container => {
	      container.classList.remove('show');
	    });
	  }

	  function setupMobileDropdownHandling() {
	    // Add event listeners to dropdown items to prevent premature closing on mobile
	    overlay.addEventListener('touchstart', function(e) {
	      const dropdownItem = e.target.closest('.dropdown-item');
	      if (dropdownItem) {
	        console.log('Dropdown item touched, preventing premature close');
	        dropdownActionInProgress = true;

	        // Clear the flag after a short delay to allow the action to complete
	        setTimeout(() => {
	          dropdownActionInProgress = false;
	        }, 1000);
	      }
	    });

	    // Also handle click events for better compatibility
	    overlay.addEventListener('click', function(e) {
	      const dropdownItem = e.target.closest('.dropdown-item');
	      if (dropdownItem) {
	        console.log('Dropdown item clicked, preventing premature close');
	        dropdownActionInProgress = true;

	        // For onclick handlers, give more time for the modal to open
	        const onclick = dropdownItem.getAttribute('onclick');
	        if (onclick && (onclick.includes('show_likes') || onclick.includes('delete_post') || onclick.includes('report_post'))) {
	          setTimeout(() => {
	            dropdownActionInProgress = false;
	            // Manually hide dropdowns after the modal should have opened
	            hideAllDropdowns();
	          }, 1500);
	        } else {
	          setTimeout(() => {
	            dropdownActionInProgress = false;
	          }, 500);
	        }
	      }
	    });

	    // Prevent scroll events from immediately closing dropdowns on mobile
	    let scrollTimeout;
	    const originalScrollHandler = overlay.onscroll;

	    overlay.addEventListener('scroll', function(e) {
	      // Debounce scroll events to prevent immediate dropdown closing
	      clearTimeout(scrollTimeout);
	      scrollTimeout = setTimeout(() => {
	        if (!dropdownActionInProgress) {
	          hideAllDropdowns();
	        }
	      }, 200);
	    });
	  }

	  // Shorts Modal System
	  const shortsModal = {
	    container: null,
	    backdrop: null,
	    content: null,

	    init() {
	      this.container = document.getElementById('shortsModalContainer');
	      this.backdrop = document.getElementById('shortsModalBackdrop');
	      this.content = document.getElementById('shortsModalContent');

	      // Close modal when clicking backdrop
	      this.backdrop.addEventListener('click', () => this.close());

	      // Close modal on escape key
	      document.addEventListener('keydown', (e) => {
	        if (e.key === 'Escape' && this.isOpen()) {
	          this.close();
	        }
	      });
	    },

	    isOpen() {
	      return this.container && this.container.style.display !== 'none';
	    },

	    show(title, bodyHTML) {
	      if (!this.container) this.init();

	      const modalHTML = `
	        <div class="shorts-modal-header">
	          <h5 class="shorts-modal-title">${title}</h5>
	          <button class="shorts-modal-close" type="button">&times;</button>
	        </div>
	        <div class="shorts-modal-body">
	          ${bodyHTML}
	        </div>
	      `;

	      this.content.innerHTML = modalHTML;
	      this.container.style.display = 'flex';

	      // Add close button event listener
	      const closeBtn = this.content.querySelector('.shorts-modal-close');
	      if (closeBtn) {
	        closeBtn.addEventListener('click', () => this.close());
	      }

	      // Prevent body scroll
	      document.body.style.overflow = 'hidden';
	    },

	    close() {
	      if (!this.container) return;

	      this.content.classList.add('closing');
	      setTimeout(() => {
	        this.container.style.display = 'none';
	        this.content.classList.remove('closing');
	        this.content.innerHTML = '';
	        document.body.style.overflow = '';
	      }, 200);
	    }
	  };

	  // Make shortsModal globally accessible
	  window.shortsModal = shortsModal;

	  // Global function to close shorts modal (fallback)
	  window.closeShortsModal = function() {
	    if (window.shortsModal && typeof window.shortsModal.close === 'function') {
	      window.shortsModal.close();
	    } else {
	      // Fallback: manually hide the modal
	      const container = document.getElementById('shortsModalContainer');
	      if (container) {
	        container.style.display = 'none';
	        document.body.style.overflow = '';
	      }
	    }
	  };

	  // Override modal functions when in shorts viewer
	  function setupShortsModalOverrides() {
	    // Store original functions
	    window._originalSharePost = window.SMColibri?.share_post;
	    window._originalShowLikes = window.SMColibri?.show_likes;
	    window._originalReportPost = window.SMColibri?.report_post;
	    window._originalEditPost = window.SMColibri?.edit_post;
	    window._originalDeletePost = window.SMColibri?.delete_post;
	    window._originalConfirmAction = window.SMColibri?.confirm_action;
	    window._originalInfo = window.SMColibri?.info;
	    window._originalBookmarkPost = window.SMColibri?.bookmark_post;
	    window._originalPinAdminPost = window.SMColibri?.pin_admin_post;
	    window._originalPubReply = window.SMColibri?.pub_reply;

	    // Setup shorts-specific clipboard handling
	    setupShortsClipboardHandling();

	    // Override share_post function
	    if (window.SMColibri && window.SMColibri.share_post) {
	      window.SMColibri.share_post = function(url, encoded_url) {
	        if (overlay.style.display !== 'none') {
	          showShortsShareModal(url, encoded_url);
	        } else {
	          window._originalSharePost.call(this, url, encoded_url);
	        }
	      };
	    }

	    // Override show_likes function
	    if (window.SMColibri && window.SMColibri.show_likes) {
	      window.SMColibri.show_likes = function(id) {
	        if (overlay.style.display !== 'none') {
	          showShortsLikesModal(id);
	        } else {
	          window._originalShowLikes.call(this, id);
	        }
	      };
	    }

	    // Override report_post function
	    if (window.SMColibri && window.SMColibri.report_post) {
	      window.SMColibri.report_post = function(id) {
	        if (overlay.style.display !== 'none') {
	          showShortsReportModal(id);
	        } else {
	          window._originalReportPost.call(this, id);
	        }
	      };
	    }

	    // Override edit_post function
	    if (window.SMColibri && window.SMColibri.edit_post) {
	      window.SMColibri.edit_post = function(id) {
	        if (overlay.style.display !== 'none') {
	          showShortsEditModal(id);
	        } else {
	          window._originalEditPost.call(this, id);
	        }
	      };
	    }

	    // Override delete_post function
	    if (window.SMColibri && window.SMColibri.delete_post) {
	      window.SMColibri.delete_post = function(id) {
	        if (overlay.style.display !== 'none') {
	          showShortsDeleteModal(id);
	        } else {
	          window._originalDeletePost.call(this, id);
	        }
	      };
	    }

	    // Override confirm_action function
	    if (window.SMColibri && window.SMColibri.confirm_action) {
	      window.SMColibri.confirm_action = function(data) {
	        if (overlay.style.display !== 'none') {
	          return showShortsConfirmModal(data);
	        } else {
	          return window._originalConfirmAction.call(this, data);
	        }
	      };
	    }

	    // Override info function
	    if (window.SMColibri && window.SMColibri.info) {
	      window.SMColibri.info = function(title, body) {
	        if (overlay.style.display !== 'none') {
	          showShortsInfoModal(title, body);
	        } else {
	          window._originalInfo.call(this, title, body);
	        }
	      };
	    }

	    // Override bookmark_post function (for feedback messages)
	    if (window.SMColibri && window.SMColibri.bookmark_post) {
	      window.SMColibri.bookmark_post = function(id, element) {
	        if (overlay.style.display !== 'none') {
	          handleShortsBookmark(id, element);
	        } else {
	          window._originalBookmarkPost.call(this, id, element);
	        }
	      };
	    }

	    // Override pin_admin_post function (for feedback messages)
	    if (window.SMColibri && window.SMColibri.pin_admin_post) {
	      window.SMColibri.pin_admin_post = function(id, element) {
	        if (overlay.style.display !== 'none') {
	          handleShortsPin(id, element);
	        } else {
	          window._originalPinAdminPost.call(this, id, element);
	        }
	      };
	    }

	    // Override pub_reply function (new post modal)
	    if (window.SMColibri && window.SMColibri.pub_reply) {
	      window.SMColibri.pub_reply = function(id) {
	        if (overlay.style.display !== 'none') {
	          showShortsReplyModal(id);
	        } else {
	          window._originalPubReply.call(this, id);
	        }
	      };
	    }
	  }

	  // Shorts-specific modal functions
	  // Modern Native-like Share Sheet
	  function showShortsShareModal(url, encoded_url) {
	    createNativeShareSheet(url, encoded_url);
	  }

	  function createNativeShareSheet(url, encoded_url) {
	    // Remove existing share sheet if any
	    const existingSheet = document.getElementById('native-share-sheet');
	    if (existingSheet) {
	      existingSheet.remove();
	    }

	    // Create share sheet HTML
	    const shareSheetHTML = `
	      <div id="native-share-sheet" class="native-share-sheet">
	        <div class="native-share-backdrop"></div>
	        <div class="native-share-container">
	          <div class="native-share-handle"></div>
	          <div class="native-share-header">
	            <h3>Share</h3>
	            <button class="native-share-close" type="button">
	              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
	                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
	              </svg>
	            </button>
	          </div>

	          <!-- Quick Actions Row -->
	          <div class="native-share-quick-actions">
	            <div class="quick-action-item" onclick="copyToClipboard('${url}')">
	              <div class="quick-action-icon copy-icon">
	                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
	                  <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z" fill="currentColor"/>
	                </svg>
	              </div>
	              <span>Copy Link</span>
	            </div>
	            <div class="quick-action-item" onclick="showMoreOptions('${url}')">
	              <div class="quick-action-icon share-icon">
	                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
	                  <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z" fill="currentColor"/>
	                </svg>
	              </div>
	              <span>More</span>
	            </div>
	          </div>

	          <!-- Social Media Apps -->
	          <div class="native-share-section">
	            <h4>Share to</h4>
	            <div class="native-share-apps">
	              <div class="share-app-item" onclick="shareToSocialMedia('whatsapp', '${encoded_url}')">
	                <div class="share-app-icon whatsapp">
	                  <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
	                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488" fill="#25D366"/>
	                  </svg>
	                </div>
	                <span>WhatsApp</span>
	              </div>

	              <div class="share-app-item" onclick="shareToSocialMedia('facebook', '${encoded_url}')">
	                <div class="share-app-icon facebook">
	                  <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
	                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" fill="#1877F2"/>
	                  </svg>
	                </div>
	                <span>Facebook</span>
	              </div>

	              <div class="share-app-item" onclick="shareToSocialMedia('twitter', '${encoded_url}')">
	                <div class="share-app-icon twitter">
	                  <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
	                    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" fill="#000000"/>
	                  </svg>
	                </div>
	                <span>X (Twitter)</span>
	              </div>

	              <div class="share-app-item" onclick="shareToSocialMedia('telegram', '${encoded_url}')">
	                <div class="share-app-icon telegram">
	                  <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
	                    <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z" fill="#0088CC"/>
	                  </svg>
	                </div>
	                <span>Telegram</span>
	              </div>

	              <div class="share-app-item" onclick="shareToSocialMedia('instagram', '${encoded_url}')">
	                <div class="share-app-icon instagram">
	                  <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
	                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z" fill="url(#instagram-gradient)"/>
	                    <defs>
	                      <linearGradient id="instagram-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
	                        <stop offset="0%" style="stop-color:#833AB4"/>
	                        <stop offset="50%" style="stop-color:#FD1D1D"/>
	                        <stop offset="100%" style="stop-color:#FCB045"/>
	                      </linearGradient>
	                    </defs>
	                  </svg>
	                </div>
	                <span>Instagram</span>
	              </div>

	              <div class="share-app-item" onclick="shareToSocialMedia('linkedin', '${encoded_url}')">
	                <div class="share-app-icon linkedin">
	                  <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
	                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" fill="#0A66C2"/>
	                  </svg>
	                </div>
	                <span>LinkedIn</span>
	              </div>

	              <div class="share-app-item" onclick="shareToSocialMedia('reddit', '${encoded_url}')">
	                <div class="share-app-icon reddit">
	                  <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
	                    <path d="M12 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0zm5.01 4.744c.688 0 1.25.561 1.25 1.249a1.25 1.25 0 0 1-2.498.056l-2.597-.547-.8 3.747c1.824.07 3.48.632 4.674 1.488.308-.309.73-.491 1.207-.491.968 0 1.754.786 1.754 1.754 0 .716-.435 1.333-1.01 1.614a3.111 3.111 0 0 1 .042.52c0 2.694-3.13 4.87-7.004 4.87-3.874 0-7.004-2.176-7.004-4.87 0-.183.015-.366.043-.534A1.748 1.748 0 0 1 4.028 12c0-.968.786-1.754 1.754-1.754.463 0 .898.196 1.207.49 1.207-.883 2.878-1.43 4.744-1.487l.885-4.182a.342.342 0 0 1 .14-.197.35.35 0 0 1 .238-.042l2.906.617a1.214 1.214 0 0 1 1.108-.701zM9.25 12C8.561 12 8 12.562 8 13.25c0 .687.561 1.248 1.25 1.248.687 0 1.248-.561 1.248-1.249 0-.688-.561-1.249-1.249-1.249zm5.5 0c-.687 0-1.248.561-1.248 1.25 0 .687.561 1.248 1.249 1.248.688 0 1.249-.561 1.249-1.249 0-.687-.562-1.249-1.25-1.249zm-5.466 3.99a.327.327 0 0 0-.231.094.33.33 0 0 0 0 .463c.842.842 2.484.913 2.961.913.477 0 2.105-.056 2.961-.913a.361.361 0 0 0 .029-.463.33.33 0 0 0-.464 0c-.547.533-1.684.73-2.512.73-.828 0-1.979-.196-2.512-.73a.326.326 0 0 0-.232-.095z" fill="#FF4500"/>
	                  </svg>
	                </div>
	                <span>Reddit</span>
	              </div>

	              <div class="share-app-item" onclick="shareToSocialMedia('pinterest', '${encoded_url}')">
	                <div class="share-app-icon pinterest">
	                  <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
	                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24c6.624 0 11.99-5.367 11.99-12C24.007 5.367 18.641.001.012.001z" fill="#E60023"/>
	                  </svg>
	                </div>
	                <span>Pinterest</span>
	              </div>
	            </div>
	          </div>
	        </div>
	      </div>
	    `;

	    // Add to DOM
	    document.body.insertAdjacentHTML('beforeend', shareSheetHTML);

	    // Get the sheet element
	    const sheet = document.getElementById('native-share-sheet');
	    if (sheet) {
	      // Add event listeners
	      const backdrop = sheet.querySelector('.native-share-backdrop');
	      const closeBtn = sheet.querySelector('.native-share-close');

	      if (backdrop) {
	        backdrop.addEventListener('click', closeNativeShareSheet);
	      }
	      if (closeBtn) {
	        closeBtn.addEventListener('click', closeNativeShareSheet);
	      }
	      document.addEventListener('keydown', handleShareSheetEscape);

	      // Add swipe gesture support for mobile
	      setupNativeShareSwipeGestures(sheet);

	      // Trigger animation immediately (no delay for faster opening)
	      requestAnimationFrame(() => {
	        sheet.classList.add('active');
	      });
	    }
	  }

	  // Native Share Sheet Functions
	  window.closeNativeShareSheet = function() {
	    const sheet = document.getElementById('native-share-sheet');
	    if (sheet) {
	      // Remove event listeners
	      const backdrop = sheet.querySelector('.native-share-backdrop');
	      const closeBtn = sheet.querySelector('.native-share-close');

	      if (backdrop) {
	        backdrop.removeEventListener('click', closeNativeShareSheet);
	      }
	      if (closeBtn) {
	        closeBtn.removeEventListener('click', closeNativeShareSheet);
	      }
	      document.removeEventListener('keydown', handleShareSheetEscape);

	      sheet.classList.remove('active');
	      setTimeout(() => {
	        sheet.remove();
	      }, 300);
	    }
	  }

	  // Make functions globally accessible for the share modal
	  window.copyToClipboard = function(text) {
	    console.log('copyToClipboard called with:', text);
	    if (navigator.clipboard && window.isSecureContext) {
	      navigator.clipboard.writeText(text).then(() => {
	        console.log('Clipboard write successful');
	        showCopySuccess();
	      }).catch((err) => {
	        console.log('Clipboard write failed, using fallback:', err);
	        fallbackCopyToClipboard(text);
	      });
	    } else {
	      console.log('Using fallback clipboard method');
	      fallbackCopyToClipboard(text);
	    }
	  }

	  window.fallbackCopyToClipboard = function(text) {
	    const textArea = document.createElement('textarea');
	    textArea.value = text;
	    textArea.style.position = 'fixed';
	    textArea.style.left = '-999999px';
	    textArea.style.top = '-999999px';
	    document.body.appendChild(textArea);
	    textArea.focus();
	    textArea.select();

	    try {
	      document.execCommand('copy');
	      showCopySuccess();
	    } catch (err) {
	      console.error('Failed to copy text: ', err);
	    }

	    document.body.removeChild(textArea);
	  }

	  window.showCopySuccess = function() {
	    // Update the quick action copy button text temporarily
	    const copyQuickAction = document.querySelector('.quick-action-item[onclick*="copyToClipboard"]');
	    if (copyQuickAction) {
	      const iconDiv = copyQuickAction.querySelector('.quick-action-icon');
	      const spanElement = copyQuickAction.querySelector('span');

	      if (iconDiv && spanElement) {
	        // Store original content
	        const originalIconHTML = iconDiv.innerHTML;
	        const originalText = spanElement.textContent;

	        // Update to success state
	        iconDiv.innerHTML = `
	          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
	            <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
	          </svg>
	        `;
	        iconDiv.style.background = '#10B981';
	        spanElement.textContent = 'Copied!';

	        // Reset after 2 seconds
	        setTimeout(() => {
	          iconDiv.innerHTML = originalIconHTML;
	          iconDiv.style.background = '';
	          spanElement.textContent = originalText;
	        }, 2000);
	      }
	    }

	    // Also show a notification if available
	    if (typeof cl_bs_notify === 'function') {
	      cl_bs_notify("Copied to your clipboard!", 3000, "success");
	    }
	  }

	  window.shareToSocialMedia = function(platform, url) {
	    console.log('shareToSocialMedia called with:', platform, url);
	    // Decode the URL first
	    const decodedUrl = decodeURIComponent(url);
	    console.log('Decoded URL:', decodedUrl);

	    const shareUrls = {
	      whatsapp: `https://wa.me/?text=${encodeURIComponent(decodedUrl)}`,
	      facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(decodedUrl)}`,
	      twitter: `https://twitter.com/intent/tweet?url=${encodeURIComponent(decodedUrl)}`,
	      telegram: `https://telegram.me/share/url?url=${encodeURIComponent(decodedUrl)}`,
	      instagram: `https://www.instagram.com/`, // Instagram doesn't support direct URL sharing
	      linkedin: `https://www.linkedin.com/shareArticle?mini=true&url=${encodeURIComponent(decodedUrl)}`,
	      reddit: `https://www.reddit.com/submit?url=${encodeURIComponent(decodedUrl)}`,
	      pinterest: `https://pinterest.com/pin/create/button/?url=${encodeURIComponent(decodedUrl)}`
	    };

	    if (shareUrls[platform]) {
	      console.log('Opening URL:', shareUrls[platform]);
	      window.open(shareUrls[platform], '_blank', 'width=600,height=400');
	    } else {
	      console.error('Unsupported platform:', platform);
	    }
	  }

	  function shareToNativeApp(url, type) {
	    if (navigator.share) {
	      navigator.share({
	        title: 'Check out this post',
	        url: url
	      }).catch((error) => {
	        console.log('Error sharing:', error);
	        // Fallback to copy link
	        copyToClipboard(url);
	      });
	    } else {
	      // Fallback for browsers that don't support Web Share API
	      copyToClipboard(url);
	    }
	  }

	  // Handle backdrop clicks
	  function handleShareSheetBackdropClick(event) {
	    if (event.target.classList.contains('native-share-backdrop')) {
	      closeNativeShareSheet();
	    }
	  }

	  // Handle escape key
	  window.handleShareSheetEscape = function(event) {
	    if (event.key === 'Escape') {
	      closeNativeShareSheet();
	    }
	  }

	  // Setup swipe gestures for native share modal (like TikTok/YouTube)
	  function setupNativeShareSwipeGestures(sheet) {
	    const container = sheet.querySelector('.native-share-container');
	    const handle = sheet.querySelector('.native-share-handle');
	    const header = sheet.querySelector('.native-share-header');

	    if (!container) return;

	    let startY = 0;
	    let currentY = 0;
	    let isDragging = false;
	    let startTime = 0;

	    function handleTouchStart(e) {
	      const touch = e.touches[0];
	      startY = touch.clientY;
	      currentY = startY;
	      startTime = Date.now();
	      isDragging = true;

	      container.style.transition = 'none';
	      document.body.style.overflow = 'hidden';
	    }

	    function handleTouchMove(e) {
	      if (!isDragging) return;

	      const touch = e.touches[0];
	      currentY = touch.clientY;
	      const deltaY = currentY - startY;

	      if (deltaY > 0) {
	        e.preventDefault();
	        const resistance = Math.min(deltaY * 0.6, window.innerHeight * 0.3);
	        container.style.transform = `translateY(${resistance}px)`;

	        const opacity = Math.max(0.3, 1 - (resistance / (window.innerHeight * 0.3)));
	        sheet.style.opacity = opacity;
	      }
	    }

	    function handleTouchEnd(e) {
	      if (!isDragging) return;

	      isDragging = false;
	      const deltaY = currentY - startY;
	      const deltaTime = Date.now() - startTime;
	      const velocity = deltaY / deltaTime;

	      document.body.style.overflow = '';

	      const shouldClose = deltaY > 100 || (deltaY > 50 && velocity > 0.3);

	      if (shouldClose) {
	        closeNativeShareSheet();
	      } else {
	        container.style.transition = '';
	        container.style.transform = '';
	        sheet.style.opacity = '';
	      }
	    }

	    // Add event listeners
	    if (handle) {
	      handle.addEventListener('touchstart', handleTouchStart, { passive: false });
	      handle.addEventListener('touchmove', handleTouchMove, { passive: false });
	      handle.addEventListener('touchend', handleTouchEnd, { passive: false });
	    }

	    if (header) {
	      header.addEventListener('touchstart', handleTouchStart, { passive: false });
	      header.addEventListener('touchmove', handleTouchMove, { passive: false });
	      header.addEventListener('touchend', handleTouchEnd, { passive: false });
	    }

	    // Also add to container top area
	    container.addEventListener('touchstart', function(e) {
	      const rect = container.getBoundingClientRect();
	      const relativeY = e.touches[0].clientY - rect.top;
	      if (relativeY < 80) {
	        handleTouchStart(e);
	      }
	    }, { passive: false });

	    container.addEventListener('touchmove', handleTouchMove, { passive: false });
	    container.addEventListener('touchend', handleTouchEnd, { passive: false });
	  }

	  // Show more options - use native device share API like TikTok/Instagram
	  window.showMoreOptions = function(url) {
	    console.log('showMoreOptions called with:', url);
	    // Decode the URL first
	    const decodedUrl = decodeURIComponent(url);
	    console.log('Decoded URL:', decodedUrl);

	    // Try to use native Web Share API first (like TikTok/Instagram)
	    if (navigator.share) {
	      console.log('Using native Web Share API');
	      navigator.share({
	        title: 'Check out this post',
	        text: 'Check out this amazing post!',
	        url: decodedUrl
	      }).then(() => {
	        console.log('Successfully shared');
	        closeNativeShareSheet();
	      }).catch((error) => {
	        console.log('Error sharing:', error);
	        // If native share fails, show the more options modal
	        showMoreOptionsModal(decodedUrl);
	      });
	    } else {
	      console.log('Web Share API not supported, using fallback');
	      // Fallback to more options modal for browsers that don't support Web Share API
	      showMoreOptionsModal(decodedUrl);
	    }
	  }

	  // Fallback more options modal
	  function showMoreOptionsModal(url) {
	    // Close current share sheet
	    closeNativeShareSheet();

	    // Get the current post ID from the URL
	    const postId = url.split('/').pop();

	    // Create more options sheet with dropdown menu content
	    const moreOptionsHTML = `
	      <div id="native-share-sheet" class="native-share-sheet">
	        <div class="native-share-backdrop"></div>
	        <div class="native-share-container">
	          <div class="native-share-handle"></div>
	          <div class="native-share-header">
	            <h3>More Options</h3>
	            <button class="native-share-close" type="button">
	              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
	                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
	              </svg>
	            </button>
	          </div>

	          <div class="shorts-bottom-sheet-content">
	            <div class="shorts-bottom-sheet-body">
	              <a class="dropdown-item" href="${url}" data-spa="true">
	                <span class="flex-item dropdown-item-icon">
	                  <svg width="24" height="24" fill="none" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
	                    <path d="M6.25 4.5A1.75 1.75 0 0 0 4.5 6.25v11.5c0 .966.783 1.75 1.75 1.75h11.5a1.75 1.75 0 0 0 1.75-1.75v-4a.75.75 0 0 1 1.5 0v4A3.25 3.25 0 0 1 17.75 21H6.25A3.25 3.25 0 0 1 3 17.75V6.25A3.25 3.25 0 0 1 6.25 3h4a.75.75 0 0 1 0 1.5h-4ZM13 3.75a.75.75 0 0 1 .75-.75h6.5a.75.75 0 0 1 .75.75v6.5a.75.75 0 0 1-1.5 0V5.56l-5.22 5.22a.75.75 0 0 1-1.06-1.06l5.22-5.22h-4.69a.75.75 0 0 1-.75-.75Z" fill="currentColor"></path>
	                  </svg>
	                </span>
	                <span class="flex-item">Show thread</span>
	              </a>

	              <div class="dropdown-divider"></div>

	              <a class="dropdown-item" href="javascript:void(0)" onclick="copyToClipboard('${url}'); closeNativeShareSheet();">
	                <span class="flex-item dropdown-item-icon">
	                  <svg width="24" height="24" fill="none" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
	                    <path d="M15.986 4a2.25 2.25 0 0 0-2.236-2h-3.5a2.25 2.25 0 0 0-2.236 2H6.25A2.25 2.25 0 0 0 4 6.25v13.5A2.25 2.25 0 0 0 6.25 22H11a4.986 4.986 0 0 1-.771-1.5H6.25a.75.75 0 0 1-.75-.75V6.25a.75.75 0 0 1 .75-.75h2.129c.404.603 1.091 1 1.871 1h3.5c.778 0 1.464-.395 1.868-.996h1.884V5.5h.248a.75.75 0 0 1 .75.75V14H20V6.25A2.25 2.25 0 0 0 17.75 4h-1.764Zm-5.736-.5h3.5a.75.75 0 0 1 0 1.5h-3.5a.75.75 0 0 1 0-1.5Z" fill="currentColor"></path>
	                  </svg>
	                </span>
	                <span class="flex-item">Copy link</span>
	              </a>

	              <div class="dropdown-divider"></div>

	              <a class="dropdown-item" href="javascript:void(0);" onclick="SMColibri.bookmark_post('${postId}', this); closeNativeShareSheet();">
	                <span class="flex-item dropdown-item-icon">
	                  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
	                    <path fill-rule="evenodd" clip-rule="evenodd" d="M10 2C6.68629 2 4 4.69052 4 8.00944V18.992C4 21.1492 6.20215 22.6036 8.18176 21.7538L11.6061 20.2839C11.8576 20.176 12.1424 20.176 12.3939 20.2839L15.8182 21.7538C17.7978 22.6036 20 21.1492 20 18.992V8.00944C20 4.69052 17.3137 2 14 2H10ZM6 8.00944C6 5.79683 7.79086 4.00315 10 4.00315H14C16.2091 4.00315 18 5.79683 18 8.00944V18.992C18 19.7111 17.2659 20.1959 16.6061 19.9126L13.1818 18.4428C12.4271 18.1188 11.5729 18.1188 10.8182 18.4428L7.39392 19.9126C6.73405 20.1959 6 19.7111 6 18.992V8.00944Z" fill="currentColor"></path>
	                  </svg>
	                </span>
	                <span class="flex-item">Bookmark</span>
	              </a>
	            </div>
	          </div>
	        </div>
	      </div>
	    `;

	    // Add to DOM
	    document.body.insertAdjacentHTML('beforeend', moreOptionsHTML);

	    // Get the sheet element and set up
	    const sheet = document.getElementById('native-share-sheet');
	    if (sheet) {
	      // Add event listeners
	      const backdrop = sheet.querySelector('.native-share-backdrop');
	      const closeBtn = sheet.querySelector('.native-share-close');

	      if (backdrop) {
	        backdrop.addEventListener('click', closeNativeShareSheet);
	      }
	      if (closeBtn) {
	        closeBtn.addEventListener('click', closeNativeShareSheet);
	      }
	      document.addEventListener('keydown', handleShareSheetEscape);

	      // Add swipe gesture support for mobile
	      setupNativeShareSwipeGestures(sheet);

	      // Trigger animation immediately
	      requestAnimationFrame(() => {
	        sheet.classList.add('active');
	      });
	    }
	  }

	  function showShortsLikesModal(id) {
	    shortsModal.show('Loading...', '<div style="text-align:center;padding:40px;"><div style="display:inline-block;width:20px;height:20px;border:2px solid #f3f3f3;border-top:2px solid #007bff;border-radius:50%;animation:spin 1s linear infinite;"></div><p style="margin-top:10px;color:#666;">Loading likes...</p></div>');

	    // Fetch likes data
	    fetch('<?php echo cl_link("native_api/main/show_likes"); ?>', {
	      method: 'POST',
	      headers: {'Content-Type': 'application/x-www-form-urlencoded'},
	      body: 'id=' + id
	    })
	    .then(response => response.json())
	    .then(data => {
	      if (data.status === 200) {
	        // Extract and process the likes data
	        const tempDiv = document.createElement('div');
	        tempDiv.innerHTML = data.html;
	        const title = tempDiv.querySelector('.modal-title')?.textContent || 'Likes';

	        // Try multiple selectors to find the likes container and user items
	        let userItems = [];
	        const possibleContainers = [
	          '.post-likes .timeline-user-ls',
	          '.timeline-users-container .timeline-user-ls',
	          '.timeline-user-ls',
	          '.post-likes',
	          '.modal-body'
	        ];

	        for (const selector of possibleContainers) {
	          const container = tempDiv.querySelector(selector);
	          if (container) {
	            userItems = container.querySelectorAll('.timeline-user-item, .user-item, .user-list-item, a[data-uinfo-lbox]');
	            if (userItems.length > 0) break;
	          }
	        }

	        // If still no items found, try to find any links with user data
	        if (userItems.length === 0) {
	          userItems = tempDiv.querySelectorAll('a[data-uinfo-lbox], a[href*="/user/"], a[href*="/@"]');
	        }

	        console.log('Found user items:', userItems.length); // Debug log

	        if (userItems.length > 0) {
	          let likesHTML = '<div class="post-likes"><div class="timeline-users-container"><div class="timeline-user-ls">';

	          userItems.forEach(item => {
	            // Extract user data from various possible structures
	            let userLink, avatar, nameEl, usernameEl;

	            // Check if item itself is a user link
	            if (item.hasAttribute('data-uinfo-lbox') || item.href) {
	              userLink = item;
	              avatar = item.querySelector('img') || item.querySelector('.user-data__avatar img') || item.querySelector('.avatar img');
	              nameEl = item.querySelector('.user-name') || item.querySelector('.user-data__info .user-name') || item.querySelector('.name');
	              usernameEl = item.querySelector('.user-username') || item.querySelector('.user-data__info .user-username') || item.querySelector('.username');
	            } else {
	              // Look for nested user link
	              userLink = item.querySelector('a[data-uinfo-lbox]') || item.querySelector('a[href*="/user/"]') || item.querySelector('.user-data');
	              avatar = item.querySelector('img') || item.querySelector('.user-data__avatar img') || item.querySelector('.avatar img');
	              nameEl = item.querySelector('.user-name') || item.querySelector('.user-data__info .user-name') || item.querySelector('.name');
	              usernameEl = item.querySelector('.user-username') || item.querySelector('.user-data__info .user-username') || item.querySelector('.username');
	            }

	            // Extract text content more reliably
	            let userName = '';
	            let userUsername = '';

	            if (nameEl) {
	              userName = nameEl.textContent || nameEl.innerText || '';
	              userName = userName.trim();
	            }

	            if (usernameEl) {
	              userUsername = usernameEl.textContent || usernameEl.innerText || '';
	              userUsername = userUsername.trim().replace('@', '');
	            }

	            // If no name found, try to extract from link text or title
	            if (!userName && userLink) {
	              userName = userLink.textContent?.trim() || userLink.title?.trim() || userLink.getAttribute('data-original-title')?.trim() || '';
	            }

	            if (userLink && avatar && userName) {
	              const userId = userLink.getAttribute('data-uinfo-lbox') || userLink.getAttribute('data-id') || '';
	              const userHref = userLink.href || '#';
	              const avatarSrc = avatar.getAttribute('data-src') || avatar.src || avatar.getAttribute('data-original') || '';

	              console.log('Processing user:', userName, avatarSrc); // Debug log

	              likesHTML += `
	                <div class="timeline-user-item">
	                  <a href="${userHref}" class="user-data" data-spa="true">
	                    <div class="user-avatar">
	                      <img src="${avatarSrc}" alt="${userName}" loading="lazy" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNmMGYwZjAiLz4KPHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSIxMCIgeT0iMTAiPgo8cGF0aCBkPSJNMTIgMTJjMi4yMSAwIDQtMS43OSA0LTRzLTEuNzktNC00LTQtNCAxLjc5LTQgNCAxLjc5IDQgNCA0em0wIDJjLTIuNjcgMC04IDEuMzQtOCA0djJoMTZ2LTJjMC0yLjY2LTUuMzMtNC04LTR6IiBmaWxsPSIjOTk5Ii8+Cjwvc3ZnPgo8L3N2Zz4K'">
	                    </div>
	                    <div class="user-info">
	                      <div class="user-name">${userName}</div>
	                      ${userUsername ? `<div class="user-username">@${userUsername}</div>` : ''}
	                    </div>
	                  </a>
	                </div>
	              `;
	            }
	          });

	          likesHTML += '</div></div></div>';
	          shortsModal.show(title, likesHTML);
	        } else {
	          // No users found, show empty state
	          shortsModal.show(title, '<div style="text-align:center;padding:40px;color:#666;"><p>No likes yet</p><p style="font-size:12px;margin-top:8px;">Be the first to like this post!</p></div>');
	        }
	      } else if (data.status === 404) {
	        shortsModal.show('No Likes', '<div style="text-align:center;padding:40px;color:#666;"><p>No likes yet</p><p style="font-size:12px;margin-top:8px;">Be the first to like this post!</p></div>');
	      } else {
	        shortsModal.show('Error', '<div style="text-align:center;padding:40px;color:#dc3545;"><p>Failed to load likes</p><p style="font-size:12px;margin-top:8px;">Please try again later</p></div>');
	      }
	    })
	    .catch(error => {
	      console.error('Error loading likes:', error); // Debug log
	      shortsModal.show('Error', '<div style="text-align:center;padding:40px;color:#dc3545;"><p>Failed to load likes</p><p style="font-size:12px;margin-top:8px;">Please try again later</p></div>');
	    });
	  }

	  function showShortsReportModal(id) {
	    const reportHTML = `
	      <form class="report-form" onsubmit="submitShortsReport(event, ${id})">
	        <div class="form-group">
	          <label>What's the problem with this post?</label>
	          <div class="report-reasons">
	            <div class="reason-option" onclick="selectReportReason(this, '1')">
	              <input type="radio" name="reason" value="1" id="reason1">
	              <label for="reason1">Spam or misleading content</label>
	            </div>
	            <div class="reason-option" onclick="selectReportReason(this, '2')">
	              <input type="radio" name="reason" value="2" id="reason2">
	              <label for="reason2">Inappropriate or offensive content</label>
	            </div>
	            <div class="reason-option" onclick="selectReportReason(this, '3')">
	              <input type="radio" name="reason" value="3" id="reason3">
	              <label for="reason3">Harassment or bullying</label>
	            </div>
	            <div class="reason-option" onclick="selectReportReason(this, '4')">
	              <input type="radio" name="reason" value="4" id="reason4">
	              <label for="reason4">False information or misinformation</label>
	            </div>
	            <div class="reason-option" onclick="selectReportReason(this, '5')">
	              <input type="radio" name="reason" value="5" id="reason5">
	              <label for="reason5">Copyright or intellectual property violation</label>
	            </div>
	            <div class="reason-option" onclick="selectReportReason(this, '6')">
	              <input type="radio" name="reason" value="6" id="reason6">
	              <label for="reason6">Violence or dangerous content</label>
	            </div>
	          </div>
	        </div>
	        <div class="form-group">
	          <label>Additional details (optional)</label>
	          <textarea name="comment" placeholder="Help us understand the issue better by providing more context..."></textarea>
	        </div>
	        <div class="form-actions">
	          <button type="button" class="btn btn-cancel" onclick="closeShortsModal()">Cancel</button>
	          <button type="submit" class="btn btn-submit">Submit Report</button>
	        </div>
	      </form>
	    `;
	    shortsModal.show('Report Post', reportHTML);
	  }

	  // Helper function for report reason selection
	  window.selectReportReason = function(element, value) {
	    // Remove selected class from all options
	    const allOptions = element.parentNode.querySelectorAll('.reason-option');
	    allOptions.forEach(option => option.classList.remove('selected'));

	    // Add selected class to clicked option
	    element.classList.add('selected');

	    // Check the radio button
	    const radio = element.querySelector('input[type="radio"]');
	    if (radio) {
	      radio.checked = true;
	    }
	  };

	  // Edit Post Modal
	  function showShortsEditModal(id) {
	    shortsModal.show('Loading...', '<div style="text-align:center;padding:40px;"><div style="display:inline-block;width:20px;height:20px;border:2px solid #f3f3f3;border-top:2px solid var(--cl-primary-color);border-radius:50%;animation:spin 1s linear infinite;"></div><p style="margin-top:10px;color:var(--cl-secondary-text-color);">Loading post data...</p></div>');

	    // Use jQuery AJAX to match the original implementation exactly
	    $.ajax({
	      url: '<?php echo cl_link("native_api/edit_post/get_data"); ?>',
	      type: 'GET',
	      dataType: 'json',
	      data: {id: id}
	    }).done(function(data) {
	      console.log('Response data:', data);
	      if (data.status == 200) {
	        // Extract post data from the response
	        const tempDiv = document.createElement('div');
	        tempDiv.innerHTML = data.html;
	        const postTextElement = tempDiv.querySelector('textarea[name="post_text"]');
	        let postText = '';

	        if (postTextElement) {
	          // Try different ways to get the text content
	          postText = postTextElement.value || postTextElement.textContent || postTextElement.innerHTML || '';
	          // Clean up the text content
	          postText = postText.replace(/{{post_text}}/g, '').trim();
	        }

	        // If still no text, try to find it in a script tag or Vue data
	        if (!postText) {
	          const scriptMatch = data.html.match(/post_text:\s*"([^"]*?)"/);
	          if (scriptMatch) {
	            postText = scriptMatch[1];
	          }
	        }

	        console.log('Extracted post text:', postText);

	        const maxLength = 600; // Default max length

	        const editHTML = `
	          <form class="edit-form" onsubmit="submitShortsEdit(event, ${id})">
	            <div class="form-group">
	              <div class="form-header">
	                <h3>Edit your post</h3>
	                <span class="char-counter" id="charCounter">${postText.length}/${maxLength}</span>
	              </div>
	              <div class="textarea-container">
	                <textarea name="post_text" id="editTextarea" placeholder="Share your thoughts with the world..." oninput="updateCharCounter()">${postText.replace(/"/g, '&quot;')}</textarea>
	              </div>
	            </div>
	            <div class="form-info">
	              <p>💡 You can edit your post to fix typos or add more context. This helps keep your content accurate and engaging for your audience.</p>
	            </div>
	            <div class="form-actions">
	              <button type="button" class="btn btn-cancel" onclick="closeShortsModal()">Cancel</button>
	              <button type="submit" class="btn btn-save">Save Changes</button>
	            </div>
	          </form>
	        `;
	        shortsModal.show('Edit Post', editHTML);
	      } else {
	        console.error('API returned error status:', data.status);
	        shortsModal.show('Error', '<div style="text-align:center;padding:40px;color:var(--cl-danger-color);"><p>Failed to load post data</p><p style="font-size:12px;margin-top:8px;">Status: ' + data.status + '</p></div>');
	      }
	    }).fail(function(xhr, status, error) {
	      console.error('AJAX Error:', status, error);
	      console.error('Response:', xhr.responseText);
	      shortsModal.show('Error', '<div style="text-align:center;padding:40px;color:var(--cl-danger-color);"><p>Failed to load post data</p><p style="font-size:12px;margin-top:8px;">Error: ' + error + '</p></div>');
	    });
	  }

	  // Character counter for edit modal
	  window.updateCharCounter = function() {
	    const textarea = document.getElementById('editTextarea');
	    const counter = document.getElementById('charCounter');
	    const maxLength = 600;

	    if (textarea && counter) {
	      const currentLength = textarea.value.length;
	      counter.textContent = `${currentLength}/${maxLength}`;

	      if (currentLength > maxLength) {
	        counter.classList.add('over-limit');
	      } else {
	        counter.classList.remove('over-limit');
	      }
	    }
	  };

	  // Submit edit function
	  window.submitShortsEdit = function(event, postId) {
	    event.preventDefault();
	    const form = event.target;
	    const postText = form.querySelector('textarea[name="post_text"]').value;
	    const maxLength = 600;

	    if (postText.length > maxLength) {
	      const errorHTML = `
	        <div style="text-align:center;padding:30px;color:var(--cl-danger-color);">
	          <div style="font-size:48px;margin-bottom:16px;">⚠️</div>
	          <h4 style="margin-bottom:12px;color:var(--cl-primary-text-color);">Text Too Long</h4>
	          <p style="color:var(--cl-secondary-text-color);margin-bottom:20px;">
	            Your post is ${postText.length} characters long. Maximum allowed is ${maxLength} characters.
	          </p>
	          <button onclick="closeShortsModal(); setTimeout(() => showShortsEditModal(${postId}), 300);"
	                  style="background:var(--cl-primary-color);color:white;border:none;padding:10px 20px;border-radius:6px;cursor:pointer;">
	            Go Back
	          </button>
	        </div>
	      `;
	      shortsModal.show('Edit Post', errorHTML);
	      return;
	    }

	    const submitBtn = form.querySelector('button[type="submit"]');
	    const cancelBtn = form.querySelector('button[type="button"]');

	    // Update UI for loading state
	    submitBtn.disabled = true;
	    submitBtn.innerHTML = '<span style="display:inline-block;width:16px;height:16px;border:2px solid transparent;border-top:2px solid currentColor;border-radius:50%;animation:spin 1s linear infinite;margin-right:8px;"></span>Saving...';
	    cancelBtn.disabled = true;

	    // Use jQuery AJAX to match the original implementation
	    $.ajax({
	      url: '<?php echo cl_link("native_api/edit_post/save_data"); ?>',
	      type: 'POST',
	      dataType: 'json',
	      data: {
	        id: postId,
	        post_text: postText,
	        curr_pn: 'main'
	      }
	    }).done(function(data) {
	      if (data.status == 200) {
	        // Show success message
	        const successHTML = `
	          <div style="text-align:center;padding:40px;color:var(--cl-success-color);">
	            <div style="font-size:64px;margin-bottom:20px;">✅</div>
	            <h4 style="margin-bottom:16px;color:var(--cl-primary-text-color);">Post Updated</h4>
	            <p style="color:var(--cl-secondary-text-color);line-height:1.5;">
	              Your post has been successfully updated.<br>
	              The changes are now visible to everyone.
	            </p>
	          </div>
	        `;
	        shortsModal.show('Success', successHTML);
	        setTimeout(() => {
	          shortsModal.close();
	          // Refresh the current slide to show updated content
	          location.reload();
	        }, 2000);
	      } else {
	        // Show error message
	        const errorHTML = `
	          <div style="text-align:center;padding:30px;color:var(--cl-danger-color);">
	            <div style="font-size:48px;margin-bottom:16px;">❌</div>
	            <h4 style="margin-bottom:12px;color:var(--cl-primary-text-color);">Update Failed</h4>
	            <p style="color:var(--cl-secondary-text-color);margin-bottom:20px;">
	              We couldn't save your changes right now. Please try again.
	            </p>
	            <button onclick="closeShortsModal(); setTimeout(() => showShortsEditModal(${postId}), 300);"
	                    style="background:var(--cl-primary-color);color:white;border:none;padding:10px 20px;border-radius:6px;cursor:pointer;">
	              Try Again
	            </button>
	          </div>
	        `;
	        shortsModal.show('Update Failed', errorHTML);
	      }
	    }).fail(function(xhr, status, error) {
	      // Show network error message
	      console.error('AJAX Error:', status, error);
	      const errorHTML = `
	        <div style="text-align:center;padding:30px;color:var(--cl-danger-color);">
	          <div style="font-size:48px;margin-bottom:16px;">🌐</div>
	          <h4 style="margin-bottom:12px;color:var(--cl-primary-text-color);">Connection Error</h4>
	          <p style="color:var(--cl-secondary-text-color);margin-bottom:20px;">
	            Please check your internet connection and try again.
	          </p>
	          <button onclick="closeShortsModal(); setTimeout(() => showShortsEditModal(${postId}), 300);"
	                  style="background:var(--cl-primary-color);color:white;border:none;padding:10px 20px;border-radius:6px;cursor:pointer;">
	            Try Again
	          </button>
	        </div>
	      `;
	      shortsModal.show('Connection Error', errorHTML);
	    });
	  };

	  // Delete Post Modal
	  function showShortsDeleteModal(id) {
	    const deleteHTML = `
	      <div class="confirm-content">
	        <h4>Delete Post</h4>
	        <p>Are you sure you want to delete this post? This action cannot be undone and all related content will also be permanently deleted.</p>
	        <div class="confirm-actions">
	          <button type="button" class="btn btn-cancel" onclick="closeShortsModal()">Cancel</button>
	          <button type="button" class="btn btn-confirm" onclick="confirmShortsDelete(${id})">Delete</button>
	        </div>
	      </div>
	    `;
	    shortsModal.show('Confirm Action', deleteHTML);
	  }

	  // Confirm delete function
	  window.confirmShortsDelete = function(postId) {
	    const confirmBtn = document.querySelector('.btn-confirm');
	    const cancelBtn = document.querySelector('.btn-cancel');

	    confirmBtn.disabled = true;
	    confirmBtn.innerHTML = '<span style="display:inline-block;width:16px;height:16px;border:2px solid transparent;border-top:2px solid currentColor;border-radius:50%;animation:spin 1s linear infinite;margin-right:8px;"></span>Deleting...';
	    cancelBtn.disabled = true;

	    fetch('<?php echo cl_link("native_api/main/delete_post"); ?>', {
	      method: 'POST',
	      headers: {'Content-Type': 'application/x-www-form-urlencoded'},
	      body: `id=${postId}`
	    })
	    .then(response => response.json())
	    .then(data => {
	      if (data.status === 200) {
	        const successHTML = `
	          <div style="text-align:center;padding:40px;color:var(--cl-success-color);">
	            <div style="font-size:64px;margin-bottom:20px;">🗑️</div>
	            <h4 style="margin-bottom:16px;color:var(--cl-primary-text-color);">Post Deleted</h4>
	            <p style="color:var(--cl-secondary-text-color);line-height:1.5;">
	              The post has been successfully deleted.
	            </p>
	          </div>
	        `;
	        shortsModal.show('Success', successHTML);
	        setTimeout(() => {
	          shortsModal.close();
	          closeOverlay(); // Exit shorts viewer since post is deleted
	        }, 2000);
	      } else {
	        const errorHTML = `
	          <div style="text-align:center;padding:30px;color:var(--cl-danger-color);">
	            <div style="font-size:48px;margin-bottom:16px;">❌</div>
	            <h4 style="margin-bottom:12px;color:var(--cl-primary-text-color);">Delete Failed</h4>
	            <p style="color:var(--cl-secondary-text-color);margin-bottom:20px;">
	              We couldn't delete the post right now. Please try again.
	            </p>
	            <button onclick="closeShortsModal();"
	                    style="background:var(--cl-primary-color);color:white;border:none;padding:10px 20px;border-radius:6px;cursor:pointer;">
	              Close
	            </button>
	          </div>
	        `;
	        shortsModal.show('Delete Failed', errorHTML);
	      }
	    })
	    .catch(() => {
	      const errorHTML = `
	        <div style="text-align:center;padding:30px;color:var(--cl-danger-color);">
	          <div style="font-size:48px;margin-bottom:16px;">🌐</div>
	          <h4 style="margin-bottom:12px;color:var(--cl-primary-text-color);">Connection Error</h4>
	          <p style="color:var(--cl-secondary-text-color);margin-bottom:20px;">
	            Please check your internet connection and try again.
	          </p>
	          <button onclick="closeShortsModal();"
	                  style="background:var(--cl-primary-color);color:white;border:none;padding:10px 20px;border-radius:6px;cursor:pointer;">
	            Close
	          </button>
	        </div>
	      `;
	      shortsModal.show('Connection Error', errorHTML);
	    });
	  };

	  // Generic Confirm Modal
	  function showShortsConfirmModal(data) {
	    const deferred = new $.Deferred();

	    const confirmHTML = `
	      <div class="confirm-content">
	        <h4>${data.title || 'Confirm Action'}</h4>
	        <p>${data.message || 'Are you sure you want to proceed?'}</p>
	        <div class="confirm-actions">
	          <button type="button" class="btn btn-cancel" onclick="rejectShortsConfirm()">${data.btn_1 || 'Cancel'}</button>
	          <button type="button" class="btn btn-confirm" onclick="resolveShortsConfirm()">${data.btn_2 || 'Confirm'}</button>
	        </div>
	      </div>
	    `;

	    // Store the deferred object globally for the callback functions
	    window._shortsConfirmDeferred = deferred;

	    shortsModal.show(data.title || 'Confirm Action', confirmHTML);

	    return deferred.promise();
	  }

	  // Confirm modal callbacks
	  window.resolveShortsConfirm = function() {
	    if (window._shortsConfirmDeferred) {
	      window._shortsConfirmDeferred.resolve();
	      window._shortsConfirmDeferred = null;
	    }
	    shortsModal.close();
	  };

	  window.rejectShortsConfirm = function() {
	    if (window._shortsConfirmDeferred) {
	      window._shortsConfirmDeferred.reject();
	      window._shortsConfirmDeferred = null;
	    }
	    shortsModal.close();
	  };

	  // Info Modal
	  function showShortsInfoModal(title, body) {
	    const infoHTML = `
	      <div class="info-content">
	        <h4>${title}</h4>
	        <p>${body}</p>
	        <button type="button" class="btn" onclick="closeShortsModal()">OK</button>
	      </div>
	    `;
	    shortsModal.show(title, infoHTML);
	  }

	  // Handle bookmark with native feedback
	  function handleShortsBookmark(id, element) {
	    // Call the original bookmark function but handle the feedback differently
	    const originalElement = element;
	    const isBookmarked = originalElement.textContent.includes('Unbookmark');

	    fetch('<?php echo cl_link("native_api/main/bookmark_post"); ?>', {
	      method: 'POST',
	      headers: {'Content-Type': 'application/x-www-form-urlencoded'},
	      body: `id=${id}`
	    })
	    .then(response => response.json())
	    .then(data => {
	      if (data.status === 200) {
	        // Initialize global bookmark states if not exists
	        window.bookmarkStates = window.bookmarkStates || {};

	        const newBookmarkState = data.status_code === '1';

	        // Update global state
	        window.bookmarkStates[id] = newBookmarkState;

	        // Update the button text in current element
	        const textElement = originalElement.querySelector('[data-itag="text"]');
	        if (textElement) {
	          textElement.textContent = newBookmarkState ? 'Unbookmark' : 'Bookmark';
	        }

	        // Update all instances of this post's bookmark button across the app
	        const bookmarkLinks = document.querySelectorAll(`a[onclick*="bookmark_post('${id}'"]`);
	        bookmarkLinks.forEach(link => {
	          const textEl = link.querySelector('[data-itag="text"]');
	          if (textEl) {
	            textEl.textContent = newBookmarkState ? 'Unbookmark' : 'Bookmark';
	          }
	        });

	        // Show success feedback
	        const message = newBookmarkState ? 'Added to bookmarks' : 'Removed from bookmarks';
	        const icon = newBookmarkState ? '🔖' : '🗑️';

	        const feedbackHTML = `
	          <div style="text-align:center;padding:30px;color:var(--cl-success-color);">
	            <div style="font-size:48px;margin-bottom:16px;">${icon}</div>
	            <h4 style="margin-bottom:12px;color:var(--cl-primary-text-color);">${message}</h4>
	            <p style="color:var(--cl-secondary-text-color);">
	              ${newBookmarkState ? 'This post has been saved to your bookmarks.' : 'This post has been removed from your bookmarks.'}
	            </p>
	          </div>
	        `;
	        shortsModal.show('Bookmark', feedbackHTML);
	        setTimeout(() => shortsModal.close(), 2000);
	      } else {
	        showShortsInfoModal('Error', 'Failed to update bookmark. Please try again.');
	      }
	    })
	    .catch(() => {
	      showShortsInfoModal('Error', 'Failed to update bookmark. Please try again.');
	    });
	  }

	  // Setup shorts-specific clipboard handling
	  function setupShortsClipboardHandling() {
	    // Handle clipboard copy in shorts overlay and bottom sheet
	    $(document).on('click', '#shortsViewer .clip-board-copy, .shorts-bottom-sheet .clip-board-copy', function(e) {
	      // Only handle if we're in the shorts context
	      const overlay = document.getElementById('shortsViewer');
	      const bottomSheet = document.querySelector('.shorts-bottom-sheet');

	      if ((overlay && overlay.style.display !== 'none') ||
	          (bottomSheet && bottomSheet.classList.contains('show'))) {
	        e.preventDefault();
	        e.stopPropagation();

	        const $element = $(this);
	        const textToCopy = $element.attr('data-clipboard-text');

	        if (textToCopy) {
	          // Use modern clipboard API
	          if (navigator.clipboard && navigator.clipboard.writeText) {
	            navigator.clipboard.writeText(textToCopy).then(() => {
	              showShortsClipboardSuccess();
	            }).catch(() => {
	              showShortsClipboardError();
	            });
	          } else {
	            // Fallback for older browsers
	            try {
	              const textArea = document.createElement('textarea');
	              textArea.value = textToCopy;
	              textArea.style.position = 'fixed';
	              textArea.style.left = '-999999px';
	              textArea.style.top = '-999999px';
	              document.body.appendChild(textArea);
	              textArea.focus();
	              textArea.select();

	              const successful = document.execCommand('copy');
	              document.body.removeChild(textArea);

	              if (successful) {
	                showShortsClipboardSuccess();
	              } else {
	                showShortsClipboardError();
	              }
	            } catch (err) {
	              showShortsClipboardError();
	            }
	          }
	        }

	        return false;
	      }
	    });
	  }

	  // Show clipboard success message in shorts
	  function showShortsClipboardSuccess() {
	    const successHTML = `
	      <div style="text-align:center;padding:30px;color:var(--cl-success-color);">
	        <div style="font-size:48px;margin-bottom:16px;">📋</div>
	        <h4 style="margin-bottom:12px;color:var(--cl-primary-text-color);">Link Copied!</h4>
	        <p style="color:var(--cl-secondary-text-color);">
	          The link has been copied to your clipboard.
	        </p>
	      </div>
	    `;
	    shortsModal.show('Copy Link', successHTML);
	    setTimeout(() => shortsModal.close(), 2000);
	  }

	  // Show clipboard error message in shorts
	  function showShortsClipboardError() {
	    const errorHTML = `
	      <div style="text-align:center;padding:30px;color:var(--cl-danger-color);">
	        <div style="font-size:48px;margin-bottom:16px;">❌</div>
	        <h4 style="margin-bottom:12px;color:var(--cl-primary-text-color);">Copy Failed</h4>
	        <p style="color:var(--cl-secondary-text-color);">
	          Failed to copy the link to your clipboard. Please try again.
	        </p>
	      </div>
	    `;
	    shortsModal.show('Copy Link', errorHTML);
	    setTimeout(() => shortsModal.close(), 3000);
	  }

	  // Copy link function for share modal in shorts
	  function copyLinkInShorts(url) {
	    if (navigator.clipboard && navigator.clipboard.writeText) {
	      navigator.clipboard.writeText(url).then(() => {
	        showShortsClipboardSuccess();
	      }).catch(() => {
	        showShortsClipboardError();
	      });
	    } else {
	      // Fallback for older browsers
	      try {
	        const textArea = document.createElement('textarea');
	        textArea.value = url;
	        textArea.style.position = 'fixed';
	        textArea.style.left = '-999999px';
	        textArea.style.top = '-999999px';
	        document.body.appendChild(textArea);
	        textArea.focus();
	        textArea.select();

	        const successful = document.execCommand('copy');
	        document.body.removeChild(textArea);

	        if (successful) {
	          showShortsClipboardSuccess();
	        } else {
	          showShortsClipboardError();
	        }
	      } catch (err) {
	        showShortsClipboardError();
	      }
	    }
	  }

	  // Handle pin with native feedback
	  function handleShortsPin(id, element) {
	    // Call the original pin function but handle the feedback differently
	    const originalElement = element;
	    const isPinned = originalElement.textContent.includes('Unpin post from feeds');

	    fetch('<?php echo cl_link("native_api/cpanel/pin_feed_post"); ?>', {
	      method: 'POST',
	      headers: {'Content-Type': 'application/x-www-form-urlencoded'},
	      body: `id=${id}`
	    })
	    .then(response => response.json())
	    .then(data => {
	      if (data.status === 200) {
	        // Initialize global pin states if not exists
	        window.pinStates = window.pinStates || {};

	        const newPinState = data.status_code === '1';

	        // Update global state
	        window.pinStates[id] = newPinState;

	        // Update the button text in current element
	        const textElement = originalElement.querySelector('[data-itag="text"]');
	        if (textElement) {
	          textElement.textContent = newPinState ? 'Unpin post from feeds' : 'Pin post to feeds';
	        }

	        // Update all instances of this post's pin button across the app
	        const pinLinks = document.querySelectorAll(`a[onclick*="pin_admin_post(${id}"]`);
	        pinLinks.forEach(link => {
	          const textEl = link.querySelector('[data-itag="text"]');
	          if (textEl) {
	            textEl.textContent = newPinState ? 'Unpin post from feeds' : 'Pin post to feeds';
	          }
	        });

	        // Show success feedback
	        const message = newPinState ? 'Pinned to feeds' : 'Unpinned from feeds';
	        const icon = newPinState ? '📌' : '📍';

	        const feedbackHTML = `
	          <div style="text-align:center;padding:30px;color:var(--cl-success-color);">
	            <div style="font-size:48px;margin-bottom:16px;">${icon}</div>
	            <h4 style="margin-bottom:12px;color:var(--cl-primary-text-color);">${message}</h4>
	            <p style="color:var(--cl-secondary-text-color);">
	              ${newPinState ? 'This post has been pinned to user feed pages.' : 'This post has been unpinned from user feed pages.'}
	            </p>
	          </div>
	        `;
	        shortsModal.show('Pin Status', feedbackHTML);
	        setTimeout(() => shortsModal.close(), 2000);
	      } else {
	        showShortsInfoModal('Error', 'Failed to update pin status. Please try again.');
	      }
	    })
	    .catch(() => {
	      showShortsInfoModal('Error', 'Failed to update pin status. Please try again.');
	    });
	  }

	  // Reply Modal (simplified - just show info for now)
	  function showShortsReplyModal(id) {
	    const replyHTML = `
	      <div class="info-content">
	        <h4>Reply to Post</h4>
	        <p>The reply feature will open in the main interface for the best experience.</p>
	        <div style="display:flex;gap:12px;justify-content:center;margin-top:20px;">
	          <button type="button" class="btn" style="background:var(--cl-secondary-bg-color);color:var(--cl-secondary-text-color);" onclick="closeShortsModal()">Cancel</button>
	          <button type="button" class="btn" onclick="openReplyInMain(${id})">Open Reply</button>
	        </div>
	      </div>
	    `;
	    shortsModal.show('Reply to Post', replyHTML);
	  }

	  // Open reply in main interface
	  window.openReplyInMain = function(id) {
	    shortsModal.close();
	    closeOverlay();
	    setTimeout(() => {
	      if (window._originalPubReply) {
	        window._originalPubReply.call(window.SMColibri, id);
	      }
	    }, 300);
	  };

	  // Submit report function
	  window.submitShortsReport = function(event, postId) {
	    event.preventDefault();
	    const form = event.target;
	    const reason = form.querySelector('input[name="reason"]:checked')?.value;
	    const comment = form.querySelector('textarea[name="comment"]').value;

	    if (!reason) {
	      // Show error message in a more elegant way
	      const errorHTML = `
	        <div style="text-align:center;padding:30px;color:var(--cl-danger-color);">
	          <div style="font-size:48px;margin-bottom:16px;">⚠️</div>
	          <h4 style="margin-bottom:12px;color:var(--cl-primary-text-color);">Selection Required</h4>
	          <p style="color:var(--cl-secondary-text-color);margin-bottom:20px;">Please select a reason for reporting this post.</p>
	          <button onclick="closeShortsModal(); setTimeout(() => showShortsReportModal(${postId}), 300);"
	                  style="background:var(--cl-primary-color);color:white;border:none;padding:10px 20px;border-radius:6px;cursor:pointer;">
	            Go Back
	          </button>
	        </div>
	      `;
	      shortsModal.show('Report Post', errorHTML);
	      return;
	    }

	    const submitBtn = form.querySelector('button[type="submit"]');
	    const cancelBtn = form.querySelector('button[type="button"]');

	    // Update UI for loading state
	    submitBtn.disabled = true;
	    submitBtn.innerHTML = '<span style="display:inline-block;width:16px;height:16px;border:2px solid transparent;border-top:2px solid currentColor;border-radius:50%;animation:spin 1s linear infinite;margin-right:8px;"></span>Submitting...';
	    cancelBtn.disabled = true;

	    fetch('<?php echo cl_link("native_api/main/report_post"); ?>', {
	      method: 'POST',
	      headers: {'Content-Type': 'application/x-www-form-urlencoded'},
	      body: `reason=${reason}&post_id=${postId}&comment=${encodeURIComponent(comment)}`
	    })
	    .then(response => response.json())
	    .then(data => {
	      if (data.status === 200) {
	        // Show success message
	        const successHTML = `
	          <div style="text-align:center;padding:40px;color:var(--cl-success-color);">
	            <div style="font-size:64px;margin-bottom:20px;">✅</div>
	            <h4 style="margin-bottom:16px;color:var(--cl-primary-text-color);">Report Submitted</h4>
	            <p style="color:var(--cl-secondary-text-color);line-height:1.5;">
	              Thank you for helping keep our community safe.<br>
	              We'll review this report and take appropriate action.
	            </p>
	          </div>
	        `;
	        shortsModal.show('Thank You', successHTML);
	        setTimeout(() => shortsModal.close(), 3000);
	      } else {
	        // Show error message
	        const errorHTML = `
	          <div style="text-align:center;padding:30px;color:var(--cl-danger-color);">
	            <div style="font-size:48px;margin-bottom:16px;">❌</div>
	            <h4 style="margin-bottom:12px;color:var(--cl-primary-text-color);">Submission Failed</h4>
	            <p style="color:var(--cl-secondary-text-color);margin-bottom:20px;">
	              We couldn't submit your report right now. Please try again.
	            </p>
	            <button onclick="closeShortsModal(); setTimeout(() => showShortsReportModal(${postId}), 300);"
	                    style="background:var(--cl-primary-color);color:white;border:none;padding:10px 20px;border-radius:6px;cursor:pointer;">
	              Try Again
	            </button>
	          </div>
	        `;
	        shortsModal.show('Report Failed', errorHTML);
	      }
	    })
	    .catch(() => {
	      // Show network error message
	      const errorHTML = `
	        <div style="text-align:center;padding:30px;color:var(--cl-danger-color);">
	          <div style="font-size:48px;margin-bottom:16px;">🌐</div>
	          <h4 style="margin-bottom:12px;color:var(--cl-primary-text-color);">Connection Error</h4>
	          <p style="color:var(--cl-secondary-text-color);margin-bottom:20px;">
	            Please check your internet connection and try again.
	          </p>
	          <button onclick="closeShortsModal(); setTimeout(() => showShortsReportModal(${postId}), 300);"
	                  style="background:var(--cl-primary-color);color:white;border:none;padding:10px 20px;border-radius:6px;cursor:pointer;">
	            Try Again
	          </button>
	        </div>
	      `;
	      shortsModal.show('Connection Error', errorHTML);
	    })
	    .finally(() => {
	      // Reset button states (in case user is still on the form)
	      if (submitBtn && !submitBtn.closest('.report-form').style.display === 'none') {
	        submitBtn.disabled = false;
	        submitBtn.textContent = 'Submit Report';
	        cancelBtn.disabled = false;
	      }
	    });
	  };

	  function initSlides() {
	    console.log('=== INITIALIZING SLIDES ===');
	    const slides = overlay.querySelectorAll('.shorts-slide');

	    // Clean up any existing observers or handlers
	    if (window.shortsObserver) {
	      window.shortsObserver.disconnect();
	      window.shortsObserver = null;
	    }

	    // Simplified video management - disabled aggressive switching
	    function handleVideoPlayback() {
	      // Temporarily disabled to prevent constant video switching
	      // This was causing videos to pause/play constantly
	      return;

	      // Let the unified manager's smart algorithm handle selection
	      const result = window.__unifiedVideoManager.getMostVisibleVideo();

	      // Filter to only consider videos within the shorts overlay
	      const shortsVideos = [];
	      slides.forEach(slide => {
	        const video = slide.querySelector('video');
	        if (!video) return;

	        const rect = slide.getBoundingClientRect();
	        const overlayRect = overlay.getBoundingClientRect();

	        // Check if video is within shorts overlay bounds
	        const isInOverlay = (
	          rect.top < overlayRect.bottom &&
	          rect.bottom > overlayRect.top
	        );

	        if (isInOverlay) {
	          // Calculate visibility within overlay
	          const visibleTop = Math.max(rect.top, overlayRect.top);
	          const visibleBottom = Math.min(rect.bottom, overlayRect.bottom);
	          const visibleHeight = Math.max(0, visibleBottom - visibleTop);
	          const totalHeight = rect.height;
	          const visibilityRatio = totalHeight > 0 ? visibleHeight / totalHeight : 0;

	          if (visibilityRatio >= 0.3) {
	            shortsVideos.push({ video, visibility: visibilityRatio });
	          }
	        }
	      });

	      // Find the best video within shorts context
	      let bestVideo = null;
	      let bestVisibility = 0;

	      shortsVideos.forEach(({ video, visibility }) => {
	        if (visibility > bestVisibility) {
	          bestVisibility = visibility;
	          bestVideo = video;
	        }
	      });

	      // Use unified manager to handle video switching - with very high threshold for stability
	      if (bestVideo && bestVideo !== window.__unifiedVideoManager.currentVideo && bestVisibility >= 0.95) {
	        console.log('Shorts: Smart switching to video, visibility:', bestVisibility);

	        // Hide spinners for all other videos immediately
	        slides.forEach(slide => {
	          const video = slide.querySelector('video');
	          if (video && video !== bestVideo) {
	            const spinner = slide.querySelector('.loading-spinner');
	            if (spinner) {
	              spinner.style.display = 'none';
	            }
	          }
	        });

	        // Use unified manager to play the video
	        window.__unifiedVideoManager.playVideo(bestVideo, 'shorts', bestVisibility);

	      }
	      // Disabled automatic pausing - let videos play continuously in shorts
	      // The constant pausing was causing poor user experience
	    }

	    // Disable MutationObserver - rely only on scroll events for better performance
	    // MutationObserver was causing too many video switching calls
	    console.log('Using scroll-only video management for better performance');

	    // RELIABLE Video Management - Single source of truth for instant playback
	    let currentPlayingVideo = null;
	    let scrollTimeout;
	    let isScrolling = false;
	    let lastScrollTop = 0;
	    let scrollOperationId = 0;

	    // Simple function to get the video that should be playing based on scroll position
	    function getCurrentVideo() {
	      const slides = Array.from(overlay.querySelectorAll('.shorts-slide'));
	      const currentSlideIndex = Math.round(overlay.scrollTop / window.innerHeight);
	      const targetSlide = slides[currentSlideIndex];
	      return targetSlide ? targetSlide.querySelector('video') : null;
	    }

	    // Instant video switching function - handles state properly for smooth resume
	    async function switchToCurrentVideo() {
	      const targetVideo = getCurrentVideo();

	      if (!targetVideo) return;

	      // If it's already the current video and playing, do nothing
	      if (targetVideo === currentPlayingVideo && !targetVideo.paused) {
	        return;
	      }

	      console.log(`🚀 INSTANT: Switching to video ${targetVideo.id}`);

	      // AGGRESSIVE CLEANUP: Pause ALL other videos first to prevent background playback
	      const allVideos = overlay.querySelectorAll('video');
	      allVideos.forEach(video => {
	        if (video !== targetVideo && !video.paused) {
	          console.log(`⏸️ AGGRESSIVE: Pausing video ${video.id} before switch`);
	          video.pause();
	          video.currentTime = video.currentTime; // Force pause to take effect

	          // Save state if using autoplay manager
	          if (window.shortsAutoplayManager) {
	            window.shortsAutoplayManager.saveVideoState(video);
	          }
	        }
	      });

	      // INSTANT description initialization - no delay
	      const slide = targetVideo.closest('.shorts-slide');
	      if (slide) {
	        const description = slide.querySelector('.shorts-description');
	        const descriptionBox = slide.querySelector('.shorts-description-box');
	        const publisher = slide.querySelector('.shorts-publisher');

	        if (description && descriptionBox && publisher && !description.classList.contains('collapsed') && !description.classList.contains('expanded')) {
	          console.log('🔄 INSTANT: Initializing description for switched video:', targetVideo.id);
	          initInstagramStyleDescription(description, descriptionBox, publisher);
	        }
	      }

	      // Set as current and play immediately
	      currentPlayingVideo = targetVideo;
	      const slides = Array.from(overlay.querySelectorAll('.shorts-slide'));
	      const slideIndex = slides.findIndex(slide => slide.contains(targetVideo));
	      if (slideIndex !== -1) {
	        window.shortsState.currentIndex = slideIndex;
	      }

	      // Start playing instantly - will restore state automatically
	      await window.shortsAutoplayManager.playVideo(targetVideo);
	    }

	    // Add simple event handlers to all videos
	    overlay.querySelectorAll('.shorts-video').forEach(video => {
	      // Add click handler for manual play/pause
	      video.addEventListener('click', (e) => {
	        e.stopPropagation();
	        if (video.paused) {
	          console.log(`▶️ INSTANT: Manual resume ${video.id}`);
	          video.dataset.manuallyPaused = 'false';
	          window.shortsAutoplayManager.playVideo(video);
	        } else {
	          console.log(`⏸️ INSTANT: Manual pause ${video.id}`);
	          video.dataset.manuallyPaused = 'true';
	          video.pause();
	        }
	      });

	      // Track when videos start playing
	      video.addEventListener('playing', () => {
	        console.log(`🎬 INSTANT: Video ${video.id} started playing`);
	        currentPlayingVideo = video;
	        video.classList.remove('buffering');
	        video.style.background = '#000';

	        // Set up periodic state saving during playback
	        if (video.stateInterval) {
	          clearInterval(video.stateInterval);
	        }
	        video.stateInterval = setInterval(() => {
	          if (!video.paused && window.shortsAutoplayManager) {
	            window.shortsAutoplayManager.saveVideoState(video);
	          }
	        }, 2000); // Save state every 2 seconds during playback
	      });

	      // Clean up interval when video is paused or ends
	      video.addEventListener('pause', () => {
	        if (video.stateInterval) {
	          clearInterval(video.stateInterval);
	          video.stateInterval = null;
	        }
	      });

	      // Handle buffering states
	      video.addEventListener('waiting', () => {
	        console.log(`⏳ INSTANT: Video ${video.id} buffering`);
	        video.classList.add('buffering');
	      });

	      // Handle video errors with simple recovery
	      video.addEventListener('error', (e) => {
	        console.log(`❌ INSTANT: Video error for ${video.id}:`, e);
	        setTimeout(() => video.load(), 1000);
	      });
	    });



	    // Function to add handlers to new videos when dynamically loaded
	    window.reobserveShorts = function() {
	      console.log('📹 Adding handlers to new videos...');
	      overlay.querySelectorAll('.shorts-video').forEach(video => {
	        // Only add handlers if not already added
	        if (!video.dataset.handlersAdded) {
	          video.dataset.handlersAdded = 'true';
	          // Add the same event handlers as above
	          video.addEventListener('click', (e) => {
	            e.stopPropagation();
	            if (video.paused) {
	              console.log(`▶️ INSTANT: Manual resume ${video.id}`);
	              video.dataset.manuallyPaused = 'false';
	              window.shortsAutoplayManager.playVideo(video);
	            } else {
	              console.log(`⏸️ INSTANT: Manual pause ${video.id}`);
	              video.dataset.manuallyPaused = 'true';
	              video.pause();
	            }
	          });
	          video.addEventListener('playing', () => {
	            console.log(`🎬 INSTANT: Video ${video.id} started playing`);
	            currentPlayingVideo = video;
	            video.classList.remove('buffering');
	            video.style.background = '#000';

	            // Set up periodic state saving during playback
	            if (video.stateInterval) {
	              clearInterval(video.stateInterval);
	            }
	            video.stateInterval = setInterval(() => {
	              if (!video.paused && window.shortsAutoplayManager) {
	                window.shortsAutoplayManager.saveVideoState(video);
	              }
	            }, 2000); // Save state every 2 seconds during playback
	          });
	          video.addEventListener('pause', () => {
	            if (video.stateInterval) {
	              clearInterval(video.stateInterval);
	              video.stateInterval = null;
	            }
	          });
	          video.addEventListener('waiting', () => {
	            console.log(`⏳ INSTANT: Video ${video.id} buffering`);
	            video.classList.add('buffering');
	          });
	          video.addEventListener('error', (e) => {
	            console.log(`❌ INSTANT: Video error for ${video.id}:`, e);
	            setTimeout(() => video.load(), 1000);
	          });
	        }
	      });
	    };

	    // Advanced scroll handler with ultra-fast pausing and debounced switching like themes200
	    let scrollDebounceTimer;
	    let scrollDebounceTimeout;
	    let lastScrollTime = 0;
	    window.shortsScrolling = false; // Global flag to track scrolling state

	    overlay.addEventListener('scroll', () => {
	      const currentScrollTop = overlay.scrollTop;
	      const scrollDirection = currentScrollTop > (window.lastScrollTop || 0) ? 'down' : 'up';
	      const currentSlideIndex = Math.round(currentScrollTop / window.innerHeight);

	      // Update current index in shorts state
	      window.shortsState.currentIndex = currentSlideIndex;
	      window.shortsScrolling = true;

	      // Set scrolling flag for video management
	      isScrolling = true;
	      if (window.__unifiedVideoManager) {
	        window.__unifiedVideoManager.isScrolling = true;
	      }

	      // ULTRA-FAST PAUSE: Immediately pause videos that are moving out of view
	      const viewportHeight = window.innerHeight;
	      const allVideos = overlay.querySelectorAll('video');
	      let currentVideo = null;

	      allVideos.forEach(video => {
	        const videoSlide = video.closest('.shorts-slide');
	        if (videoSlide) {
	          const slideRect = videoSlide.getBoundingClientRect();
	          const slideCenter = slideRect.top + (slideRect.height / 2);
	          const viewportCenter = viewportHeight / 2;
	          const distanceFromCenter = Math.abs(slideCenter - viewportCenter);

	          // Check if this video is in the center (should be playing)
	          if (distanceFromCenter < (viewportHeight * 0.3)) {
	            currentVideo = video;

	            // CONSISTENT PREP: Ensure ALL center videos are ready
	            video.muted = window.__globalMute !== false;
	            video.playsInline = true;
	            video.setAttribute('webkit-playsinline', 'true');
	            video.setAttribute('playsinline', 'true');

	            // Ensure video is ready for consistent performance
	            if (video.preload === 'none') {
	              video.preload = 'metadata';
	            }
	          }

	          // ANDROID NO-FLASH: Prevent pause on Android to eliminate flash
	          const isAndroid = /Android/i.test(navigator.userAgent);
	          const pauseThreshold = isAndroid ? 0.9 : 0.7; // Android: only pause when almost completely out

	          if (!video.paused && distanceFromCenter > (viewportHeight * pauseThreshold)) {
	            video.pause();
	            console.log(`⚡ NO-FLASH: Paused video ${video.id} (Android: ${isAndroid})`);
	          }
	        }
	      });

	      // UNIVERSAL: Start new video FIRST, then pause others (prevents blank)
	      if (currentVideo && currentVideo !== currentPlayingVideo) {
	        console.log(`🌍 UNIVERSAL: Same switching for ALL devices - ${currentVideo.id}`);

	        // STEP 1: Start new video FIRST (prevents blank container)
	        currentPlayingVideo = currentVideo;
	        if (currentVideo.paused) {
	          currentVideo.muted = window.__globalMute !== false;
	          currentVideo.play().then(() => {
	            // STEP 2: Only pause others AFTER new video starts (no blank gap)
	            allVideos.forEach(v => {
	              if (v !== currentVideo && !v.paused) {
	                v.pause();
	              }
	            });
	          });
	        }

	      }

	      // Clear previous timeouts
	      clearTimeout(scrollDebounceTimer);
	      clearTimeout(scrollDebounceTimeout);

	      // CROSS-DEVICE scroll response - iPhone 12 speed + Android compatibility
	      scrollDebounceTimer = setTimeout(() => {
	        isScrolling = false;
	        window.shortsScrolling = false;
	        if (window.__unifiedVideoManager) {
	          window.__unifiedVideoManager.isScrolling = false;
	        }

	        // UNIVERSAL final switching - Same for ALL devices
	        const targetVideo = getCurrentVideo();
	        if (targetVideo && targetVideo !== currentPlayingVideo && targetVideo.paused) {
	          console.log('🌍 UNIVERSAL: Final switch - same for all devices');
	          currentPlayingVideo = targetVideo;
	          targetVideo.muted = window.__globalMute !== false;
	          targetVideo.play();
	        }
	      }, 50); // Balanced response for all devices

	      // Load more content when approaching the end
	      const totalSlides = window.shortsState.loadedVideos.length;
	      const slidesRemaining = totalSlides - currentSlideIndex;

	      if (scrollDirection === 'down' && slidesRemaining <= 2 && window.shortsState.hasMoreContent && !window.shortsState.isLoading) {
	        console.log('📱 Loading more content...');
	        loadMoreShorts().catch(err => console.log('Failed to load more:', err));
	      }

	      window.lastScrollTop = currentScrollTop;

	      // Hide any open dropdown menus when scrolling
	      if (!dropdownActionInProgress) {
	        hideAllDropdowns();
	      }
	    }, { passive: true }); // Use passive for better performance

    // Additional safety mechanism: Immediate pause for videos with low visibility
    overlay.addEventListener('scroll', () => {
      const viewportHeight = window.innerHeight;
      const allVideos = overlay.querySelectorAll('video');

      allVideos.forEach(video => {
        if (!video.paused) {
          const videoSlide = video.closest('.shorts-slide');
          if (videoSlide) {
            const slideRect = videoSlide.getBoundingClientRect();
            const slideTop = slideRect.top;
            const slideBottom = slideRect.bottom;

            // Pause immediately if video slide is not prominently visible (less than 50% visible)
            const visibleHeight = Math.min(slideBottom, viewportHeight) - Math.max(slideTop, 0);
            const slideHeight = slideRect.height;
            const visibilityRatio = Math.max(0, visibleHeight) / slideHeight;

            if (visibilityRatio < 0.5) {
              video.pause();
              console.log(`⏸️ VIEWPORT: Immediate pause of video ${video.id} (visibility: ${Math.round(visibilityRatio * 100)}%)`);
            }
          }
        }
      });
    }, { passive: true });

    // Global safety function to pause all videos except the specified one
    window.pauseAllShortsVideosExcept = function(exceptVideo) {
      const allVideos = overlay.querySelectorAll('video');
      allVideos.forEach(video => {
        if (video !== exceptVideo && !video.paused) {
          video.pause();
          console.log(`🛑 GLOBAL PAUSE: Paused video ${video.id}`);
        }
      });
    };

    // Call this function periodically to ensure no multiple videos are playing
    setInterval(() => {
      const playingVideos = Array.from(overlay.querySelectorAll('video')).filter(v => !v.paused);
      if (playingVideos.length > 1) {
        console.log(`⚠️ MULTIPLE VIDEOS DETECTED: ${playingVideos.length} videos playing, pausing all except current`);
        const currentVideo = window.shortsAutoplayManager?.currentVideo;
        playingVideos.forEach(video => {
          if (video !== currentVideo) {
            video.pause();
            console.log(`🛑 CLEANUP: Paused extra video ${video.id}`);
          }
        });
      }
    }, 500); // Check every 500ms

	    // Advanced gesture detection like themes200 - prevents conflicts with vertical scrolling
    let touchStartX = 0;
    let touchStartY = 0;
    let touchStartTime = 0;
    let isTouching = false;
    let gestureDirection = null; // Track if gesture is 'horizontal' or 'vertical'
    let isBackSwiping = false;   // Track if user is performing back swipe

    // Touch settings for horizontal swipe to close only
    const TOUCH_SETTINGS = {
      minHorizontalSwipeDistance: 30,  // Very low threshold - just 30px to start closing
      minHorizontalSwipeVelocity: 0.1, // Very low velocity requirement
      flexibleCloseDistance: 40,       // If swiped more than 40px, close on release (lowered from 60)
      flexibleCloseProgress: 0.12      // If swiped more than 12% of screen, close on release (lowered from 0.15)
    };

    overlay.addEventListener('touchstart', (e) => {
      touchStartX = e.touches[0].clientX;
      touchStartY = e.touches[0].clientY;
      touchStartTime = Date.now();
      isTouching = true;
      gestureDirection = null;
      isBackSwiping = false;
      console.log('📱 Touch start detected');
    }, { passive: true });

    overlay.addEventListener('touchmove', (e) => {
      if (!isTouching) return;

      const touchX = e.touches[0].clientX;
      const touchY = e.touches[0].clientY;
      const rawDeltaX = touchStartX - touchX; // Keep sign for direction
      const rawDeltaY = touchStartY - touchY;
      const deltaX = Math.abs(rawDeltaX);
      const deltaY = Math.abs(rawDeltaY);

      // More flexible gesture detection like native iOS apps
      if (!gestureDirection && (deltaX > 5 || deltaY > 5)) {
        // iOS back swipe is very flexible - can start from anywhere on left side
        const isFromLeftSide = touchStartX < 100; // Increased from 50px to 100px
        const isHorizontalMovement = rawDeltaX < 0; // Moving right

        if (isFromLeftSide && isHorizontalMovement && deltaX > 8) {
          gestureDirection = 'horizontal';
          isBackSwiping = true;
          console.log('📱 GESTURE: Horizontal back swipe detected', { touchStartX, deltaX, deltaY });
        } else if (deltaY > 8) {
          gestureDirection = 'vertical';
          console.log('📱 GESTURE: Vertical scroll detected', { touchStartX, deltaX, deltaY });
        }
        // Allow mixed gestures initially - don't lock too early
      }

      // Handle iOS back swipe with native-like flexibility
      if (gestureDirection === 'horizontal' && isBackSwiping) {
        e.preventDefault(); // Prevent scroll interference

        const progress = Math.min(Math.abs(rawDeltaX) / window.innerWidth, 1);
        const translateX = Math.max(rawDeltaX, -window.innerWidth * 0.9); // Allow more movement

        overlay.style.transform = `translateX(${Math.abs(translateX)}px)`;
        overlay.style.transition = 'none';
        overlay.style.opacity = 1 - (progress * 0.4); // Less opacity change for smoother feel
      }
      // Also allow back swipe to start even during vertical scroll (like native iOS)
      else if (!gestureDirection && touchStartX < 100 && rawDeltaX < -15) {
        // Late back swipe detection - user started scrolling but then swiped right
        gestureDirection = 'horizontal';
        isBackSwiping = true;
        e.preventDefault();

        const progress = Math.min(Math.abs(rawDeltaX) / window.innerWidth, 1);
        overlay.style.transform = `translateX(${Math.abs(rawDeltaX)}px)`;
        overlay.style.transition = 'none';
        overlay.style.opacity = 1 - (progress * 0.4);
      }
      // For pure vertical gestures, let browser handle scroll naturally
    }, { passive: false });

    overlay.addEventListener('touchend', (e) => {
      if (!isTouching) return;

      // Only handle back swipe completion if it was a horizontal back swipe
      if (gestureDirection === 'horizontal' && isBackSwiping) {
        const touchEndX = e.changedTouches[0].clientX;
        const deltaX = touchStartX - touchEndX;
        const swipeDistance = Math.abs(deltaX);
        const swipeProgress = swipeDistance / window.innerWidth;

        // More flexible completion like native iOS - multiple ways to trigger
        const shouldClose = deltaX < 0 && (
          swipeDistance > TOUCH_SETTINGS.flexibleCloseDistance ||           // Lower distance threshold
          swipeProgress > TOUCH_SETTINGS.flexibleCloseProgress ||         // Lower percentage threshold
          (swipeDistance > 25 && swipeProgress > 0.08) // Combo threshold for easier activation
        );

        if (shouldClose) {
          console.log('📱 Flexible swipe detected - closing shorts viewer', {
            distance: swipeDistance,
            progress: (swipeProgress * 100).toFixed(1) + '%'
          });

          // Animate the final close transition
          overlay.style.transition = 'transform 0.3s ease-out, opacity 0.3s ease-out';
          overlay.style.transform = 'translateX(100vw)'; // Move to the right (same direction as swipe)
          overlay.style.opacity = '0';

          // Close after animation completes - EXACTLY like arrow button (just click it)
          setTimeout(() => {
            const closeBtn = document.getElementById('shortsCloseBtn');
            if (closeBtn) {
              closeBtn.click(); // This is exactly what arrow button does
            }
          }, 300);

          isTouching = false;
          gestureDirection = null;
          isBackSwiping = false;
          return;
        }

        // Reset transform if swipe wasn't completed
        const wasTransformed = overlay.style.transform && overlay.style.transform !== 'none';
        if (wasTransformed) {
          overlay.style.transition = 'transform 0.2s ease-out, opacity 0.2s ease-out';
          overlay.style.transform = 'translateX(0)';
          overlay.style.opacity = '1';

          // Clean up after animation
          setTimeout(() => {
            overlay.style.transition = '';
            overlay.style.transform = '';
            overlay.style.opacity = '';
          }, 200);
        }
      }

      // Reset touch state
      isTouching = false;
      gestureDirection = null;
      isBackSwiping = false;
      hasScrolled = false;
    }, { passive: true });

    // Removed duplicate scroll listener - now handled in main scroll handler above

    // ULTRA-RELIABLE Video Manager - Maximum reliability with advanced error recovery!
	    window.shortsAutoplayManager = {
	      currentVideo: null,
	      isTransitioning: false,
	      preloadedVideos: new Set(),
	      pendingOperations: new Map(), // Track pending operations to prevent conflicts
	      retryAttempts: new Map(), // Track retry attempts per video
	      maxRetries: 3,
	      lastVideoSwitch: 0, // Track last video switch time for debouncing

	      async playVideo(video) {
	        if (!video) return false;

	        const videoId = video.id || 'unknown';

	        // If this is already the current video and it's playing, don't interrupt
	        if (video === this.currentVideo && !video.paused) return true;

	        // BALANCED switching - prevent conflicts but allow smooth switching
	        const now = Date.now();
	        if (this.lastVideoSwitch && (now - this.lastVideoSwitch) < 100) {
	          // Balanced debounce to prevent conflicts
	          console.log(`⏳ BALANCED: Ignoring rapid switch to ${videoId}`);
	          return false;
	        }

	        // Prevent multiple simultaneous operations on same video
	        if (this.pendingOperations.has(videoId)) {
	          console.log(`⏳ RELIABLE: Operation already pending for ${videoId}, waiting...`);
	          return await this.pendingOperations.get(videoId);
	        }

	        // Record the switch time
	        this.lastVideoSwitch = now;

	        // Create operation promise
	        const operationPromise = this._executePlayVideo(video);
	        this.pendingOperations.set(videoId, operationPromise);

	        try {
	          const result = await operationPromise;
	          return result;
	        } finally {
	          this.pendingOperations.delete(videoId);
	        }
	      },

	      async _executePlayVideo(video) {
	        const videoId = video.id || 'unknown';
	        console.log(`🚀 RELIABLE PLAY: Starting ultra-reliable playback for ${videoId}`);

	        try {
	          // INSTANT pause all other videos - no delays
	          this.pauseAllOthers(video);

	          // Set as current immediately
	          this.currentVideo = video;

	          // Try to restore previous state first
	          this.restoreVideoState(video);

	          // Ensure video is ready for instant playback
	          await this.prepareVideoForInstantPlay(video);

	          // Multi-stage play attempt with progressive fallbacks
	          const success = await this.reliablePlay(video);

	          if (success) {
	            console.log(`✅ RELIABLE SUCCESS: ${videoId} playing reliably`);
	            // Remove poster when video starts playing successfully
	            video.removeAttribute('poster');
	            video.style.background = '#000';

	            // Final check: ensure video respects global mute state
	            if (window.__globalMute === false && video.muted) {
	              console.log('🔊 FINAL FIX: Video still muted despite global unmute, forcing unmute');
	              video.muted = false;
	            }

	            this.preloadAdjacentVideos(video);
	            this.retryAttempts.delete(videoId); // Clear retry count on success
	            return true;
	          } else {
	            console.log(`❌ RELIABLE FAILED: All attempts failed for ${videoId}`);
	            this.setupClickToPlay(video);
	            return false;
	          }

	        } catch (e) {
	          console.log(`❌ RELIABLE ERROR: ${videoId}`, e.message);
	          return false;
	        }
	      },

	      pauseAllOthers(currentVideo) {
	        overlay.querySelectorAll('video').forEach(v => {
	          if (v !== currentVideo && !v.paused) {
	            v.pause();
	            console.log('⏸️ Paused:', v.id);
	          }
	        });
	      },

	      async prepareVideoForInstantPlay(video) {
	        const videoId = video.id || 'unknown';

	        // Ensure video source is loaded
	        if (!video.src && video.dataset.src) {
	          video.src = video.dataset.src;
	          console.log(`📥 RELIABLE: Loaded source for ${videoId}`);
	        }

	        // Set optimal attributes for reliable playback
	        video.muted = window.__globalMute !== false; // Respect global mute state
	        console.log('🔊 RELIABLE MANAGER: Set video mute state to:', video.muted, '(global mute:', window.__globalMute, ')');
	        video.playsInline = true;
	        video.loop = true;

	        // Upgrade preload if needed
	        if (video.preload === 'none') {
	          video.preload = 'metadata';
	        }

	        // Enhanced readiness check with better error handling
	        if (video.readyState < 1) {
	          console.log(`⚡ RELIABLE: Loading for ${videoId}`);

	          // Reset video element if it seems corrupted
	          if (video.error) {
	            console.log(`🔄 RELIABLE: Resetting corrupted video ${videoId}`);
	            const src = video.src;
	            video.src = '';
	            video.load();
	            video.src = src;
	          }

	          video.load();

	          // Wait for readiness with timeout
	          await new Promise(resolve => {
	            if (video.readyState >= 1) {
	              resolve();
	              return;
	            }

	            const onReady = () => {
	              video.removeEventListener('loadedmetadata', onReady);
	              video.removeEventListener('loadeddata', onReady);
	              video.removeEventListener('error', onError);
	              resolve();
	            };

	            const onError = () => {
	              console.log(`❌ RELIABLE: Load error for ${videoId}`, video.error);
	              video.removeEventListener('loadedmetadata', onReady);
	              video.removeEventListener('loadeddata', onReady);
	              video.removeEventListener('error', onError);
	              resolve(); // Continue anyway
	            };

	            video.addEventListener('loadedmetadata', onReady);
	            video.addEventListener('loadeddata', onReady);
	            video.addEventListener('error', onError);

	            // Much shorter timeout for ultra-fast playback
	            setTimeout(resolve, 50);
	          });
	        }
	      },

	      async reliablePlay(video) {
	        const videoId = video.id || 'unknown';
	        const currentRetries = this.retryAttempts.get(videoId) || 0;

	        // Instant playback strategy - prioritize speed over complex fallbacks
	        const strategies = [
	          { name: 'muted', muted: true },      // Start with muted for instant success
	          { name: 'direct', muted: false },    // Try unmuted second
	          { name: 'reset-muted', muted: true, reset: true }  // Only one reset fallback
	        ];

	        for (let i = 0; i < strategies.length; i++) {
	          const strategy = strategies[i];
	          console.log(`🎯 RELIABLE: Trying strategy '${strategy.name}' for ${videoId} (attempt ${currentRetries + 1})`);

	          try {
	            // Apply strategy-specific preparations with minimal delays
	            if (strategy.reset) {
	              video.currentTime = 0;
	              video.load();
	              await new Promise(resolve => setTimeout(resolve, 50)); // Reduced delay
	            }

	            // Set mute state
	            video.muted = strategy.muted;

	            // Attempt play
	            await video.play();
	            console.log(`✅ RELIABLE SUCCESS: ${videoId} playing with strategy '${strategy.name}'`);
	            return true;

	          } catch (e) {
	            console.log(`❌ RELIABLE: Strategy '${strategy.name}' failed for ${videoId}:`, e.message);

	            // If this is a critical error, try immediate recovery with minimal delay
	            if (e.name === 'AbortError' && i < strategies.length - 1) {
	              console.log(`🔄 INSTANT: AbortError detected, trying immediate recovery for ${videoId}`);
	              await new Promise(resolve => setTimeout(resolve, 25)); // Reduced delay
	            }
	          }
	        }

	        // If all strategies failed, increment retry count
	        this.retryAttempts.set(videoId, currentRetries + 1);

	        // If we haven't exceeded max retries, try again with minimal delay
	        if (currentRetries < this.maxRetries) {
	          console.log(`🔄 INSTANT: All strategies failed, scheduling retry ${currentRetries + 1}/${this.maxRetries} for ${videoId}`);
	          setTimeout(() => {
	            if (this.currentVideo === video) { // Only retry if still current
	              this.reliablePlay(video);
	            }
	          }, 500); // Fixed short delay for instant retry
	        } else {
	          console.log(`❌ INSTANT: Max retries exceeded for ${videoId}`);
	        }

	        return false;
	      },

	      setupClickToPlay(video) {
	        const videoId = video.id || 'unknown';
	        console.log(`👆 TIKTOK: Setting up click-to-play for ${videoId}`);

	        const clickHandler = async (e) => {
	          e.stopPropagation();
	          console.log(`👆 TIKTOK: Manual play triggered for ${videoId}`);

	          try {
	            await video.play();
	            video.removeEventListener('click', clickHandler);
	            console.log(`✅ TIKTOK: Manual play success for ${videoId}`);
	          } catch (err) {
	            console.log(`❌ TIKTOK: Manual play failed for ${videoId}:`, err.message);
	          }
	        };

	        video.addEventListener('click', clickHandler, { once: true });
	      },

	      pauseVideo(video) {
	        if (video && !video.paused) {
	          // Save state before pausing for smooth resume
	          this.saveVideoState(video);
	          video.pause();
	          console.log('⏸️ INSTANT: Paused video:', video.id);
	        }
	      },

	      preloadAdjacentVideos(currentVideo) {
	        const slides = Array.from(overlay.querySelectorAll('.shorts-slide'));
	        const currentIndex = slides.findIndex(slide => slide.contains(currentVideo));

	        // Preload next and previous videos
	        [currentIndex - 1, currentIndex + 1].forEach(index => {
	          if (index >= 0 && index < slides.length) {
	            const video = slides[index].querySelector('video');
	            if (video && !this.preloadedVideos.has(video.id)) {
	              console.log(`📥 TIKTOK: Preloading adjacent video ${video.id}`);
	              video.preload = 'metadata';
	              if (video.readyState === 0) {
	                video.load();
	              }
	              this.preloadedVideos.add(video.id);
	            }
	          }
	        });
	      },

	      // Switch to a specific video (used by scroll handler)
	      async switchToVideo(video) {
	        if (!video || video === this.currentVideo) return true;

	        console.log(`🔄 TIKTOK: Switching to video ${video.id}`);
	        return await this.playVideo(video);
	      },

	      // Force play method for initial video
	      async forcePlay(video) {
	        return await this.playVideo(video);
	      },

	      // Enhanced state management for SPA navigation
	      saveVideoState(video) {
	        if (!video || !video.id) return;

	        const state = {
	          currentTime: video.currentTime,
	          paused: video.paused,
	          muted: video.muted,
	          manuallyPaused: video.dataset.manuallyPaused === 'true',
	          timestamp: Date.now()
	        };

	        window.shortsVideoStates = window.shortsVideoStates || {};
	        window.shortsVideoStates[video.id] = state;
	        console.log('💾 RELIABLE: Saved video state for', video.id, state);
	      },

	      restoreVideoState(video) {
	        if (!video || !video.id) return false;

	        const states = window.shortsVideoStates || {};
	        const state = states[video.id];

	        if (state && (Date.now() - state.timestamp) < 300000) { // 5 minutes max
	          video.currentTime = state.currentTime;
	          video.muted = state.muted;
	          video.dataset.manuallyPaused = state.manuallyPaused ? 'true' : 'false';
	          console.log('🔄 RELIABLE: Restored video state for', video.id, state);
	          return true;
	        }

	        return false;
	      },

	      // Clean up old states to prevent memory leaks
	      cleanupOldStates() {
	        if (!window.shortsVideoStates) return;

	        const now = Date.now();
	        const maxAge = 300000; // 5 minutes

	        Object.keys(window.shortsVideoStates).forEach(videoId => {
	          const state = window.shortsVideoStates[videoId];
	          if (now - state.timestamp > maxAge) {
	            delete window.shortsVideoStates[videoId];
	          }
	        });
	      }
	    };

	    // TikTok-style video preloader for smooth experience
	    const preloadObserver = new IntersectionObserver((entries) => {
	      entries.forEach(entry => {
	        if (entry.isIntersecting && entry.intersectionRatio > 0.9) {
	          const video = entry.target.querySelector('video');
	          if (video && video.preload === 'none') {
	            console.log(`📥 DATA SAVING: Minimal preloading video ${video.id}`);
	            video.preload = 'metadata';
	            if (video.readyState === 0) {
	              video.load();
	            }
	            window.shortsAutoplayManager.preloadedVideos.add(video.id);
	          }
	        }
	      });
	    }, {
	      threshold: [0.9], // Only when almost fully visible - extreme data saving
	      rootMargin: '10px' // Minimal margin to prevent background loading
	    });

	    // Observe all slides for preloading
	    const observeSlides = () => {
	      overlay.querySelectorAll('.shorts-slide').forEach(slide => {
	        preloadObserver.observe(slide);
	      });
	    };

	    // Periodic health check and cleanup system for maximum reliability
	    const healthCheckInterval = setInterval(() => {
	      if (!overlay || overlay.style.display === 'none') {
	        clearInterval(healthCheckInterval);
	        return;
	      }

	      // Clean up old video states
	      if (window.shortsAutoplayManager) {
	        window.shortsAutoplayManager.cleanupOldStates();
	      }

	      // Check for stuck videos and recover
	      overlay.querySelectorAll('video').forEach(video => {
	        if (video.error) {
	          console.log(`🔧 RELIABLE: Detected video error for ${video.id}, attempting recovery`);
	          const src = video.src;
	          video.src = '';
	          video.load();
	          setTimeout(() => {
	            video.src = src;
	            video.load();
	          }, 100);
	        }
	      });

	      console.log('💚 RELIABLE: Health check completed');
	    }, 30000); // Every 30 seconds

	    // Keep the fast dynamic loading system - no changes needed here

	    // Initial video setup - INSTANT START with new system
	    setTimeout(() => {
	      observeSlides();

	      // Find and start the initial video from timeline
	      const initialVideo = overlay.querySelector('.shorts-video[data-is-initial-video="true"]') ||
	                          overlay.querySelector('.shorts-video');

	      if (initialVideo) {
	        console.log('🚀 INSTANT: Found initial video from timeline:', initialVideo.id);

	        // Ensure video respects global mute state (like themes699)
	        initialVideo.muted = window.__globalMute !== false;
	        console.log('🔊 INSTANT: Initial video mute state:', initialVideo.muted, '(global mute:', window.__globalMute, ')');

	        // Set as current playing video immediately
	        currentPlayingVideo = initialVideo;

	        // Start the initial video immediately with the reliable manager
	        if (window.shortsAutoplayManager) {
	          console.log('✅ INSTANT: Manager ready, starting initial video');
	          window.shortsAutoplayManager.playVideo(initialVideo).then(success => {
	            if (success) {
	              console.log('✅ INSTANT: Initial video started successfully');
	            } else {
	              console.log('⚠️ INSTANT: Initial video autoplay failed, click-to-play enabled');
	            }
	          }).catch(error => {
	            console.log('❌ INSTANT: Error starting initial video:', error);
	          });
	        } else {
	          console.log('⏳ INSTANT: Manager not ready, waiting...');
	          setTimeout(() => {
	            if (window.shortsAutoplayManager) {
	              console.log('✅ INSTANT: Manager ready after wait, starting initial video');
	              window.shortsAutoplayManager.playVideo(initialVideo);
	            }
	          }, 200);
	        }
	      } else {
	        console.log('⚠️ INSTANT: No initial video found');
	      }
	    }, 300); // Shorter delay for faster startup

	    // Re-observe when new slides are added - simplified for new system
	    const originalReobserve = window.reobserveShorts;
	    window.reobserveShorts = function() {
	      console.log('📹 INSTANT: Adding handlers to new videos...');
	      // Call preload observer for new videos
	      overlay.querySelectorAll('.shorts-slide').forEach(slide => {
	        preloadObserver.observe(slide);
	      });
	      // Also call original function if it exists
	      if (originalReobserve) {
	        originalReobserve();
	      }
	    };

	    // Add keyboard navigation for desktop users
	    document.addEventListener('keydown', (e) => {
	      if (overlay.style.display === 'none') return; // Only when shorts viewer is open

	      if (e.key === 'ArrowDown' || e.key === 'PageDown') {
	        e.preventDefault();
	        const currentIndex = window.shortsState.currentIndex;
	        const nextIndex = currentIndex + 1;

	        // Check if we need to load more content
	        if (nextIndex >= window.shortsState.loadedVideos.length - 1 && window.shortsState.hasMoreContent) {
	          loadMoreShorts().then(() => {
	            overlay.scrollTo(0, nextIndex * window.innerHeight);
	          });
	        } else if (nextIndex < window.shortsState.loadedVideos.length) {
	          overlay.scrollTo(0, nextIndex * window.innerHeight);
	        }
	      } else if (e.key === 'ArrowUp' || e.key === 'PageUp') {
	        e.preventDefault();
	        const currentIndex = window.shortsState.currentIndex;
	        const prevIndex = Math.max(0, currentIndex - 1);
	        overlay.scrollTo(0, prevIndex * window.innerHeight);
	      }
	    });

	    // Mouse wheel support - only for loading more content, let browser handle scrolling
	    overlay.addEventListener('wheel', (e) => {
	      // Only handle loading more content when scrolling down near the end
	      if (e.deltaY > 0) {
	        const currentSlideIndex = Math.round(overlay.scrollTop / window.innerHeight);
	        const nextIndex = currentSlideIndex + 1;
	        if (nextIndex >= window.shortsState.loadedVideos.length - 1 && window.shortsState.hasMoreContent) {
	          loadMoreShorts();
	        }
	      }
	    }, { passive: true });

	    // Set up videos and register with unified manager
	    slides.forEach(slide => {
	      const video = slide.querySelector('video');
	      if (!video) return;

	      video.controls = false;
	      video.loop = true;
	      video.muted = window.__globalMute === undefined ? true : window.__globalMute;

	      // Smart registration with unified manager
	      if (window.__unifiedVideoManager) {
	        // Only register if not already registered
	        if (!window.__unifiedVideoManager.allVideos.has(video)) {
	          window.__unifiedVideoManager.registerVideo(video, 'shorts');
	          console.log('Registered shorts video:', video.id);
	        } else {
	          console.log('Video already registered:', video.id);
	        }
	      }

	      // Setup aspect ratio detection for the video
	      setupAspectRatioDetection(video);

	      // Video tap handling is managed by the unified tap detection system
	      console.log('🎯 Initial video tap handling managed by unified system:', video.id);

	      // Ensure video mute state matches global state
	      video.muted = window.__globalMute !== false;
	      console.log('🔊 Set initial video mute state:', video.muted, 'based on global:', window.__globalMute);

	      // Pause indicator clicks are handled by the unified double-tap handler
	    });

	    // HLS initialization is now handled earlier in openOverlay function

	    // Preload videos for smoother experience (TikTok-style)
	    preloadNearbyVideos();

	    // Handle initial video spinner (fix for first video spinner issue)
	    handleInitialVideoSpinner();

	    // Force hide spinners for any playing videos after initialization
	    setTimeout(() => {
	      forceHideSpinnersForPlayingVideos();
	    }, 100);

	    // Additional check after a longer delay
	    setTimeout(() => {
	      forceHideSpinnersForPlayingVideos();
	    }, 500);

	    // Initial check
	    handleVideoPlayback();

	    // Initialize dual-tap functionality
	    initMultiTapFunctionality();




	  }

	  // Handle initial video spinner issue
	  function handleInitialVideoSpinner() {
	    const firstSlide = overlay.querySelector('.shorts-slide');
	    if (!firstSlide) return;

	    const firstVideo = firstSlide.querySelector('video');
	    const firstSpinner = firstSlide.querySelector('.loading-spinner');

	    if (firstVideo && firstSpinner) {
	      // Check if the first video is already playing or ready
	      const checkVideoState = () => {
	        if (!firstVideo.paused || firstVideo.readyState >= 3) {
	          // Video is playing or ready, hide spinner immediately
	          firstSpinner.style.display = 'none';
	          console.log('Initial video ready, hiding spinner');
	          return;
	        }

	        // If video has src and is loading, give it a moment
	        if (firstVideo.src && firstVideo.readyState >= 1) {
	          setTimeout(() => {
	            if (!firstVideo.paused || firstVideo.readyState >= 3) {
	              firstSpinner.style.display = 'none';
	              console.log('Initial video loaded after delay, hiding spinner');
	            }
	          }, 200);
	        }
	      };

	      // Check immediately
	      checkVideoState();

	      // Also check after a short delay in case video is still loading
	      setTimeout(checkVideoState, 100);
	      setTimeout(checkVideoState, 500);

	      // Listen for video events to hide spinner
	      const hideInitialSpinner = () => {
	        firstSpinner.style.display = 'none';
	        console.log('Initial video event triggered, hiding spinner');
	      };

	      firstVideo.addEventListener('playing', hideInitialSpinner, { once: true });
	      firstVideo.addEventListener('canplay', hideInitialSpinner, { once: true });
	      firstVideo.addEventListener('timeupdate', hideInitialSpinner, { once: true });
	    }
	  }

	  // Data-efficient video loading system
	  function preloadNearbyVideos() {
	    const slides = overlay.querySelectorAll('.shorts-slide');

	    // Detect connection type for data-saving
	    const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
	    const isSlowConnection = connection && (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g' || connection.effectiveType === '3g');
	    const isSaveData = connection && connection.saveData;

	    console.log('Connection info:', {
	      effectiveType: connection?.effectiveType,
	      saveData: isSaveData,
	      isSlowConnection: isSlowConnection
	    });

	    slides.forEach((slide, index) => {
	      const video = slide.querySelector('video');
	      if (!video) return;

	      // ULTRA AGGRESSIVE preloading for instant playback: preload first 5 videos with 'auto'
	      const shouldPreload = index < 5; // Increased from 3 to 5 for smoother scrolling
	      const isFirstVideo = index === 0;

	      if (shouldPreload) {
	        const src = video.dataset.src || video.getAttribute('data-src');
	        if (src && !video.src) {
	          video.src = src;

	          // Data-efficient preloading - only metadata for smooth playback
	          if (isFirstVideo) {
	            video.preload = 'metadata'; // Only metadata for first video
	          } else if (isSlowConnection || isSaveData) {
	            video.preload = 'none'; // No preload on slow connections
	          } else {
	            video.preload = 'metadata'; // Only metadata for data efficiency
	          }

	          video.load();
	          video.dataset.loaded = '1';
	          console.log(`⚡ Preloaded video ${index + 1} with preload=${video.preload} for instant playback`);
	        }
	      }
	    });

	    // Set up intersection observer for lazy loading with data-saving
	    const observer = new IntersectionObserver((entries) => {
	      entries.forEach(entry => {
	        if (entry.isIntersecting) {
	          const video = entry.target.querySelector('video');
	          if (video && !video.dataset.loaded) {
	            const src = video.dataset.src || video.getAttribute('data-src');
	            if (src && !video.src) {
	              video.src = src;
	              // Use metadata preload for data saving - never use auto
	              video.preload = 'metadata'; // Always use metadata to save data
	              video.load();
	              video.dataset.loaded = '1';
	              console.log('Lazy loaded video with data-saving preload');
	            }
	          }
	          observer.unobserve(entry.target);
	        }
	      });
	    }, {
	      root: overlay,
	      // Minimal margin to prevent any background loading
	      rootMargin: '0px' // No margin - only load when actually visible
	    });

	    // Observe slides for lazy loading
	    slides.forEach(slide => {
	      observer.observe(slide);
	    });
	  }





	  // TikTok-Style Tap Functionality: Single=Pause/Play, Double=Like (Less Sensitive)
	  function initMultiTapFunctionality() {
	    let lastTapTime = 0;
	    let tapCount = 0;
	    let tapTimeout = null;
	    let isProcessingDoubleTap = false;
	    let lastTapPosition = { x: 0, y: 0 };
	    let touchStartTime = 0;
	    let touchStartPosition = { x: 0, y: 0 };

	    // Remove existing listeners to prevent duplicates
	    overlay.removeEventListener('touchstart', handleTouchStart, true);
	    overlay.removeEventListener('touchend', handleMultiTap, true);
	    overlay.removeEventListener('click', handleMultiTap, true);

	    // Add touch start listener to track touch duration and movement
	    overlay.addEventListener('touchstart', handleTouchStart, true);
	    // Use event delegation to handle all slides, including dynamically created ones
	    overlay.addEventListener('touchend', handleMultiTap, true);
	    overlay.addEventListener('click', handleMultiTap, true);

	    console.log('TikTok-style functionality initialized: Single=Pause/Play, Double=Like (Less Sensitive)');

	    function handleTouchStart(e) {
	      touchStartTime = Date.now();
	      touchStartPosition = {
	        x: e.touches[0].clientX,
	        y: e.touches[0].clientY
	      };
	    }

	    function handleMultiTap(e) {
	      const target = e.target;

	      // Handle pause indicator clicks immediately (no multi-tap delay)
	      if (target.classList.contains('pause-indicator') || target.closest('.pause-indicator')) {
	        e.preventDefault();
	        e.stopPropagation();

	        const activeSlide = getCurrentActiveSlide();
	        if (activeSlide) {
	          const video = activeSlide.querySelector('video');
	          if (video && video.paused) {
	            console.log('🎯 Pause indicator clicked, resuming video:', video.id);
	            const pauseIndicator = video.parentElement.querySelector('.pause-indicator');
	            if (pauseIndicator) {
	              pauseIndicator.style.display = 'none';
	            }
	            // Clear manual pause flag when user resumes via pause indicator
	            video.dataset.manuallyPaused = 'false';
	            if (window.__unifiedVideoManager) {
	              window.__unifiedVideoManager.playVideo(video, 'shorts');
	            } else {
	              video.play().catch(e => console.log('Resume from pause indicator failed:', e));
	            }
	          }
	        }
	        return;
	      }

	      // Only handle taps on video elements directly, not on buttons/controls/UI elements
	      if (target.tagName === 'BUTTON' || target.closest('button') ||
	          target.closest('.shorts-actions') || target.closest('.shorts-publisher') ||
	          target.closest('.dropdown-menu') || target.id === 'shortsCloseBtn' ||
	          target.closest('.shorts-info') || target.closest('.shorts-controls')) {
	        return; // Don't interfere with button clicks or UI elements
	      }

	      // Allow clicks anywhere in video area to toggle pause/play
	      const activeSlide = getCurrentActiveSlide();
	      const isVideoAreaClick = target.tagName === 'VIDEO' ||
	                              target.closest('video') ||
	                              (target.closest('.shorts-slide') === activeSlide &&
	                               !target.closest('.shorts-actions') &&
	                               !target.closest('.shorts-publisher'));

	      if (!isVideoAreaClick) {
	        return;
	      }

	      const currentTime = Date.now();
	      const timeSinceLastTap = currentTime - lastTapTime;

	      // Get current tap position for more precise double-tap detection
	      const currentTapPosition = {
	        x: e.touches ? e.changedTouches[0].clientX : e.clientX,
	        y: e.touches ? e.changedTouches[0].clientY : e.clientY
	      };

	      // Calculate touch duration and movement to filter out accidental touches
	      const touchDuration = currentTime - touchStartTime;
	      const touchMovement = Math.sqrt(
	        Math.pow(currentTapPosition.x - touchStartPosition.x, 2) +
	        Math.pow(currentTapPosition.y - touchStartPosition.y, 2)
	      );

	      // Filter out accidental touches: Much stricter like TikTok
	      const isValidTouch = touchDuration >= 100 && // Minimum 100ms touch (increased from 50ms)
	                          touchDuration <= 600 && // Maximum 600ms touch (decreased from 800ms)
	                          touchMovement < 20;     // Maximum 20px movement (decreased from 30px)

	      if (!isValidTouch) {
	        console.log('📱 Invalid touch filtered out (TikTok-style):', {
	          duration: touchDuration,
	          movement: touchMovement,
	          required: 'duration: 100-600ms, movement: <20px'
	        });
	        return;
	      }

	      // Calculate distance between taps for double-tap detection
	      const tapDistance = Math.sqrt(
	        Math.pow(currentTapPosition.x - lastTapPosition.x, 2) +
	        Math.pow(currentTapPosition.y - lastTapPosition.y, 2)
	      );

	      clearTimeout(tapTimeout);

	      // TikTok-style double-tap detection: Much stricter timing + position proximity
	      if (timeSinceLastTap < 350 && timeSinceLastTap > 80 && tapDistance < 40 && tapCount === 1) {
	        // Valid double-tap detected!
	        console.log('🔥 DOUBLE-TAP: Triggering like animation', {
	          timeSinceLastTap,
	          tapDistance,
	          position: currentTapPosition
	        });

	        clearTimeout(tapTimeout);
	        isProcessingDoubleTap = true;
	        tapCount = 0;
	        lastTapTime = 0;
	        lastTapPosition = { x: 0, y: 0 };

	        e.preventDefault();
	        e.stopPropagation();
	        triggerLikeAnimation(e);

	        // Reset double-tap flag after animation
	        setTimeout(() => {
	          isProcessingDoubleTap = false;
	        }, 200);
	        return;
	      }

	      // First tap - record it and wait for potential double-tap
	      console.log('📱 Single tap detected, waiting for potential double-tap...', {
	        position: currentTapPosition,
	        duration: touchDuration,
	        movement: touchMovement
	      });

	      tapCount = 1;
	      lastTapTime = currentTime;
	      lastTapPosition = currentTapPosition;

	      e.preventDefault();
	      e.stopPropagation();

	      // Set timeout to trigger pause/play only if no second tap comes (longer delay = less sensitive)
	      tapTimeout = setTimeout(() => {
	        if (tapCount === 1 && !isProcessingDoubleTap) {
	          console.log('✅ SINGLE TAP CONFIRMED: Triggering pause/play (TikTok-style delay)');
	          triggerPausePlay(e);
	        }
	        tapCount = 0;
	        lastTapTime = 0;
	        lastTapPosition = { x: 0, y: 0 };
	      }, 400); // Wait 400ms for potential double-tap (increased from 300ms for less sensitivity)
	    }



	    // Single tap: Pause/Play functionality
	    function triggerPausePlay(e) {
	      const activeSlide = getCurrentActiveSlide();
	      if (activeSlide) {
	        const video = activeSlide.querySelector('video');
	        if (video) {
	          console.log('🎬 Single tap pause/play:', video.id, 'paused:', video.paused);

	          // Prevent rapid state changes by adding a small delay flag
	          if (video.dataset.stateChanging === 'true') {
	            console.log('🚫 Video state already changing, ignoring tap');
	            return;
	          }

	          video.dataset.stateChanging = 'true';
	          setTimeout(() => {
	            video.dataset.stateChanging = 'false';
	          }, 200); // 200ms cooldown

	          if (video.paused) {
	            console.log('📱 Single tap to play:', video.id);
	            const pauseIndicator = video.parentElement.querySelector('.pause-indicator');
	            if (pauseIndicator) {
	              pauseIndicator.style.display = 'none';
	            }
	            video.dataset.manuallyPaused = 'false';
	            if (window.__unifiedVideoManager) {
	              window.__unifiedVideoManager.playVideo(video, 'shorts');
	            } else {
	              video.play().catch(e => console.log('User play failed:', e));
	            }
	          } else {
	            console.log('📱 Single tap to pause:', video.id);
	            video.pause();
	            video.dataset.manuallyPaused = 'true';
	            const pauseIndicator = video.parentElement.querySelector('.pause-indicator');
	            if (pauseIndicator) {
	              pauseIndicator.style.display = 'flex';
	            }
	          }
	        }
	      }
	    }



	    function triggerLikeAnimation(e) {
	      // Find the current active slide first
	      const activeSlide = getCurrentActiveSlide();
	      if (!activeSlide) return;

	      // Get tap/click coordinates relative to the active slide
	      const slideRect = activeSlide.getBoundingClientRect();
	      const overlayRect = overlay.getBoundingClientRect();
	      const clientX = e.touches ? e.changedTouches[0].clientX : e.clientX;
	      const clientY = e.touches ? e.changedTouches[0].clientY : e.clientY;

	      // Calculate position relative to the active slide (not overlay)
	      const x = clientX - slideRect.left;
	      const y = clientY - slideRect.top;

	      // Also calculate overlay position for comparison
	      const overlayX = clientX - overlayRect.left;
	      const overlayY = clientY - overlayRect.top;

	      console.log('Double tap detected at:', {
	        slideCoords: {x, y},
	        overlayCoords: {overlayX, overlayY},
	        slide: activeSlide,
	        slideRect: slideRect,
	        overlayRect: overlayRect
	      });

	      // Find the like button in the current slide with multiple selectors
	      let likeButton = activeSlide.querySelector('.shorts-actions .like-btn') ||
	                      activeSlide.querySelector('.shorts-actions [data-action="like"]') ||
	                      activeSlide.querySelector('.shorts-actions [onclick*="like"]') ||
	                      activeSlide.querySelector('[data-action="like"]') ||
	                      activeSlide.querySelector('[onclick*="like"]') ||
	                      activeSlide.querySelector('.like-btn') ||
	                      activeSlide.querySelector('button[onclick*="SMColibri.like"]') ||
	                      activeSlide.querySelector('button[onclick*="like_post"]') ||
	                      activeSlide.querySelector('.post-actions [onclick*="like"]') ||
	                      activeSlide.querySelector('.timeline-actions [onclick*="like"]');

	      console.log('Active slide HTML:', activeSlide.innerHTML.substring(0, 500));
	      console.log('Like button search result:', likeButton);

	      if (!likeButton) {
	        console.log('No like button found in active slide, trying alternative approach...');
	        // Try to find any clickable element with "like" in its onclick or data attributes
	        const allButtons = activeSlide.querySelectorAll('button, a, [onclick], [data-action]');
	        for (let btn of allButtons) {
	          const onclick = btn.getAttribute('onclick') || '';
	          const dataAction = btn.getAttribute('data-action') || '';
	          const className = btn.className || '';
	          if (onclick.includes('like') || dataAction.includes('like') || className.includes('like')) {
	            likeButton = btn;
	            console.log('Found like button via alternative search:', btn);
	            break;
	          }
	        }
	      }

	      if (!likeButton) {
	        console.log('Still no like button found, showing available buttons:');
	        const allButtons = activeSlide.querySelectorAll('button, a, [onclick]');
	        allButtons.forEach((btn, index) => {
	          console.log(`Button ${index}:`, btn.outerHTML.substring(0, 200));
	        });
	        return;
	      }

	      console.log('Like button found:', likeButton);

	      // Always create main love splash at tap location - this should always work
	      console.log('Creating love splash at slide coordinates:', x, y);
	      console.log('Creating love splash at overlay coordinates:', overlayX, overlayY);

	      // Create test elements in BOTH locations to see which one is visible

	      // Test 1: Add to active slide with slide coordinates
	      const testElementSlide = document.createElement('div');
	      testElementSlide.style.cssText = `
	        position: absolute !important;
	        left: ${x}px !important;
	        top: ${y}px !important;
	        width: 20px !important;
	        height: 20px !important;
	        background: red !important;
	        z-index: 99999 !important;
	        border-radius: 50% !important;
	        transform: translate(-50%, -50%) !important;
	      `;
	      activeSlide.appendChild(testElementSlide);
	      setTimeout(() => {
	        if (testElementSlide.parentNode) {
	          testElementSlide.parentNode.removeChild(testElementSlide);
	        }
	      }, 2000);

	      // Test 2: Add to overlay with overlay coordinates
	      const testElementOverlay = document.createElement('div');
	      testElementOverlay.style.cssText = `
	        position: absolute !important;
	        left: ${overlayX}px !important;
	        top: ${overlayY}px !important;
	        width: 20px !important;
	        height: 20px !important;
	        background: blue !important;
	        z-index: 99999 !important;
	        border-radius: 50% !important;
	        transform: translate(-50%, -50%) !important;
	      `;
	      overlay.appendChild(testElementOverlay);
	      setTimeout(() => {
	        if (testElementOverlay.parentNode) {
	          testElementOverlay.parentNode.removeChild(testElementOverlay);
	        }
	      }, 2000);

	      console.log('Created test elements: RED dot on slide, BLUE dot on overlay');

	      // Smart positioning: Use overlay for first video, slide for others
	      const isFirstVideo = getCurrentSlideIndex() === 0;

	      if (isFirstVideo) {
	        console.log('First video detected - using overlay positioning');
	        createSmartLoveSplash(overlay, overlayX, overlayY, '💙');
	        createLoveTrail(overlayX, overlayY, likeButton);
	      } else {
	        console.log('Subsequent video detected - using slide positioning');
	        createSmartLoveSplash(activeSlide, x, y, '❤️');
	        createLoveTrailOnSlide(activeSlide, x, y, likeButton);
	      }

	      // Always create some particle trail, even if no button found
	      createLoveTrail(x, y, likeButton);

	      if (likeButton) {
	        // Trigger like for double-tap (ONLY LIKE, NEVER UNLIKE)
	        setTimeout(() => {
	          console.log('Triggering like for double-tap:', likeButton);

	          // Check if post is already liked
	          const svg = likeButton.querySelector('svg path');
	          const isAlreadyLiked =
	            (svg && (svg.style.fill === '#ff3355' || svg.style.fill === 'rgb(255, 51, 85)')) ||
	            (likeButton.style.color === '#ff3355' || likeButton.style.color === 'rgb(255, 51, 85)') ||
	            likeButton.classList.contains('liked') ||
	            (svg && getComputedStyle(svg).fill === 'rgb(255, 51, 85)') ||
	            (getComputedStyle(likeButton).color === 'rgb(255, 51, 85)');

	          if (isAlreadyLiked) {
	            // Post is already liked - just show animation, don't unlike
	            console.log('Double-tap: Post already liked, showing animation only');

	            // Add pulse animation to show interaction
	            likeButton.classList.add('like-button-pulse');
	            setTimeout(() => {
	              likeButton.classList.remove('like-button-pulse');
	            }, 600);

	            // Don't call like function - just visual feedback
	          } else {
	            // Post is not liked - like it
	            console.log('Double-tap: Post not liked, liking it now');

	            // Extract post ID and call our instant like function
	            const onclick = likeButton.getAttribute('onclick') || '';
	            const match = onclick.match(/like_post\(['"]?(\d+)['"]?/);

	            if (match && window.SMColibri && window.SMColibri.like_post) {
	              const postId = match[1];
	              console.log('Calling instant like function for post:', postId);

	              // The like state and count will be stored automatically by our SMColibri.like_post override
	              window.SMColibri.like_post(postId, likeButton);
	            } else {
	              // Fallback to normal click
	              console.log('Fallback to normal click');
	              likeButton.click();
	            }
	          }
	        }, 300);
	      } else {
	        // If no like button found, try alternative approaches
	        console.log('No like button found, trying alternative like methods...');

	        // Try alternative like methods
	        const postId = extractPostIdFromSlide(activeSlide);
	        if (postId) {
	          setTimeout(() => {
	            console.log('Attempting like for post ID:', postId);
	            if (window.SMColibri && window.SMColibri.like_post) {
	              // The like state and count will be stored automatically by our SMColibri.like_post override
	              window.SMColibri.like_post(postId);
	            } else if (window.SMColibri && window.SMColibri.like) {
	              window.SMColibri.like(postId);
	            }
	          }, 300);
	        } else {
	          // Try to find any like element and click it
	          setTimeout(() => {
	            const likeElements = activeSlide.querySelectorAll('*');
	            for (let element of likeElements) {
	              const onclick = element.getAttribute('onclick') || '';
	              if (onclick.includes('like') && onclick.includes('(')) {
	                console.log('Found alternative like element:', element);
	                element.click();
	                break;
	              }
	            }
	          }, 300);
	        }
	      }
	    }

	    function extractPostIdFromSlide(slide) {
	      // Try multiple ways to extract post ID
	      let postId = null;

	      // Method 1: Look for data-id attributes
	      const dataIdElement = slide.querySelector('[data-id]');
	      if (dataIdElement) {
	        postId = dataIdElement.getAttribute('data-id');
	      }

	      // Method 2: Look for post ID in onclick attributes
	      if (!postId) {
	        const onclickElements = slide.querySelectorAll('[onclick]');
	        for (let element of onclickElements) {
	          const onclick = element.getAttribute('onclick');
	          const match = onclick.match(/\b(\d+)\b/);
	          if (match) {
	            postId = match[1];
	            break;
	          }
	        }
	      }

	      // Method 3: Look for post ID in the slide's data attributes
	      if (!postId) {
	        postId = slide.getAttribute('data-post-id') || slide.getAttribute('data-id');
	      }

	      console.log('Extracted post ID:', postId);
	      return postId;
	    }

	    function getCurrentActiveSlide() {
	      const slides = overlay.querySelectorAll('.shorts-slide');
	      let mostVisibleSlide = null;
	      let maxVisibleArea = 0;

	      for (let slide of slides) {
	        const rect = slide.getBoundingClientRect();
	        const overlayRect = overlay.getBoundingClientRect();

	        // Calculate visible area of this slide
	        const visibleTop = Math.max(rect.top, overlayRect.top);
	        const visibleBottom = Math.min(rect.bottom, overlayRect.bottom);
	        const visibleHeight = Math.max(0, visibleBottom - visibleTop);
	        const visibleArea = visibleHeight * rect.width;

	        if (visibleArea > maxVisibleArea) {
	          maxVisibleArea = visibleArea;
	          mostVisibleSlide = slide;
	        }
	      }

	      return mostVisibleSlide || slides[0]; // Fallback to first slide
	    }

	    function getCurrentSlideIndex() {
	      const slides = overlay.querySelectorAll('.shorts-slide');
	      const activeSlide = getCurrentActiveSlide();

	      for (let i = 0; i < slides.length; i++) {
	        if (slides[i] === activeSlide) {
	          return i;
	        }
	      }

	      return 0; // Fallback to first slide
	    }

	    function createLoveSplashOnSlide(slide, x, y) {
	      console.log('Creating love splash ON SLIDE at:', x, y);

	      // Create a test splash that stays visible for 3 seconds (no animation)
	      const testSplash = document.createElement('div');
	      testSplash.className = 'love-splash-test';
	      testSplash.innerHTML = '❤️';
	      testSplash.style.cssText = `
	        position: absolute !important;
	        left: ${x}px !important;
	        top: ${y}px !important;
	        transform: translate(-50%, -50%) !important;
	        pointer-events: none !important;
	        z-index: 99999 !important;
	        font-size: 60px !important;
	        color: #ff3355 !important;
	        display: block !important;
	        visibility: visible !important;
	        opacity: 1 !important;
	        width: auto !important;
	        height: auto !important;
	        background: transparent !important;
	        border: none !important;
	        margin: 0 !important;
	        padding: 0 !important;
	        line-height: 1 !important;
	        font-family: emoji !important;
	      `;

	      slide.appendChild(testSplash);
	      console.log('Test love splash created ON SLIDE (no animation) - should be visible for 3 seconds');

	      // Remove after 3 seconds
	      setTimeout(() => {
	        if (testSplash.parentNode) {
	          testSplash.parentNode.removeChild(testSplash);
	          console.log('Test love splash ON SLIDE removed');
	        }
	      }, 3000);
	    }

	    function createLoveSplashOnOverlay(x, y) {
	      console.log('Creating love splash ON OVERLAY at:', x, y);

	      // Create a test splash that stays visible for 3 seconds (no animation)
	      const testSplash = document.createElement('div');
	      testSplash.className = 'love-splash-test';
	      testSplash.innerHTML = '💙';
	      testSplash.style.cssText = `
	        position: absolute !important;
	        left: ${x}px !important;
	        top: ${y}px !important;
	        transform: translate(-50%, -50%) !important;
	        pointer-events: none !important;
	        z-index: 99999 !important;
	        font-size: 60px !important;
	        color: #0066ff !important;
	        display: block !important;
	        visibility: visible !important;
	        opacity: 1 !important;
	        width: auto !important;
	        height: auto !important;
	        background: transparent !important;
	        border: none !important;
	        margin: 0 !important;
	        padding: 0 !important;
	        line-height: 1 !important;
	        font-family: emoji !important;
	      `;

	      overlay.appendChild(testSplash);
	      console.log('Test love splash created ON OVERLAY (no animation) - should be visible for 3 seconds');

	      // Remove after 3 seconds
	      setTimeout(() => {
	        if (testSplash.parentNode) {
	          testSplash.parentNode.removeChild(testSplash);
	          console.log('Test love splash ON OVERLAY removed');
	        }
	      }, 3000);
	    }

	    function createSmartLoveSplash(container, x, y, emoji = '❤️') {
	      console.log('Creating smart love splash at:', x, y, 'in container:', container, 'with emoji:', emoji);

	      // Create main love splash
	      const splash = document.createElement('div');
	      splash.className = 'love-splash';
	      splash.innerHTML = emoji;
	      splash.style.cssText = `
	        position: absolute !important;
	        left: ${x}px !important;
	        top: ${y}px !important;
	        transform: translate(-50%, -50%) !important;
	        pointer-events: none !important;
	        z-index: 99999 !important;
	        font-size: 60px !important;
	        color: #ff3355 !important;
	        display: block !important;
	        visibility: visible !important;
	        opacity: 1 !important;
	        animation: loveSplash 3s ease-out forwards !important;
	        width: auto !important;
	        height: auto !important;
	        background: transparent !important;
	        border: none !important;
	        margin: 0 !important;
	        padding: 0 !important;
	        line-height: 1 !important;
	        font-family: emoji !important;
	      `;

	      container.appendChild(splash);
	      console.log('Smart love splash created and added to container');

	      // Create particles around the splash
	      for (let i = 0; i < 6; i++) {
	        setTimeout(() => {
	          createLoveParticleInContainer(container, x, y, i);
	        }, i * 50);
	      }

	      // Remove splash after animation
	      setTimeout(() => {
	        if (splash.parentNode) {
	          splash.parentNode.removeChild(splash);
	          console.log('Smart love splash removed after animation');
	        }
	      }, 3000);
	    }

	    function createLoveParticleInContainer(container, centerX, centerY, index) {
	      const particle = document.createElement('div');
	      particle.className = 'love-particle';

	      // Random position around the center
	      const angle = (index * 60) + Math.random() * 30;
	      const distance = 30 + Math.random() * 20;
	      const x = centerX + Math.cos(angle * Math.PI / 180) * distance;
	      const y = centerY + Math.sin(angle * Math.PI / 180) * distance;

	      particle.style.cssText = `
	        position: absolute !important;
	        left: ${x}px !important;
	        top: ${y}px !important;
	        width: 8px !important;
	        height: 8px !important;
	        background: #ff3355 !important;
	        border-radius: 50% !important;
	        animation: loveParticle 1s ease-out forwards !important;
	        animation-delay: ${Math.random() * 0.2}s !important;
	        z-index: 99998 !important;
	        pointer-events: none !important;
	        display: block !important;
	        visibility: visible !important;
	      `;

	      container.appendChild(particle);

	      // Remove particle after animation
	      setTimeout(() => {
	        if (particle.parentNode) {
	          particle.parentNode.removeChild(particle);
	        }
	      }, 1000);
	    }

	    function createLoveTrailOnSlide(slide, startX, startY, targetButton) {
	      if (!targetButton) {
	        console.log('No target button for slide trail');
	        return;
	      }

	      const buttonRect = targetButton.getBoundingClientRect();
	      const slideRect = slide.getBoundingClientRect();
	      const targetX = buttonRect.left + buttonRect.width / 2 - slideRect.left;
	      const targetY = buttonRect.top + buttonRect.height / 2 - slideRect.top;

	      console.log('Creating love trail on slide from', startX, startY, 'to', targetX, targetY);

	      // Create multiple trail hearts
	      for (let i = 0; i < 5; i++) {
	        setTimeout(() => {
	          const trail = document.createElement('div');
	          trail.className = 'love-trail';
	          trail.innerHTML = '💕';

	          // Calculate intermediate position
	          const progress = i / 4;
	          const x = startX + (targetX - startX) * progress;
	          const y = startY + (targetY - startY) * progress;

	          trail.style.cssText = `
	            position: absolute !important;
	            left: ${x}px !important;
	            top: ${y}px !important;
	            transform: translate(-50%, -50%) !important;
	            font-size: 24px !important;
	            color: #ff3355 !important;
	            animation: loveTrail 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards !important;
	            animation-delay: ${i * 0.1}s !important;
	            z-index: 99997 !important;
	            pointer-events: none !important;
	            display: block !important;
	            visibility: visible !important;
	          `;

	          slide.appendChild(trail);

	          // Remove trail after animation
	          setTimeout(() => {
	            if (trail.parentNode) {
	              trail.parentNode.removeChild(trail);
	            }
	          }, 800);
	        }, i * 60);
	      }
	    }

	    function createLoveSplash(x, y) {
	      console.log('Creating love splash at:', x, y);

	      // Create a test splash that stays visible for 3 seconds (no animation)
	      const testSplash = document.createElement('div');
	      testSplash.className = 'love-splash-test';
	      testSplash.innerHTML = '❤️';
	      testSplash.style.cssText = `
	        position: absolute !important;
	        left: ${x}px !important;
	        top: ${y}px !important;
	        transform: translate(-50%, -50%) !important;
	        pointer-events: none !important;
	        z-index: 99999 !important;
	        font-size: 60px !important;
	        color: #ff3355 !important;
	        display: block !important;
	        visibility: visible !important;
	        opacity: 1 !important;
	        width: auto !important;
	        height: auto !important;
	        background: transparent !important;
	        border: none !important;
	        margin: 0 !important;
	        padding: 0 !important;
	        line-height: 1 !important;
	        font-family: emoji !important;
	      `;

	      overlay.appendChild(testSplash);
	      console.log('Test love splash created (no animation) - should be visible for 3 seconds');

	      // Remove after 3 seconds
	      setTimeout(() => {
	        if (testSplash.parentNode) {
	          testSplash.parentNode.removeChild(testSplash);
	          console.log('Test love splash removed');
	        }
	      }, 3000);

	      // Also create the animated version
	      const splash = document.createElement('div');
	      splash.className = 'love-splash';
	      splash.innerHTML = '💕';
	      splash.style.cssText = `
	        position: absolute !important;
	        left: ${x + 30}px !important;
	        top: ${y}px !important;
	        transform: translate(-50%, -50%) !important;
	        pointer-events: none !important;
	        z-index: 99999 !important;
	        font-size: 60px !important;
	        color: #ff3355 !important;
	        display: block !important;
	        visibility: visible !important;
	        opacity: 1 !important;
	        animation: loveSplash 3s ease-out forwards !important;
	        width: auto !important;
	        height: auto !important;
	        background: transparent !important;
	        border: none !important;
	        margin: 0 !important;
	        padding: 0 !important;
	        line-height: 1 !important;
	        font-family: emoji !important;
	      `;

	      overlay.appendChild(splash);
	      console.log('Animated love splash created');

	      // Double-check that both elements are visible
	      setTimeout(() => {
	        const testRect = testSplash.getBoundingClientRect();
	        const splashRect = splash.getBoundingClientRect();
	        console.log('Love splash visibility check:', {
	          testSplash: {element: testSplash, rect: testRect, visible: testRect.width > 0 && testRect.height > 0},
	          animatedSplash: {element: splash, rect: splashRect, visible: splashRect.width > 0 && splashRect.height > 0}
	        });
	      }, 100);

	      // Create additional particles around the main splash
	      for (let i = 0; i < 6; i++) {
	        setTimeout(() => {
	          createLoveParticle(x, y, i);
	        }, i * 50);
	      }

	      // Remove splash after animation (3 seconds)
	      setTimeout(() => {
	        if (splash.parentNode) {
	          splash.parentNode.removeChild(splash);
	          console.log('Animated love splash removed after 3 seconds');
	        }
	      }, 3000);
	    }

	    function createLoveParticle(centerX, centerY, index) {
	      const particle = document.createElement('div');
	      particle.className = 'love-particle';

	      // Random position around the center
	      const angle = (index * 60) + Math.random() * 30; // Spread particles in a circle
	      const distance = 30 + Math.random() * 20;
	      const x = centerX + Math.cos(angle * Math.PI / 180) * distance;
	      const y = centerY + Math.sin(angle * Math.PI / 180) * distance;

	      particle.style.left = x + 'px';
	      particle.style.top = y + 'px';
	      particle.style.animationDelay = Math.random() * 0.2 + 's';

	      overlay.appendChild(particle);

	      // Remove particle after animation
	      setTimeout(() => {
	        if (particle.parentNode) {
	          particle.parentNode.removeChild(particle);
	        }
	      }, 1000);
	    }

	    function createLoveTrail(startX, startY, targetButton) {
	      let targetX, targetY;

	      if (targetButton) {
	        const buttonRect = targetButton.getBoundingClientRect();
	        const overlayRect = overlay.getBoundingClientRect();
	        targetX = buttonRect.left + buttonRect.width / 2 - overlayRect.left;
	        targetY = buttonRect.top + buttonRect.height / 2 - overlayRect.top;
	      } else {
	        // Default to right side of screen if no button found
	        targetX = window.innerWidth - 60;
	        targetY = window.innerHeight / 2;
	      }

	      // Create multiple trail hearts
	      for (let i = 0; i < 5; i++) {
	        setTimeout(() => {
	          const trail = document.createElement('div');
	          trail.className = 'love-trail';
	          trail.innerHTML = '💕';

	          // Calculate intermediate position
	          const progress = i / 4;
	          const x = startX + (targetX - startX) * progress;
	          const y = startY + (targetY - startY) * progress;

	          trail.style.left = x + 'px';
	          trail.style.top = y + 'px';
	          trail.style.transform = 'translate(-50%, -50%)';
	          trail.style.animationDelay = (i * 0.1) + 's';

	          overlay.appendChild(trail);

	          // Remove trail after animation
	          setTimeout(() => {
	            if (trail.parentNode) {
	              trail.parentNode.removeChild(trail);
	            }
	          }, 800);
	        }, i * 60);
	      }
	    }
	  }





	  function updateLikeCountFast(button, increment) {
	    // Find like count near the button
	    const slide = button.closest('.shorts-slide');
	    if (!slide) return;

	    const countSelectors = [
	      '.like-count', '.likes-count', '.post-likes-count',
	      '[class*="like"][class*="count"]', '.ctrls-item .count'
	    ];

	    let countElement = null;
	    for (let selector of countSelectors) {
	      countElement = slide.querySelector(selector);
	      if (countElement) break;
	    }

	    if (countElement) {
	      const currentCount = parseInt(countElement.textContent.replace(/[^\d]/g, '')) || 0;
	      const newCount = increment ? currentCount + 1 : Math.max(0, currentCount - 1);

	      console.log(`Updating count from ${currentCount} to ${newCount} (increment: ${increment})`);

	      // Instant update with animation
	      countElement.style.transition = 'transform 0.15s ease';
	      countElement.style.transform = 'scale(1.1)';
	      countElement.textContent = newCount.toString();

	      setTimeout(() => {
	        countElement.style.transform = 'scale(1)';
	      }, 150);
	    } else {
	      console.log('No like count element found');
	    }
	  }

	  // Disabled complex instant like system to prevent conflicts
	  function overrideOriginalLikeFunctions() {
	    console.log('Complex instant like system disabled to prevent conflicts');
	    // All instant like functionality disabled to prevent malfunctions
	    return;

	    // Store original functions
	    if (window.SMColibri && window.SMColibri.like_post && !window._originalLikePost) {
	      window._originalLikePost = window.SMColibri.like_post;

	      // Override with our version that preserves instant UI
	      window.SMColibri.like_post = function(postId, element) {
	        console.log('Intercepted SMColibri.like_post call for post:', postId);

	        // Check if this button was instantly processed
	        if (element && element.hasAttribute('data-instant-processed')) {
	          console.log('Button was instantly processed, preserving UI state');

	          // Store current UI state
	          const currentLikedState = element.classList.contains('liked') ||
	                                   element.style.color === '#ff3355' ||
	                                   element.style.color === 'rgb(255, 51, 85)';
	          const currentColor = element.style.color;
	          const currentClasses = element.className;

	          // Call original function
	          const result = window._originalLikePost.call(this, postId, element);

	          // Restore our instant UI state after a short delay
	          setTimeout(() => {
	            if (currentLikedState) {
	              element.classList.add('liked');
	              element.style.color = '#ff3355';
	              element.style.setProperty('color', '#ff3355', 'important');
	              if (element.querySelector('svg')) {
	                element.querySelector('svg').style.fill = '#ff3355';
	              }
	            } else {
	              element.classList.remove('liked', 'active');
	              element.style.color = '';
	              element.style.removeProperty('color');
	              if (element.querySelector('svg')) {
	                element.querySelector('svg').style.fill = '';
	              }
	            }
	            console.log('Restored instant UI state after server response');
	          }, 100);

	          return result;
	        } else {
	          // Normal processing for non-instant clicks
	          return window._originalLikePost.call(this, postId, element);
	        }
	      };
	    }

	    // Also override the generic like function if it exists
	    if (window.SMColibri && window.SMColibri.like && !window._originalLike) {
	      window._originalLike = window.SMColibri.like;

	      window.SMColibri.like = function(postId, element) {
	        console.log('Intercepted SMColibri.like call for post:', postId);

	        if (element && element.hasAttribute('data-instant-processed')) {
	          const currentLikedState = element.classList.contains('liked') ||
	                                   element.style.color === '#ff3355';

	          const result = window._originalLike.call(this, postId, element);

	          setTimeout(() => {
	            if (currentLikedState) {
	              element.classList.add('liked');
	              element.style.color = '#ff3355';
	              element.style.setProperty('color', '#ff3355', 'important');
	            } else {
	              element.classList.remove('liked', 'active');
	              element.style.color = '';
	            }
	          }, 100);

	          return result;
	        } else {
	          return window._originalLike.call(this, postId, element);
	        }
	      };
	    }
	  }

	  // Initialize instant like for regular button clicks
	  function initInstantLikeClicks() {
	    console.log('Initializing instant like for regular clicks');

	    // Track processed buttons to prevent double-processing
	    const processedButtons = new WeakSet();

	    // Use event delegation to handle all like buttons
	    overlay.addEventListener('click', function(e) {
	      const target = e.target;
	      const likeButton = target.closest('[onclick*="like"]') ||
	                        target.closest('[data-action="like"]') ||
	                        target.closest('.like-btn');

	      if (likeButton && !e.defaultPrevented && !processedButtons.has(likeButton) && !e.isSynthetic) {
	        // Check if this is a like button click
	        const onclick = likeButton.getAttribute('onclick') || '';
	        const dataAction = likeButton.getAttribute('data-action') || '';

	        if (onclick.includes('like') || dataAction.includes('like')) {
	          console.log('Intercepting like button click for instant feedback');

	          // Mark as processed to prevent double-processing
	          processedButtons.add(likeButton);

	          // Remove from processed set after a short delay
	          setTimeout(() => {
	            processedButtons.delete(likeButton);
	          }, 1000);

	          // Prevent the default click to avoid double-processing
	          e.preventDefault();
	          e.stopPropagation();

	          // Find the current slide
	          const slide = likeButton.closest('.shorts-slide');
	          if (slide) {
	            // Perform instant like with immediate UI feedback
	            performInstantLikeWithoutClick(likeButton, slide);
	          }
	        }
	      }
	    }, true); // Use capture phase to intercept before other handlers
	  }

	  // Instant Like System for Fast UI Response
	  function performInstantLike(likeButton, slide) {
	    console.log('Performing instant like with immediate UI feedback (for double-tap)');

	    // 1. Immediate UI feedback - update button appearance instantly
	    const wasLiked = updateLikeButtonInstantly(likeButton);

	    // 2. Add pulse animation to button
	    likeButton.classList.add('like-button-pulse');
	    setTimeout(() => {
	      likeButton.classList.remove('like-button-pulse');
	    }, 600);

	    // 3. Update like count instantly if visible
	    updateLikeCountInstantly(slide, !wasLiked);

	    // 4. Trigger actual server request in background (non-blocking)
	    setTimeout(() => {
	      try {
	        // Create a synthetic click event to avoid our interceptor
	        const syntheticEvent = new Event('click', { bubbles: false });
	        syntheticEvent.isSynthetic = true;
	        likeButton.dispatchEvent(syntheticEvent);
	      } catch (error) {
	        console.log('Error with synthetic click:', error);
	        // Try direct function call instead
	        const onclick = likeButton.getAttribute('onclick');
	        if (onclick) {
	          try {
	            eval(onclick);
	          } catch (evalError) {
	            console.log('Error with eval onclick:', evalError);
	          }
	        }
	      }
	    }, 100);
	  }

	  // Simplified shorts like handler - just call original function
	  function performInstantLikeWithoutClick(likeButton, slide) {
	    console.log('Performing shorts like - calling original function');

	    // Extract post ID and call original like function
	    const postId = extractPostIdFromButton(likeButton);
	    if (postId && window.SMColibri && window.SMColibri.like_post) {
	      // Just call the original function - it handles everything correctly
	      window.SMColibri.like_post(postId, likeButton);
	    } else {
	      // Fallback to click
	      likeButton.click();
	    }
	  }

	  // Removed complex state maintenance - let original function handle it

	  function extractPostIdFromButton(button) {
	    const onclick = button.getAttribute('onclick') || '';
	    const match = onclick.match(/like_post\(['"]?(\d+)['"]?/);
	    return match ? match[1] : null;
	  }

	  // Simplified like by ID - just call original function
	  function performInstantLikeById(postId, slide) {
	    console.log('Performing like by post ID:', postId);

	    // Find like button and call original function
	    const likeButton = slide.querySelector('[onclick*="like_post"]');
	    if (likeButton && window.SMColibri && window.SMColibri.like_post) {
	      window.SMColibri.like_post(postId, likeButton);
	    }
	  }

	  // Removed complex button update - let original SMColibri.like_post handle it

	  // Removed complex count update - let original SMColibri.like_post handle it

	  // Minimal tracking - only store state when sync functions are called
	  function initGlobalLikeEnhancement() {
	    console.log('Initializing minimal like state tracking');
	    // No event listeners - just let the original function work completely normally
	    // We'll capture states only when syncing between timeline and shorts
	  }

	  // This function is no longer needed - removed complex override system

	  // Removed complex count update - let original SMColibri.like_post handle it

	  // Function to sync like states from timeline to shorts viewer
	  function syncLikeStatesOnShortsOpen() {
	    console.log('Syncing like states from timeline to shorts viewer');

	    // Wait a bit to ensure DOM is ready after navigation
	    setTimeout(() => {
	      // Capture current states from timeline and apply to shorts
	      const timelineLikeButtons = document.querySelectorAll('[onclick*="like_post"]:not(.shorts-slide [onclick*="like_post"])');
	      const timelineStates = {};
	      const timelineCounts = {};

	      // Capture current timeline states
	      timelineLikeButtons.forEach(button => {
	        const postId = extractPostIdFromButton(button);
	        if (postId) {
	          const isLiked = button.classList.contains('liked') || button.classList.contains('active');
	          // Be more specific about like count selector - only look within the like button
	          const countElement = button.querySelector('[data-an="likes-count"]');
	          const count = countElement ? parseInt(countElement.textContent) || 0 : 0;

	          timelineStates[postId] = isLiked;
	          timelineCounts[postId] = count;
	          console.log(`Captured timeline state for post ${postId}: liked=${isLiked}, count=${count}`);
	        }
	      });

	      // Apply timeline states to shorts slides
	      const slides = document.querySelectorAll('.shorts-slide');
	      slides.forEach(slide => {
	        const likeButton = slide.querySelector('[onclick*="like_post"]');
	        if (likeButton) {
	          const postId = extractPostIdFromButton(likeButton);
	          if (postId && timelineStates.hasOwnProperty(postId)) {
	            const shouldBeLiked = timelineStates[postId];
	            const currentCount = timelineCounts[postId];

	            console.log(`Syncing shorts post ${postId} to timeline state: ${shouldBeLiked}, count: ${currentCount}`);

	            // Update visual state
	            if (shouldBeLiked) {
	              likeButton.classList.add('liked');
	            } else {
	              likeButton.classList.remove('liked', 'active');
	            }

	            // Update count - be specific about like count selector
	            const countElement = likeButton.querySelector('[data-an="likes-count"]');
	            if (countElement && currentCount !== undefined) {
	              countElement.textContent = currentCount;
	              console.log(`Updated shorts count for post ${postId} to ${currentCount}`);
	            }
	          }
	        }
	      });
	    }, 500); // Wait 500ms for DOM to be ready
	  }

	  // Function to sync like states from shorts back to timeline
	  function syncLikeStatesToTimeline() {
	    console.log('Syncing like states from shorts back to timeline');

	    // Capture current states from shorts and apply to timeline
	    const shortsLikeButtons = document.querySelectorAll('.shorts-slide [onclick*="like_post"]');
	    const shortsStates = {};
	    const shortsCounts = {};

	    // Capture current shorts states
	    shortsLikeButtons.forEach(button => {
	      const postId = extractPostIdFromButton(button);
	      if (postId) {
	        const isLiked = button.classList.contains('liked') || button.classList.contains('active');
	        // Be more specific about like count selector - only look within the like button
	        const countElement = button.querySelector('[data-an="likes-count"]');
	        const count = countElement ? parseInt(countElement.textContent) || 0 : 0;

	        shortsStates[postId] = isLiked;
	        shortsCounts[postId] = count;
	        console.log(`Captured shorts state for post ${postId}: liked=${isLiked}, count=${count}`);
	      }
	    });

	    // Apply shorts states to timeline buttons with a small delay to ensure DOM is ready
	    setTimeout(() => {
	      const timelineLikeButtons = document.querySelectorAll('[onclick*="like_post"]:not(.shorts-slide [onclick*="like_post"])');
	      timelineLikeButtons.forEach(button => {
	        const postId = extractPostIdFromButton(button);
	        if (postId && shortsStates.hasOwnProperty(postId)) {
	          const shouldBeLiked = shortsStates[postId];
	          const currentCount = shortsCounts[postId];

	          console.log(`Syncing timeline post ${postId} to shorts state: ${shouldBeLiked}, count: ${currentCount}`);

	          // Update visual state
	          if (shouldBeLiked) {
	            button.classList.add('liked');
	          } else {
	            button.classList.remove('liked', 'active');
	          }

	          // Update count - be specific about like count selector
	          const countElement = button.querySelector('[data-an="likes-count"]');
	          if (countElement && currentCount !== undefined) {
	            countElement.textContent = currentCount;
	            console.log(`Updated timeline count for post ${postId} to ${currentCount}`);
	          }
	        }
	      });
	    }, 100); // Small delay to ensure timeline DOM is ready
	  }

	  // Helper functions for like state detection and application
	  function detectLikeState(button, svg) {
	    // Check multiple indicators of like state
	    const hasLikedClass = button.classList.contains('liked') || button.classList.contains('active');
	    const hasLikedColor = button.style.color === '#ff3355' || button.style.color === 'rgb(255, 51, 85)';
	    const svgHasLikedColor = svg && (svg.style.fill === '#ff3355' || svg.style.fill === 'rgb(255, 51, 85)');

	    return hasLikedClass || hasLikedColor || svgHasLikedColor;
	  }

	  function applyLikeState(button, svg) {
	    // Apply liked state
	    button.classList.add('liked');
	    button.style.color = '#ff3355';
	    button.style.setProperty('color', '#ff3355', 'important');

	    if (svg) {
	      svg.style.fill = '#ff3355';
	      svg.style.setProperty('fill', '#ff3355', 'important');
	    }
	  }

	  function applyUnlikeState(button, svg) {
	    // Apply unliked state
	    button.classList.remove('liked', 'active');
	    button.style.color = '';
	    button.style.removeProperty('color');

	    if (svg) {
	      svg.style.fill = '';
	      svg.style.removeProperty('fill');
	    }
	  }

	  function add3DLikeEffects(button, svg, isLiking) {
	    // Add beautiful 3D effects for like/unlike
	    if (isLiking) {
	      // Liking animation
	      button.style.transform = 'scale(1.2)';
	      button.style.transition = 'all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55)';

	      setTimeout(() => {
	        button.style.transform = 'scale(1)';
	      }, 300);

	      // Add pulse effect
	      button.classList.add('like-button-pulse');
	      setTimeout(() => {
	        button.classList.remove('like-button-pulse');
	      }, 600);
	    } else {
	      // Unliking animation
	      button.style.transform = 'scale(0.9)';
	      button.style.transition = 'all 0.2s ease-out';

	      setTimeout(() => {
	        button.style.transform = 'scale(1)';
	      }, 200);
	    }
	  }

	  // Function to sync bookmark and pin states from timeline to shorts viewer
	  function syncStatesOnShortsOpen() {
	    console.log('Syncing bookmark and pin states from timeline to shorts viewer');

	    // Initialize global states if not exists
	    window.bookmarkStates = window.bookmarkStates || {};
	    window.pinStates = window.pinStates || {};

	    // Wait a bit to ensure DOM is ready after navigation
	    setTimeout(() => {
	      // Update bookmark and pin states from current page content
	      if (typeof window.initializeStates === 'function') {
	        window.initializeStates();
	      }

	      // Apply bookmark states to shorts viewer
	      const shortsBookmarkButtons = document.querySelectorAll('.shorts-slide a[onclick*="bookmark_post"]');
	      shortsBookmarkButtons.forEach(button => {
	        const onclickAttr = button.getAttribute('onclick');
	        const postIdMatch = onclickAttr.match(/bookmark_post\('(\d+)'/);

	        if (postIdMatch) {
	          const postId = postIdMatch[1];
	          const textElement = button.querySelector('[data-itag="text"]');

	          if (textElement && window.bookmarkStates[postId] !== undefined) {
	            const isBookmarked = window.bookmarkStates[postId];
	            textElement.textContent = isBookmarked ? 'Unbookmark' : 'Bookmark';
	            console.log(`Updated shorts bookmark for post ${postId}: ${isBookmarked ? 'bookmarked' : 'not bookmarked'}`);
	          }
	        }
	      });

	      // Apply pin states to shorts viewer
	      const shortsPinButtons = document.querySelectorAll('.shorts-slide a[onclick*="pin_admin_post"]');
	      shortsPinButtons.forEach(button => {
	        const onclickAttr = button.getAttribute('onclick');
	        const postIdMatch = onclickAttr.match(/pin_admin_post\((\d+)/);

	        if (postIdMatch) {
	          const postId = postIdMatch[1];
	          const textElement = button.querySelector('[data-itag="text"]');

	          if (textElement && window.pinStates[postId] !== undefined) {
	            const isPinned = window.pinStates[postId];
	            textElement.textContent = isPinned ? 'Unpin post from feeds' : 'Pin post to feeds';
	            console.log(`Updated shorts pin for post ${postId}: ${isPinned ? 'pinned' : 'not pinned'}`);
	          }
	        }
	      });
	    }, 500); // Wait 500ms for DOM to be ready
	  }

	  // Make loadAndPlay globally available for unified video manager
	  window.loadAndPlay = function loadAndPlay(v) {
	    if (!v || v.tagName !== 'VIDEO') return;

	    // Ensure only this video plays using unified manager
	    if (window.__unifiedVideoManager) {
	      window.__unifiedVideoManager.pauseAllExcept(v);
	      window.__unifiedVideoManager.currentVideo = v;
	    }

	    // Check if video is already playing - if so, don't show spinner
	    if (!v.paused) {
	      console.log('Video already playing, no spinner needed');
	      const spinner = v.parentElement.querySelector('.loading-spinner');
	      if (spinner) spinner.style.display = 'none';
	      return;
	    }

	    // Initialize HLS for this video using the global system
	    if (!v.dataset.loaded && v.classList.contains('lazy-hls')) {
	      // Use the global HLS initialization for data-efficient streaming
	      if (window.initializeHLSVideos) {
	        window.initializeHLSVideos(`#${v.id}`);
	        console.log('Initialized HLS for video:', v.id);
	      }
	      v.dataset.loaded = 1;

	      // For HLS videos, we need to trigger the HLS loading manually
	      const hlsUrl = v.dataset.hls;
	      if (hlsUrl && v._hlsInstance) {
	        console.log('HLS instance found, loading source:', hlsUrl);
	        v._hlsInstance.loadSource(hlsUrl);
	        v._hlsInstance.attachMedia(v);
	      } else if (hlsUrl && v.canPlayType("application/vnd.apple.mpegurl")) {
	        console.log('Native HLS support, setting source:', hlsUrl);
	        v.src = hlsUrl;
	      }
	    }

	    // Professional spinner management (TikTok-style)
	    const spinner = v.parentElement.querySelector('.loading-spinner');
	    let spinnerTimeout;
	    let isSpinnerVisible = false;

	    const showSpinner = () => {
	      if (spinner && !isSpinnerVisible) {
	        spinner.style.display = 'block';
	        isSpinnerVisible = true;
	        console.log('Showing spinner - video taking time to load');
	      }
	    };

	    const hideSpinner = () => {
	      if (spinner && isSpinnerVisible) {
	        spinner.style.display = 'none';
	        isSpinnerVisible = false;
	        console.log('Hiding spinner - video ready');
	      }
	      clearTimeout(spinnerTimeout);
	    };

	    // Check if video is already ready to play - instant response
	    if (v.readyState >= 2) { // Lowered threshold for faster response
	      console.log('Video already ready, no spinner needed');
	      // Video is ready, don't show spinner at all
	    } else {
	      // Only show spinner if video takes longer than 150ms to be ready (faster response)
	      spinnerTimeout = setTimeout(() => {
	        // Check if video is still not ready to play
	        if (v.readyState < 2 && v.paused) { // Lower threshold for faster hiding
	          showSpinner();
	        }
	      }, 150); // Reduced delay for faster UX
	    }

	    const onReady = () => {
	      hideSpinner();
	      // Clean up all event listeners
	      v.removeEventListener('canplay', onReady);
	      v.removeEventListener('loadeddata', onReady);
	      v.removeEventListener('playing', onReady);
	      v.removeEventListener('timeupdate', onFirstTimeUpdate);
	    };

	    // First timeupdate event (ensures video is actually playing)
	    const onFirstTimeUpdate = () => {
	      hideSpinner();
	      v.removeEventListener('timeupdate', onFirstTimeUpdate);
	      v.removeEventListener('canplay', onReady);
	      v.removeEventListener('loadeddata', onReady);
	      v.removeEventListener('playing', onReady);
	    };

	    // Listen for multiple ready events for maximum reliability
	    v.addEventListener('canplay', onReady);
	    v.addEventListener('loadeddata', onReady);
	    v.addEventListener('playing', onReady);
	    v.addEventListener('timeupdate', onFirstTimeUpdate);

	    // Emergency fallback: Hide spinner after 1.5 seconds for faster UX
	    const emergencyTimeout = setTimeout(() => {
	      hideSpinner();
	      console.log('Hiding spinner - emergency timeout (faster)');
	    }, 1500); // Reduced from 3s to 1.5s for better UX

	    // Enhanced play with retry logic and better debugging
	    const attemptPlay = async () => {
	      console.log('Attempting to play video:', v.id, 'readyState:', v.readyState, 'src:', v.src, 'data-hls:', v.dataset.hls);

	      try {
	        // Check if video is immediately ready - lowered threshold for faster playback
	        if (v.readyState >= 2) { // HAVE_CURRENT_DATA - enough to start playing
	          await v.play();
	          console.log('Video playing immediately - instant start');
	          hideSpinner();
	          clearTimeout(emergencyTimeout);
	        } else if (v.readyState >= 1) { // HAVE_METADATA - try to play anyway
	          await v.play();
	          console.log('Video playing with minimal data - aggressive start');
	          hideSpinner();
	          clearTimeout(emergencyTimeout);
	        } else {
	          // Video not ready, might need spinner
	          console.log('Video not ready, waiting for data... readyState:', v.readyState);
	          console.log('Using pure MP4 playback - no HLS needed');

	          v.addEventListener('loadeddata', async () => {
	            try {
	              console.log('Video loadeddata event fired, attempting play...');
	              await v.play();
	              console.log('Video playing after loadeddata');
	              hideSpinner();
	              clearTimeout(emergencyTimeout);
	            } catch (e) {
	              console.log('Play failed after loadeddata:', e);
	              // Show spinner if play fails and we're still trying
	              if (v.paused) showSpinner();
	            }
	          }, { once: true });

	          // Also listen for canplay event
	          v.addEventListener('canplay', async () => {
	            try {
	              console.log('Video canplay event fired, attempting play...');
	              await v.play();
	              console.log('Video playing after canplay');
	              hideSpinner();
	              clearTimeout(emergencyTimeout);
	            } catch (e) {
	              console.log('Play failed after canplay:', e);
	            }
	          }, { once: true });

	          // Add buffering state management with debouncing to prevent rapid state changes
	          let bufferingTimeout = null;
	          let playingTimeout = null;

	          v.addEventListener('waiting', () => {
	            // Clear any pending playing state change
	            if (playingTimeout) {
	              clearTimeout(playingTimeout);
	              playingTimeout = null;
	            }

	            // Debounce buffering state to prevent rapid changes
	            if (bufferingTimeout) clearTimeout(bufferingTimeout);
	            bufferingTimeout = setTimeout(() => {
	              console.log('Video buffering, maintaining black background');
	              v.classList.add('buffering');
	              v.style.background = '#000';
	            }, 100); // 100ms debounce
	          });

	          v.addEventListener('playing', () => {
	            // Clear any pending buffering state change
	            if (bufferingTimeout) {
	              clearTimeout(bufferingTimeout);
	              bufferingTimeout = null;
	            }

	            // Debounce playing state to prevent rapid changes
	            if (playingTimeout) clearTimeout(playingTimeout);
	            playingTimeout = setTimeout(() => {
	              console.log('Video playing, removing buffering state');
	              v.classList.remove('buffering');
	              v.style.background = '#000';
	            }, 50); // Shorter debounce for playing state
	          });

	          v.addEventListener('stalled', () => {
	            // Clear any pending playing state change
	            if (playingTimeout) {
	              clearTimeout(playingTimeout);
	              playingTimeout = null;
	            }

	            // Debounce stalled state
	            if (bufferingTimeout) clearTimeout(bufferingTimeout);
	            bufferingTimeout = setTimeout(() => {
	              console.log('Video stalled, maintaining black background');
	              v.classList.add('buffering');
	              v.style.background = '#000';
	            }, 100); // 100ms debounce
	          });
	        }
	      } catch (error) {
	        console.log('Play attempt failed, retrying...', error);
	        // Show spinner since we're having loading issues
	        showSpinner();

	        // Retry after a short delay
	        setTimeout(async () => {
	          try {
	            console.log('Retrying play for video:', v.id);
	            await v.play();
	            console.log('Retry play succeeded');
	            hideSpinner();
	            clearTimeout(emergencyTimeout);
	          } catch (e) {
	            console.log('Retry play failed:', e);
	            // Keep spinner visible since video is having issues
	          }
	        }, 500); // Increased retry delay
	      }
	    };

	    attemptPlay();

	    // Preload next slide video for smoother experience
	    const nextSlide = v.parentElement.nextElementSibling;
	    if(nextSlide){
	      const nextVid = nextSlide.querySelector('video');
	      if(nextVid && !nextVid.dataset.prefetched){
	        const ns = nextVid.dataset.src || nextVid.getAttribute('data-src');
	        if(ns && !nextVid.src){ nextVid.src = ns; nextVid.preload = 'auto'; }
	        nextVid.dataset.prefetched = '1';
	      }
	    }
	  }

	  function hideAllSpinners() {
	    // Hide all spinners in the shorts viewer
	    const spinners = overlay.querySelectorAll('.loading-spinner');
	    spinners.forEach(spinner => {
	      spinner.style.display = 'none';
	    });
	    console.log('Hidden all spinners');
	  }

	  // Force hide spinners for playing videos
	  function forceHideSpinnersForPlayingVideos() {
	    const slides = overlay.querySelectorAll('.shorts-slide');
	    slides.forEach(slide => {
	      const video = slide.querySelector('video');
	      const spinner = slide.querySelector('.loading-spinner');

	      if (video && spinner && !video.paused) {
	        spinner.style.display = 'none';
	        console.log('Force hidden spinner for playing video');
	      }
	    });
	  }

	  function closeOverlay(){
	    console.log('=== CLOSING SHORTS OVERLAY ===');

	    // Clear shorts active flag to re-enable unauthorized play detection
	    window.__shortsActive = false;
	    console.log('🎬 Cleared shorts active flag - re-enabled unauthorized play detection');

	    // CLEAN SHUTDOWN: Properly pause all shorts videos and clean up state
	    try {
	      overlay.querySelectorAll('video').forEach(video => {
	        // Pause video immediately to prevent background playback
	        if (!video.paused) {
	          video.pause();
	          console.log('⏸️ Paused shorts video on close:', video.id);
	        }

	        // Save state using reliable manager
	        if (window.shortsAutoplayManager) {
	          window.shortsAutoplayManager.saveVideoState(video);
	        }
	      });

	      // Clean up shorts video manager state
	      if (window.shortsAutoplayManager) {
	        window.shortsAutoplayManager.currentVideo = null;
	        window.shortsAutoplayManager.cleanupOldStates();
	        console.log('🧹 Cleaned up shorts autoplay manager');
	      }

	    } catch (e) {
	      console.log('❌ Error saving video states:', e);
	    }

	    // Sync like states back to timeline before closing
	    if (typeof syncLikeStatesToTimeline === 'function') {
	      syncLikeStatesToTimeline();
	    }

	    // Hide all spinners before closing
	    if (typeof hideAllSpinners === 'function') {
	      hideAllSpinners();
	    }

	    // Proper cleanup before closing
	    overlay.querySelectorAll('video').forEach(v => {
	      v.pause();
	      v.src = '';
	      v.load(); // Reset video element

	      // Unregister from unified manager
	      if (window.__unifiedVideoManager && window.__unifiedVideoManager.allVideos.has(v)) {
	        window.__unifiedVideoManager.allVideos.delete(v);
	      }
	    });

	    // Reset unified video manager state
	    if (window.__unifiedVideoManager) {
	      window.__unifiedVideoManager.currentVideo = null;
	      window.__unifiedVideoManager.isScrolling = false;
	    }

	    // Reset any transforms/styles from swipe gestures before closing
    overlay.style.transform = '';
    overlay.style.opacity = '';
    overlay.style.transition = '';

    // Close overlay
	    overlay.style.display='none';
	    document.body.style.overflow='';
	    overlay.innerHTML = ''; // Complete cleanup

	    // Simple restoration of clicked video state
	    setTimeout(() => {
	      try {
	        if (window.clickedVideoState) {
	          const originalVideo = document.getElementById(window.clickedVideoState.id);
	          if (originalVideo) {
	            console.log('Restoring original video:', window.clickedVideoState.id);

	            // Check if we have a more recent state from shorts
	            const shortsState = window.shortsVideoStates && window.shortsVideoStates[window.clickedVideoState.id];
	            const timeToRestore = shortsState ? shortsState.currentTime : window.clickedVideoState.currentTime;

	            originalVideo.currentTime = timeToRestore;
	            console.log('Set timeline video time to:', timeToRestore);

	            // If it was playing and is visible, resume
	            if (!window.clickedVideoState.paused && window.__unifiedVideoManager) {
	              if (window.__unifiedVideoManager.isVideoVisibleLenient &&
	                  window.__unifiedVideoManager.isVideoVisibleLenient(originalVideo)) {
	                originalVideo.play().then(() => {
	                  console.log('Resumed timeline video from:', originalVideo.currentTime);
	                }).catch(() => {});
	              }
	            }
	          }
	          // Clear the states
	          window.clickedVideoState = null;
	          window.shortsVideoStates = {};
	        }

	        // Re-register timeline videos with unified manager for proper control
	        setTimeout(() => {
	          const timelineVideos = document.querySelectorAll(FEED_SEL);
	          timelineVideos.forEach(video => {
	            if (window.__unifiedVideoManager && !window.__unifiedVideoManager.allVideos.has(video)) {
	              window.__unifiedVideoManager.registerVideo(video, 'timeline');
	              console.log('Re-registered timeline video:', video.id);
	            }
	          });

	          // Trigger a visibility check to start appropriate video
	          if (window.__unifiedVideoManager) {
	            const result = window.__unifiedVideoManager.getMostVisibleVideo();
	            if (result.video && result.visibility >= 0.5) {
	              window.__unifiedVideoManager.playVideo(result.video, 'timeline', result.visibility);
	              console.log('Started most visible timeline video after shorts close');
	            }
	          }
	        }, 500);

	      } catch (e) {
	        console.log('Error restoring video state:', e);
	      }
	    }, 300);
	  }

	  // Simple event listeners for closing
	  overlay.addEventListener('click', function(e) {
	    if (e.target === overlay || e.target.id === 'shortsCloseBtn') {
	      closeOverlay();
	    }
	  });

	  document.addEventListener('keydown', function(e) {
	    if (e.key === 'Escape' && overlay.style.display !== 'none') {
	      closeOverlay();
	    }
	  });

	  // Volume toast setup & patch toggleGlobalMute once
	  (function(){
	    if(document.getElementById('volumeToast')) return;
	    const vt=document.createElement('div'); vt.id='volumeToast'; document.body.appendChild(vt);

	    function showVolumeToast(unmuted){
	      vt.textContent = unmuted ? '🔊' : '🔇';
	      vt.style.opacity = 1;
	      setTimeout(()=>{ vt.style.opacity = 0; }, 700);
	    }

	    if(window.toggleGlobalMute && !window.__patchedGlobalMute){
	      const orig = window.toggleGlobalMute;
	      window.toggleGlobalMute = function(state){
	        orig(state);
	        showVolumeToast(!state);
	      };
	      window.__patchedGlobalMute = true;
	    }
	  })();

	  // Initialize global like enhancement for the entire site
	  initGlobalLikeEnhancement();
	})();
	</script>

	<!-- Video Timing Controls -->
	<link rel="stylesheet" href="{%config theme_url%}/statics/custom_code/video-timing-controls.css?v=<?php echo($cl["update_date"]); ?>">
	<script src="{%config theme_url%}/statics/custom_code/video-timing-controls.js?v=<?php echo($cl["update_date"]); ?>"></script>
</body>
</html>


