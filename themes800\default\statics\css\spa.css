/* SPA Navigation Styles */
.vlix-page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    z-index: 9999;
    background: linear-gradient(to right, #2329D6, #6C63FF);
    animation: loadingBar 1s infinite linear;
}

@keyframes loadingBar {
    0% {
        width: 0;
    }
    20% {
        width: 20%;
    }
    50% {
        width: 50%;
    }
    80% {
        width: 80%;
    }
    100% {
        width: 100%;
    }
}

.loader-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.loader-spinner > div {
    width: 12px;
    height: 12px;
    background-color: #2329D6;
    border-radius: 100%;
    display: inline-block;
    animation: bounce 1.4s infinite ease-in-out both;
}

.loader-spinner .bounce1 {
    animation-delay: -0.32s;
}

.loader-spinner .bounce2 {
    animation-delay: -0.16s;
}

@keyframes bounce {
    0%, 80%, 100% { 
        transform: scale(0);
    } 
    40% { 
        transform: scale(1.0);
    }
}

/* Smooth page transitions */
main {
    opacity: 1;
    transition: opacity 0.2s ease-in-out;
}

main.loading {
    opacity: 0.6;
}

/* Instagram-like UI Enhancements */
.vlix-post {
    border-radius: 8px;
    background: #fff;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    margin-bottom: 16px;
    transition: transform 0.2s ease;
}

.vlix-post:hover {
    transform: translateY(-2px);
}

.vlix-post-header {
    padding: 12px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.vlix-post-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.vlix-post-username {
    font-weight: 600;
    color: #262626;
    text-decoration: none;
}

.vlix-post-media {
    position: relative;
    aspect-ratio: 1/1;
    background: #fafafa;
    overflow: hidden;
    width: 100%;
}

.vlix-post-media img,
.vlix-post-media video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    max-width: 100%;
    margin: 0;
    display: block;
}

.vlix-post-actions {
    padding: 12px;
    display: flex;
    align-items: center;
    gap: 16px;
}

.vlix-action-btn {
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.vlix-action-btn:hover {
    transform: scale(1.1);
}

.vlix-post-likes {
    padding: 0 12px;
    font-weight: 600;
    color: #262626;
}

.vlix-post-caption {
    padding: 12px;
    color: #262626;
    line-height: 1.5;
}

/* Stories Bar */
.vlix-stories {
    display: flex;
    gap: 16px;
    padding: 16px;
    overflow-x: auto;
    scrollbar-width: none;
    margin-bottom: 24px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.vlix-stories::-webkit-scrollbar {
    display: none;
}

.vlix-story {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    cursor: pointer;
}

.vlix-story-avatar {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    padding: 2px;
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
}

.vlix-story-avatar img {
    border-radius: 50%;
    border: 3px solid #fff;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.vlix-story-username {
    font-size: 12px;
    color: #262626;
    max-width: 64px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;
} 