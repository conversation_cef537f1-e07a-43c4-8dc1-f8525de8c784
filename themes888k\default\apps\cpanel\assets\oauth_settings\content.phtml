<div class="cp-app-container" data-app="oauth-settings">
    <div class="current-page-name">
        <div class="lp">
            <h1>
                Social login
            </h1>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        Manage social login API settings
                    </h2>
                </div>
                <div class="body">
                    <form class="form" data-an="form">
                        <div class="row">
                            <div class="col-sm-12">
                                <h6>Login with Facebook API settings</h6>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group" data-an="facebook_api_id-input">
                                    <label>
                                        Facebook API ID
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config facebook_api_id%}" name="facebook_api_id" type="text" class="form-control" placeholder="Facebook API ID">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group" data-an="facebook_api_key-input">
                                    <label>
                                        Facebook API Key
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config facebook_api_key%}" name="facebook_api_key" type="text" class="form-control" placeholder="Facebook API Key">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group" data-an="facebook_oauth-input">
                                    <label>
                                        Status
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="facebook_oauth" class="form-control">
                                            <option value="on" <?php if($cl['config']['facebook_oauth'] == 'on') { echo('selected'); } ?>>On</option>
                                            <option value="off" <?php if($cl['config']['facebook_oauth'] == 'off') { echo('selected'); } ?>>Off</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-sm-12">
                                <h6>Login with Twitter API settings</h6>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group" data-an="twitter_api_id-input">
                                    <label>
                                        Twitter API ID
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config twitter_api_id%}" name="twitter_api_id" type="text" class="form-control" placeholder="Twitter API ID">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group" data-an="twitter_api_key-input">
                                    <label>
                                        Twitter API Key
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config twitter_api_key%}" name="twitter_api_key" type="text" class="form-control" placeholder="Twitter API Key">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group" data-an="twitter_oauth-input">
                                    <label>
                                        Status
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="twitter_oauth" class="form-control">
                                            <option value="on" <?php if($cl['config']['twitter_oauth'] == 'on') { echo('selected'); } ?>>On</option>
                                            <option value="off" <?php if($cl['config']['twitter_oauth'] == 'off') { echo('selected'); } ?>>Off</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-sm-12">
                                <h6>Login with Google API settings</h6>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group" data-an="google_api_id-input">
                                    <label>
                                        Google API ID
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config google_api_id%}" name="google_api_id" type="text" class="form-control" placeholder="Google API ID">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group" data-an="google_api_key-input">
                                    <label>
                                        Google API Key
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config google_api_key%}" name="google_api_key" type="text" class="form-control" placeholder="Google API Key">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group" data-an="google_oauth-input">
                                    <label>
                                        Status
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="google_oauth" class="form-control">
                                            <option value="on" <?php if($cl['config']['google_oauth'] == 'on') { echo('selected'); } ?>>On</option>
                                            <option value="off" <?php if($cl['config']['google_oauth'] == 'off') { echo('selected'); } ?>>Off</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-sm-12">
                                <h6>Login with LinkedIn API settings</h6>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group" data-an="linkedin_api_id-input">
                                    <label>
                                        LinkedIn API ID
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config linkedin_api_id%}" name="linkedin_api_id" type="text" class="form-control" placeholder="LinkedIn API ID">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group" data-an="linkedin_api_key-input">
                                    <label>
                                        LinkedIn API Key
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config linkedin_api_key%}" name="linkedin_api_key" type="text" class="form-control" placeholder="LinkedIm API Key">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group" data-an="linkedin_oauth-input">
                                    <label>
                                        Status
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="linkedin_oauth" class="form-control">
                                            <option value="on" <?php if($cl['config']['linkedin_oauth'] == 'on') { echo('selected'); } ?>>On</option>
                                            <option value="off" <?php if($cl['config']['linkedin_oauth'] == 'off') { echo('selected'); } ?>>Off</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-sm-12">
                                <h6>Login with Discord API settings</h6>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group" data-an="discord_api_id-input">
                                    <label>
                                        Discord API ID
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config discord_api_id%}" name="discord_api_id" type="text" class="form-control" placeholder="Discord API ID">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group" data-an="discord_api_key-input">
                                    <label>
                                        Discord API Key
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config discord_api_key%}" name="discord_api_key" type="text" class="form-control" placeholder="Discord API Key">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group" data-an="discord_status-input">
                                    <label>
                                        Status
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="discord_status" class="form-control">
                                            <option value="on" <?php if($cl['config']['discord_status'] == 'on') { echo('selected'); } ?>>On</option>
                                            <option value="off" <?php if($cl['config']['discord_status'] == 'off') { echo('selected'); } ?>>Off</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-sm-12">
                                <h6>Login with Vkontakte API settings</h6>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group" data-an="vkontakte_api_id-input">
                                    <label>
                                        Vkontakte API ID
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config vkontakte_api_id%}" name="vkontakte_api_id" type="text" class="form-control" placeholder="Vkontakte API ID">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group" data-an="vkontakte_api_key-input">
                                    <label>
                                        Vkontakte API Key
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config vkontakte_api_key%}" name="vkontakte_api_key" type="text" class="form-control" placeholder="Vkontakte API Key">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group" data-an="vkontakte_status-input">
                                    <label>
                                        Status
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="vkontakte_status" class="form-control">
                                            <option value="on" <?php if($cl['config']['vkontakte_status'] == 'on') { echo('selected'); } ?>>On</option>
                                            <option value="off" <?php if($cl['config']['vkontakte_status'] == 'off') { echo('selected'); } ?>>Off</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-sm-12">
                                <h6>Login with Instagram API settings</h6>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group" data-an="instagram_api_id-input">
                                    <label>
                                        Instagram API ID
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config instagram_api_id%}" name="instagram_api_id" type="text" class="form-control" placeholder="Instagram API ID">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group" data-an="instagram_api_key-input">
                                    <label>
                                        Instagram API Key
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config instagram_api_key%}" name="instagram_api_key" type="text" class="form-control" placeholder="Instagram API Key">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group" data-an="instagram_status-input">
                                    <label>
                                        Status
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="instagram_status" class="form-control">
                                            <option value="on" <?php if($cl['config']['instagram_status'] == 'on') { echo('selected'); } ?>>On</option>
                                            <option value="off" <?php if($cl['config']['instagram_status'] == 'off') { echo('selected'); } ?>>Off</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12 no-mb">
                                <button data-an="submit-ctrl" type="submit" class="btn btn-primary">
                                    Save changes
                                </button>
                            </div>
                        </div>
                        <input type="hidden" class="d-none" value="<?php echo fetch_or_get($cl['csrf_token'],'none'); ?>" name="hash">
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo cl_template('cpanel/assets/oauth_settings/scripts/app_master_script'); ?>