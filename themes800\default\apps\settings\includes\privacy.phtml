<div class="timeline-container">
	<div class="timeline-header" data-el="tl-header">
		<div class="timeline-header__botline">
			<div class="lp">
				<div class="nav-link-holder">
					<a href="<?php echo cl_link("settings/privacy"); ?>" data-spa="true">
						<?php echo cl_translate("Account privacy"); ?>
					</a>
				</div>
			</div>
			<div class="cp">
				<a href="<?php echo cl_link("home"); ?>">
					<img src="{%config site_logo%}" alt="Logo">
				</a>
			</div>
			<div class="rp">
				<div class="nav-link-holder">
					<span class="go-back" onclick="SMColibri.go_back();">
						<?php echo cl_ficon('arrow_back'); ?>
					</span>
				</div>
			</div>
		</div>
	</div>
	<div class="profile-settings">
		<div class="profile-settings__content">
			<div class="settings-form">
				<form class="form" id="cl-privacy-settings-vue-app" v-on:submit="submit_form($event)">
					<div class="form-group">
	                    <label>
	                        <?php echo cl_translate("Who can see my profile & posts?"); ?>   
	                    </label>
	                    <div class="dropdown vue-dropdown-select">
	                        <button class="btn btn-secondary dropdown-toggle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
	                            {{sdds.profile_privacy[profile_privacy]}}
	                        </button>
	                        <div class="dropdown-menu">
	                            <a v-for="(v,k) in sdds.profile_privacy" v-on:click="profile_privacy = k" class="dropdown-item" href="javascript:void(0);">
	                                {{v}}
	                            </a>
	                        </div>
	                    </div>
	                </div>
	                <div class="form-group">
	                    <label>
	                        <?php echo cl_translate("Who can follow me?"); ?>   
	                    </label>
	                    <div class="dropdown vue-dropdown-select">
	                        <button class="btn btn-secondary dropdown-toggle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
	                            {{sdds.follow_privacy[follow_privacy]}}
	                        </button>
	                        <div class="dropdown-menu">
	                            <a v-for="(v,k) in sdds.follow_privacy" v-on:click="follow_privacy = k" class="dropdown-item" href="javascript:void(0);">
	                                {{v}}
	                            </a>
	                        </div>
	                    </div>
	                </div>
	                <div class="form-group">
	                    <label>
	                        <?php echo cl_translate("Who can direct message me?"); ?>   
	                    </label>
	                    <div class="dropdown vue-dropdown-select">
	                        <button class="btn btn-secondary dropdown-toggle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
	                            {{sdds.contact_privacy[contact_privacy]}}
	                        </button>
	                        <div class="dropdown-menu">
	                            <a v-for="(v,k) in sdds.contact_privacy" v-on:click="contact_privacy = k" class="dropdown-item" href="javascript:void(0);">
	                                {{v}}
	                            </a>
	                        </div>
	                    </div>
	                </div>
	                <div class="form-group">
	                    <label>
	                        <?php echo cl_translate("Show your profile in search engines?"); ?>   
	                    </label>
	                    <div class="dropdown vue-dropdown-select">
	                        <button class="btn btn-secondary dropdown-toggle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
	                            {{sdds.index_privacy[index_privacy]}}
	                        </button>
	                        <div class="dropdown-menu">
	                            <a v-for="(v,k) in sdds.index_privacy" v-on:click="index_privacy = k" class="dropdown-item" href="javascript:void(0);">
	                                {{v}}
	                            </a>
	                        </div>
	                    </div>
	                </div>
	                <div class="form-group">
	                    <label>
	                        <?php echo cl_translate("Who can see when I'm online?"); ?>   
	                    </label>
	                    <div class="dropdown vue-dropdown-select">
	                        <button class="btn btn-secondary dropdown-toggle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
	                            {{sdds.online_ind[online_ind]}}
	                        </button>
	                        <div class="dropdown-menu">
	                            <a v-for="(v,k) in sdds.online_ind" v-on:click="online_ind = k" class="dropdown-item" href="javascript:void(0);">
	                                {{v}}
	                            </a>
	                        </div>
	                    </div>
	                </div>
	                <div class="form-group" v-if="unsuccessful_attempt">
	                    <div class="invalid-main-feedback">
	                        <?php echo cl_translate("Something went wrong while trying to save your changes, please try again later"); ?>
	                    </div>
	                </div>
	                <input type="hidden" class="d-none" value="<?php echo fetch_or_get($cl['csrf_token'],'none'); ?>" name="hash">
	                <div class="form-group no-mb d-flex">
	                	<button v-if="submitting != true" v-bind:disabled="$v.$invalid == true" type="submit" class="ml-auto btn btn-custom main-inline lg">
	                        <?php echo cl_translate("Save changes"); ?>
	                    </button>
	                    <button v-else disabled="true" type="button" class="ml-auto btn btn-custom main-inline lg">
	                        <?php echo cl_translate("Please wait"); ?>
	                    </button>
	                </div>
	            </form>
			</div>
		</div>
	</div>

	<script>
		"use strict";

		$(document).ready(function($) {
			Vue.use(window.vuelidate.default);
			var VueValids = window.validators;

			new Vue({
				el: "#cl-privacy-settings-vue-app",
				data: {
					submitting: false,
					unsuccessful_attempt: false,
					profile_privacy: "<?php echo($me['profile_privacy']); ?>",
					follow_privacy: "<?php echo($me['follow_privacy']); ?>",
					contact_privacy: "<?php echo($me['contact_privacy']); ?>",
					index_privacy: "<?php echo($me['index_privacy']); ?>",
					online_ind: "<?php echo($me['online_ind']); ?>",
					sdds: {
						profile_privacy: {
							everyone: "<?php echo cl_translate("Everyone"); ?>",
							followers: "<?php echo cl_translate("My followers"); ?>"
						},
						follow_privacy: {
							everyone: "<?php echo cl_translate("Everyone"); ?>",
							approved: "<?php echo cl_translate("Approved users only"); ?>"
						},
						contact_privacy: {
							everyone: "<?php echo cl_translate("Everyone"); ?>",
							followed: "<?php echo cl_translate("The people I follow"); ?>"
						},
						index_privacy: {
							Y: "<?php echo cl_translate("Yes"); ?>",
							N: "<?php echo cl_translate("No"); ?>"
						},
						online_ind: {
							on: "<?php echo cl_translate("Everyone"); ?>",
							off: "<?php echo cl_translate("Nobody"); ?>"
						}
					}
				},
				validations: {
					profile_privacy: {
						profile_privacy: VueValids.required
					},
					follow_privacy: {
						profile_privacy: VueValids.required
					},
					contact_privacy: {
						contact_privacy: VueValids.required
					},
					index_privacy: {
						index_privacy: VueValids.required
					},
					online_ind: {
						online_ind: VueValids.required
					}
				},
				methods: {
					submit_form: function(_self = null) {
						_self.preventDefault();
						var _app_   = this;
						var payload = _app_.form_data();

						$(_self.target).ajaxSubmit({
							url: "<?php echo cl_link("native_api/settings/save_privacy_settings"); ?>",
							type: 'POST',
							dataType: 'json',
							data: payload,
							beforeSend: function() {
								_app_.submitting = true;
								_app_.unsuccessful_attempt = false;
							},
							success: function(data) {
								if (data.status == 200) {
									cl_bs_notify("<?php echo cl_translate("Your changes has been successfully saved!"); ?>", 5000, "success");
								}

								else {
									_app_.unsuccessful_attempt = true;
								}
							},
							complete: function() {
								_app_.submitting = false;
							}
						});
					},
					form_data: function() {
	                    var _app_ = this;

	                   	return Object({
	                   		profile_privacy: _app_.profile_privacy,
	                   		follow_privacy: _app_.follow_privacy,
	                   		contact_privacy: _app_.contact_privacy,
	                   		online_ind: _app_.online_ind,
	                   		index_privacy: _app_.index_privacy
	                   	});
	                }
				}
			});
		});
	</script>
</div>