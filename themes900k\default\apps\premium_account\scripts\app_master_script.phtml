<script>

	"use strict";

	$(document).ready(function($) {
		SMColibri.PS.premium_account = Object({
			buy: function() {
				var promise = SMColibri.confirm_action({
					btn_1: "<?php echo cl_translate("Cancel"); ?>",
					btn_2: "<?php echo cl_translate("Yes, Upgrade to premium"); ?>",
					title: "<?php echo cl_translate("Please confirm your actions!"); ?>",
					message: "<?php echo cl_translate("Please note that after confirmation, funds in the amount of {@price@} will be debited from your wallet. The subscription is valid for 30 days from the date of purchase, that is, until {@end_date@}", array("price" => cl_html_el("strong", cl_money($cl["config"]["premium_account_mprice"])), "end_date" => date("d/m/Y", (time() + (86400 * 31))))); ?>",
				});

				promise.done(function() {
					$.ajax({
						url: '<?php echo cl_link("native_api/settings/upgrade_to_premium"); ?>',
						type: 'POST',
						dataType: 'json',
						beforeSend: function() {
							<?php if ($cl["config"]["user_wallet_status"] == "off"): ?>
								cl_bs_notify("<?php echo cl_translate("Sorry, but the user's wallet is temporarily disabled, and therefore you cannot complete this payment for the time being. Contact support if you have questions"); ?>", 5000, "warning");

								return false;
							<?php endif; ?>
						}
					}).done(function(data) {
						if (data.status == 200) {
							cl_bs_notify("<?php echo cl_translate("Congratulations, your account has been successfully upgraded to premium user account"); ?>", 5000, "success");

							setTimeout(function() {
								SMColibri.spa_load("premium_features");
							}, 2500);
						}
						else {
							cl_bs_notify("Your wallet does not have enough funds to pay for a premium subscription. Please replenish your wallet with at least the amount of the premium account and try again", 5000, "warning");
						}
					}).always(function() {
						$("div.confirm-actions-modal").modal("hide");
					});
		        });

				promise.fail(function() {
		            $("div.confirm-actions-modal").modal("hide");
		        });
			}
		});
	});
</script>