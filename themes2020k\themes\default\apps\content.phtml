<div class="timeline-container" data-app="maintenance">
	<div class="timeline-header" data-el="tl-header">
		<div class="timeline-header__botline">
			<div class="lp">
				<div class="nav-link-holder">
					<a href="<?php echo cl_link("maintenance"); ?>" data-spa="true">
						<?php echo cl_translate('Site Maintenance'); ?>
					</a>
				</div>
			</div>
			<div class="cp">
				<a href="<?php echo cl_link('/'); ?>">
					<img src="{%config site_logo%}" alt="Logo">
				</a>
			</div>
		</div>
	</div>
	<div class="timeline-placeholder">
		<div class="icon">
			<div class="icon__bg">
				<?php echo cl_ficon('settings'); ?>
			</div>
		</div>
		<div class="pl-message">
			<h4>
				<?php echo cl_translate('Site Under Maintenance'); ?>
			</h4>
			<p>
				<?php echo cl_translate("We're currently performing scheduled maintenance to improve your experience. Please check back in a few minutes."); ?>
			</p>
			<?php if (isset($cl['is_admin']) && $cl['is_admin'] === true): ?>
				<div class="admin-notice" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px;">
					<h5 style="color: #28a745; margin-bottom: 10px;">
						<?php echo cl_ficon('shield'); ?> <?php echo cl_translate('Admin Access'); ?>
					</h5>
					<p style="margin-bottom: 15px; color: #6c757d;">
						<?php echo cl_translate('You can access the site because you are an administrator. Regular users will see this maintenance page.'); ?>
					</p>
					<div class="admin-actions">
						<a href="<?php echo cl_link('admin_panel'); ?>" class="btn btn-primary btn-sm" style="margin-right: 10px;">
							<?php echo cl_translate('Admin Panel'); ?>
						</a>
						<a href="<?php echo cl_link('/'); ?>" class="btn btn-success btn-sm">
							<?php echo cl_translate('Continue to Site'); ?>
						</a>
					</div>
				</div>
			<?php endif; ?>
		</div>
	</div>
</div>

<style>
.timeline-placeholder {
	text-align: center;
	padding: 60px 20px;
}

.timeline-placeholder .icon {
	margin-bottom: 30px;
}

.timeline-placeholder .icon__bg {
	width: 80px;
	height: 80px;
	background: #f8f9fa;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto;
	font-size: 32px;
	color: #6c757d;
}

.timeline-placeholder h4 {
	color: #495057;
	margin-bottom: 15px;
	font-size: 24px;
	font-weight: 600;
}

.timeline-placeholder p {
	color: #6c757d;
	font-size: 16px;
	line-height: 1.6;
	max-width: 500px;
	margin: 0 auto;
}

.admin-notice {
	max-width: 600px;
	margin: 0 auto;
	text-align: left;
}

.admin-actions .btn {
	text-decoration: none;
	padding: 8px 16px;
	border-radius: 4px;
	font-size: 14px;
	font-weight: 500;
	display: inline-block;
	transition: all 0.2s ease;
}

.btn-primary {
	background-color: #007bff;
	color: white;
	border: 1px solid #007bff;
}

.btn-primary:hover {
	background-color: #0056b3;
	border-color: #0056b3;
}

.btn-success {
	background-color: #28a745;
	color: white;
	border: 1px solid #28a745;
}

.btn-success:hover {
	background-color: #1e7e34;
	border-color: #1e7e34;
}
</style>
