<div class="cp-app-container" data-app="wasabi-storage">
    <div class="current-page-name">
        <div class="lp">
            <h1>
                Wasabi S3
            </h1>
        </div>
    </div>
    <div class="card">
        <div class="header">
            <h2>
                Wasabi S3 Storage API settings
            </h2>
        </div>
        <div class="body">
            <form class="form" id="wasabi-storage-settings">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group" data-an="ws3_storage-input">
                            <label>
                                Wasabi S3 storage
                            </label>
                            <div class="form-line form-select">
                                <select name="ws3_storage" class="form-control">
                                    <option value="on" <?php if($cl['config']['ws3_storage'] == 'on') { echo('selected'); } ?>>Enabled</option>
                                    <option value="off" <?php if($cl['config']['ws3_storage'] == 'off') { echo('selected'); } ?>>Disabled</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group" data-an="ws3_bucket_name-input">
                            <label>
                                WS3 bucket name
                            </label>
                            <div class="form-line">
                                <input value="{%config ws3_bucket_name%}" name="ws3_bucket_name" type="text" class="form-control" placeholder="Enter WS3 bucket name">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group" data-an="ws3_api_key-input">
                            <label>
                                Wasabi S3 API key
                            </label>
                            <div class="form-line">
                                <input value="{%config ws3_api_key%}" name="ws3_api_key" type="text" class="form-control" placeholder="Enter Wasabi S3 API key">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group" data-an="ws3_api_secret_key-input">
                            <label>
                                Wasabi S3 API secret key
                            </label>
                            <div class="form-line">
                                <input value="{%config ws3_api_secret_key%}" name="ws3_api_secret_key" type="text" class="form-control" placeholder="Enter Wasabi S3 API secret key">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group" data-an="ws3_bucket_region-input">
                            <label>
                                Wasabi S3 bucket region
                            </label>
                            <div class="form-line form-select">
                                <select name="ws3_bucket_region" data-size="5" class="form-control">
                                    <option value="us-west-1" <?php echo ($cl['config']['ws3_bucket_region'] == 'us-west-1')   ? ' selected' : '';?> >us-west-1</option>
                                    <option value="us-east-1" <?php echo ($cl['config']['ws3_bucket_region'] == 'us-east-1')   ? ' selected' : '';?> >us-east-1</option>
                                    <option value="us-east-2" <?php echo ($cl['config']['ws3_bucket_region'] == 'us-east-2')   ? ' selected' : '';?> >us-east-2</option>
                                    <option value="us-central-1" <?php echo ($cl['config']['ws3_bucket_region'] == 'us-central-1')   ? ' selected' : '';?> >us-central-1</option>
                                    <option value="ca-central-1" <?php echo ($cl['config']['ws3_bucket_region'] == 'ca-central-1')   ? ' selected' : '';?> >ca-central-1</option>

                                    <option value="eu-west-1" <?php echo ($cl['config']['ws3_bucket_region'] == 'eu-west-1')   ? ' selected' : '';?> >eu-west-1</option>
                                    <option value="eu-west-2" <?php echo ($cl['config']['ws3_bucket_region'] == 'eu-west-2')   ? ' selected' : '';?> >eu-west-2</option>
                                    <option value="eu-central-1" <?php echo ($cl['config']['ws3_bucket_region'] == 'eu-central-1')   ? ' selected' : '';?> >eu-central-1</option>
                                    <option value="eu-central-2" <?php echo ($cl['config']['ws3_bucket_region'] == 'eu-central-2')   ? ' selected' : '';?> >eu-central-2</option>

                                    <option value="ap-northeast-1" <?php echo ($cl['config']['ws3_bucket_region'] == 'ap-northeast-1')   ? ' selected' : '';?> >ap-northeast-1</option>
                                    <option value="ap-northeast-2" <?php echo ($cl['config']['ws3_bucket_region'] == 'ap-northeast-2')   ? ' selected' : '';?> >ap-northeast-2</option>
                                    <option value="ap-southeast-2" <?php echo ($cl['config']['ws3_bucket_region'] == 'ap-southeast-2')   ? ' selected' : '';?> >ap-southeast-2</option>
                                    <option value="ap-southeast-1" <?php echo ($cl['config']['ws3_bucket_region'] == 'ap-southeast-1')   ? ' selected' : '';?> >ap-southeast-1</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group no-mb">
                    <button data-an="submit-ctrl" type="submit" class="btn btn-primary">
                        Save changes
                    </button>
                </div>
                <input type="hidden" class="d-none" value="<?php echo fetch_or_get($cl['csrf_token'],'none'); ?>" name="hash">
            </form>
        </div>
    </div>
</div>
<?php echo cl_template('cpanel/assets/wasabi_s3/scripts/app_master_script'); ?>