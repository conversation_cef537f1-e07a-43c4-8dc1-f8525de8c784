﻿table.table {
    tbody {
        tr {
            td{
                padding: 15px;
                border-top: none !important;
                border-bottom: 1px solid #eee;
                color: $black;
                font-size: 13px;
                color: $black;
                vertical-align: middle;

                time{
                    color: $grey;
                }

                b.num{
                    font-size: inherit;
                    color: $black;
                    font-weight: 500;
                }

                div.select-box{
                    select{
                        border: none;
                        padding: 5px 10px 5px ;
                        background: transparent;
                        cursor: pointer;
                        color: $black;
                    }
                }

                & > a{
                    color: $black;
                    font-weight: 500;
                    text-decoration: none;
                }

                & > span.icon{
                    vertical-align: middle;
                    line-height: 0;
                    padding: 0;
                    margin: 0;

                    svg{
                        width: 22px;
                        height: 22px;
                        
                        path{
                            fill: $blue;
                        }
                    }

                    &.pointer{
                        cursor: pointer;

                        svg{
                            path{
                                fill: $grey;
                            }
                        }

                        &:hover, &:active{
                            svg{
                                path{
                                    fill: $blue;
                                }
                            }
                        }
                    }

                    &.not-allowed{
                        opacity: 0.5;
                        cursor: not-allowed;

                        svg{
                            path{
                                fill: $grey;
                            }
                        }
                    }
                }

                div.user-info-holder{
                    width: 100%;
                    display: flex;
                    flex-direction: row;
                    flex-wrap: nowrap;
                    overflow: hidden;
                    align-items: center;

                    div.avatar-holder{
                        width: 40px;
                        height: 40px;
                        min-width: 35px;
                        min-height: 35px;
                        border-radius: 10em;
                        border: 1px solid #eee;
                        display: inline-block;
                        margin-right: 10px;
                        overflow: hidden;
                        vertical-align: middle;

                        img{
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                        }
                    }

                    div.uname-holder{
                        vertical-align: middle;
                        display: inline-block;
                        line-height: 0;

                        a,b{
                            width: 100%;
                            display: inline-block;
                            white-space: nowrap;
                            max-width: 150px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }

                        b{
                            font-size: 13px;
                            line-height: 16px;
                        }

                        a{
                            font-size: 13px;
                            line-height: 16px;
                            color: $grey;
                            text-decoration: none;
                        }
                    }
                }

                div.banner-flag{
                    display: inline-block;
                    vertical-align: middle;
                    line-height: 0;
                    width: 30px;
                    height: 15px;

                    svg{
                        width: 100%;
                        height: 100%;
                    }
                }

                div.dropdown{
                    position: relative;

                    a.dropdown-toggle{
                        svg{
                            width: 20px;
                            height: 20px;
                            
                            path{
                                fill: $grey;
                            }
                        }
                    }

                    div.dropdown-menu{
                        position: absolute;
                    }
                }

                div.empty-table{
                    width: 100%;
                    display: flex;
                    flex-direction: column;
                    flex-wrap: nowrap;
                    justify-content: center;
                    align-items: center;
                    height: 360px;
                    padding: 20px 100px;

                    h4{
                        font-size: 22px;
                        color: $black;
                        font-weight: 700;
                        line-height: 28px;
                        padding: 0;
                        margin: 0 0 10px 0;
                    }

                    p{
                        font-size: 14px;
                        color: $grey;
                        line-height: 22px;
                        padding: 0;
                        margin: 0;
                        text-align: center;
                        max-width: 70%;
                    }
                }

                &:first-child{
                    padding-left: 0px;
                }

                &:last-child{
                    padding-right: 0px;

                    div.dropdown{
                        display: flex;
                        justify-content: center;
                    }
                }
            }
        }
    }

    thead {
        tr {
            th {
                padding: 10px 15px;
                border-bottom: 1px solid #eee;
                border-top: none !important;
                color: $grey;
                font-weight: normal;
                font-size: 12px;
                font-weight: 500;
                text-transform: uppercase;

                &:first-child{
                    padding-left: 0px;
                }
            }
        }
    }
}

div.regular-table{
    overflow-x: auto;
    overflow-y: auto;

    div.table-pagination{
        margin-top: 30px;
        display: flex;
        justify-content: center;

        a.pagination-ctrls{
            background-color: $black;
            color: #ffffff;
            cursor: pointer;
            transition: all 0.27s ease-in-out;
            box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 10px 0px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            border-radius: 100%;

            svg{
                width: 25px;
                height: 25px;
                
                path{
                    fill: #ffffff;
                }
            }

            &.disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }

            &:last-child{
                margin-left: 15px;
            }
        }
    }
}
