<div class="timeline-container">
	<div class="timeline-header" data-el="tl-header">
		<div class="timeline-header__botline">
			<div class="lp">
				<div class="nav-link-holder">
					<a href="<?php echo cl_link("settings/email"); ?>" data-spa="true">
						<?php echo cl_translate("User e-mail"); ?>
					</a>
				</div>
			</div>
			<div class="cp">
				<a href="<?php echo cl_link("home"); ?>">
					<img src="{%config site_logo%}" alt="Logo">
				</a>
			</div>
			<div class="rp">
				<div class="nav-link-holder">
					<span class="go-back" onclick="SMColibri.go_back();">
						<?php echo cl_ficon('arrow_back'); ?>
					</span>
				</div>
			</div>
		</div>
	</div>
	<div class="profile-settings">
		<div class="profile-settings__content">
			<div class="settings-form">
				<form class="form" id="cl-email-settings-vue-app" v-on:submit="submit_form($event)">
	                <div class="form-group no-mb">
                        <label>
                            <?php echo cl_translate("Email address"); ?>
                        </label>
                        <input v-model.trim="$v.email.$model" type="email" class="form-control" name="email" placeholder="<?php echo cl_translate("Enter your email address"); ?>">
                        <div class="invalid-main-feedback" v-if="is_valid_email">
                            {{invalid_feedback_email}}
                        </div>
                    </div>
                    <div class="form-group" v-if="unsuccessful_attempt">
                        <div class="invalid-main-feedback">
                            <?php echo cl_translate("Something went wrong while trying to save your changes, please try again later"); ?>
                        </div>
                    </div>
                    <div class="form-group" v-else>
                        <div class="form-info-label">
                            <?php echo cl_translate("Please note that after changing the email address, the email address that you use during authorization will be replaced by a new one"); ?>
                        </div>
                    </div>
                    <input type="hidden" class="d-none" value="<?php echo fetch_or_get($cl['csrf_token'],'none'); ?>" name="hash">
	                <div class="form-group no-mb d-flex">
	                	<button v-if="submitting != true" v-bind:disabled="$v.$invalid == true" type="submit" class="ml-auto btn btn-custom main-inline lg">
	                        <?php echo cl_translate("Save changes"); ?>
	                    </button>
	                    <button v-else disabled="true" type="button" class="ml-auto btn btn-custom main-inline lg">
	                        <?php echo cl_translate("Please wait"); ?>
	                    </button>
	                </div>
	            </form>
			</div>
		</div>
	</div>

	<script>
		"use strict";

		$(document).ready(function($) {
			Vue.use(window.vuelidate.default);
			var VueValids = window.validators;

			new Vue({
				el: "#cl-email-settings-vue-app",
				data: {
					submitting: false,
					unsuccessful_attempt: false,
					invalid_feedback_email: "",
					email: "<?php echo($me["email"]) ?>",
					doubling_email: false,
					email_denied: false
				},
				computed: {
					is_valid_email: function() {
						if (this.$v.email.required == true && this.$v.email.$error) {
							this.invalid_feedback_email = "<?php echo cl_translate("The email address you entered does not match the valid format."); ?>";
							return true;
						}

						else if(this.doubling_email == true) {
							this.invalid_feedback_email = "<?php echo cl_translate("This email address is already taken"); ?>";
							return true;
						}
						
						else if(this.email_denied == true) {
							this.invalid_feedback_email = "<?php echo cl_translate("This email address is reserved and not available for use"); ?>";
							return true;
						}

						else {
							this.invalid_feedback_email = "";
							return false;
						}
					}
				},
				validations: {
					email: {
						required: VueValids.required,
						email: VueValids.email,
						min_length: VueValids.minLength(8),
						max_length: VueValids.maxLength(55),
					}
				},
				methods: {
					submit_form: function(_self = null) {
						_self.preventDefault();
						var _app_ = this;

						$(_self.target).ajaxSubmit({
							url: "<?php echo cl_link("native_api/settings/save_profile_email"); ?>",
							type: 'POST',
							dataType: 'json',
							beforeSend: function() {
								_app_.submitting = true;
								_app_.unsuccessful_attempt = false;
								_app_.doubling_email = false;
								_app_.email_denied = false;
							},
							success: function(data) {
								if (data.status == 200) {
									setTimeout(function() {
										SMColibri.spa_load("<?php echo cl_link('confirm_email'); ?>");
									}, 1000);
								}

								else if(data.err_code == "doubling_email") {
									_app_.doubling_email = true;
								}

								else if(data.err_code == "denied_email") {
									_app_.email_denied = true;
								}

								else {
									_app_.unsuccessful_attempt = true;
								}
							},
							complete: function() {
								_app_.submitting = false;
							}
						});
					}
				}
			});
		});
	</script>
</div>