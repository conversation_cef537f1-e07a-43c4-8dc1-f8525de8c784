<div class="cp-app-container" data-app="auto-follow">
    <div class="current-page-name">
        <div class="lp">
            <h1>
                Auto follow settings
            </h1>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        Manage (Auto-follow) system settings
                    </h2>
                </div>
                <div class="body">
                    <form class="form" data-an="form">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group" data-an="auto_follow_list-input">
                                    <label>
                                        Usernames separated by commas
                                    </label>
                                    <div class="form-line">
                                        <textarea name="auto_follow_list" class="form-control" rows="2" placeholder="Enter usernames separated by commas, for example: username1, username2, username3" spellcheck="false">{%config auto_follow_list%}</textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12 no-mb">
                                <button data-an="submit-ctrl" type="submit" class="btn btn-primary">
                                    Save changes
                                </button>
                            </div>
                        </div>
                        <input type="hidden" class="d-none" value="<?php echo fetch_or_get($cl['csrf_token'], 'none'); ?>" name="hash">
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo cl_template('cpanel/assets/auto_follow/scripts/app_master_script'); ?>