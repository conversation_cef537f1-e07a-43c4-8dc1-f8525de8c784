<div class="main-footer">
	<ul class="footer-nav">
		<?php if ($cl["pn"] != "guest"): ?>
			<li class="footer-nav-item">
				<a href="<?php echo cl_link('search'); ?>">
					<?php echo cl_translate("Explore"); ?>
				</a>
			</li>
		<?php endif; ?>

		<?php if (not_empty($cl['is_logged'])): ?>
			<?php if ($cl['config']['advertising_system'] == 'on'): ?>
				<li class="footer-nav-item">
					<a href="<?php echo cl_link('ads'); ?>">
						<?php echo cl_translate("Advertising"); ?>
					</a>
				</li>
			<?php endif; ?>
		<?php endif; ?>

		<li class="footer-nav-item">
			<a href="<?php echo cl_link('terms_of_use'); ?>" data-spa="true">
				<?php echo cl_translate("Terms of use"); ?>
			</a>
		</li>
		<li class="footer-nav-item">
			<a href="<?php echo cl_link('privacy_policy'); ?>" data-spa="true">
				<?php echo cl_translate("Privacy policy"); ?>
			</a>
		</li>
		<li class="footer-nav-item">
			<a href="<?php echo cl_link('cookies_policy'); ?>" data-spa="true">
				<?php echo cl_translate("Cookies"); ?>
			</a>
		</li>
		<li class="footer-nav-item">
			<a href="<?php echo cl_link('about_us'); ?>" data-spa="true">
				<?php echo cl_translate("About us"); ?>
			</a>
		</li>
		<?php if ($cl["config"]["system_api_status"] == "on"): ?>
			<li class="footer-nav-item">
				<a href="<?php echo cl_link('api_docs'); ?>">
					API
				</a>
			</li>
		<?php endif; ?>
		<li class="footer-nav-item">
			<a href="<?php echo cl_link('faqs'); ?>" data-spa="true">
				<?php echo cl_translate('Help center'); ?>
			</a>
		</li>

		<?php if (not_empty($cl["curr_lang"])): ?>
			<li class="footer-nav-item dropdown">
				<a class="dropdown-toggle" data-toggle="dropdown">
					<?php echo cl_translate($cl["curr_lang"]["lang_data"]["name"]); ?>
				</a>
				<div class="dropdown-menu dropdown-icons">
					<?php foreach ($cl["languages"] as $lang): ?>
						<a class="dropdown-item <?php if($lang["slug"] == $cl["curr_lang"]["lang_data"]["slug"]) {echo('active');} ?>" href="<?php echo cl_link(cl_strf("language/%s", $lang["slug"])); ?>">
							<?php echo cl_translate($lang["name"]); ?>
						</a>
					<?php endforeach; ?>
				</div>
			</li>
		<?php endif; ?>

		<li class="footer-nav-item">
			<a href="<?php echo cl_link('about_us'); ?>">&copy; {%config name%} - <?php echo date('Y') ?>.</a>
		</li>
	</ul>

	<?php if (not_empty($cl["config"]["android_app_url"]) || not_empty($cl["config"]["ios_app_url"])): ?>
		<div class="footer-nav-get-apps">
			<?php if (not_empty($cl["config"]["android_app_url"])): ?>
				<a href="<?php echo($cl["config"]["android_app_url"]); ?>" target="_blank" title="Get it on Google Play">
					<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.17 11.96l-7.65 9.4C4.38 21.33 3.6 20.4 3.6 19V5c0-1.4.78-2.35 1.95-2.35h.03l.04.01 7.55 9.3zm3.81-3.1l2.1 1.2c.85.5 1.32 1.18 1.32 1.94 0 .75-.47 1.44-1.32 1.93l-2.05 1.18-2.57-3.16 2.52-3.1zm-.88-.5L7.5 3.37l6.32 7.78 2.28-2.8zM7.33 20.71l6.49-7.97 2.33 2.87-8.82 5.1z" fill="currentColor"/></svg>
				</a>
			<?php endif; ?>

			<?php if (not_empty($cl["config"]["ios_app_url"])): ?>
				<a href="<?php echo($cl["config"]["ios_app_url"]); ?>" target="_blank" title="Available in the App Store">
					<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M20.49 16.59c-.3.7-.66 1.34-1.08 1.93a9.82 9.82 0 01-1.38 1.67c-.55.51-1.15.77-1.78.79-.46 0-1-.13-1.64-.4a4.72 4.72 0 00-1.77-.39c-.57 0-1.18.13-1.82.4a4.9 4.9 0 01-1.58.4c-.6.03-1.21-.23-1.82-.8a11.99 11.99 0 01-2.98-4.78A11.15 11.15 0 014 11.8c0-1.34.29-2.5.87-3.46a5.1 5.1 0 014.28-2.54c.49 0 1.12.15 ********.3 1.3.45 ********** 0 .72-.18 1.67-.53.9-.32 1.66-.46 2.28-.4 1.68.13 2.94.8 3.79 2a4.21 4.21 0 00-2.24 3.82 4.55 4.55 0 002.77 4.09c-.1.32-.23.63-.35.93zM16.63 1.4c0 1-.36 1.93-1.1 2.8-.87 1.03-1.93 1.62-3.09 1.53a4.38 4.38 0 011.14-3.2c.37-.44.84-.79 1.41-1.07s1.11-.43 1.62-.46l.02.4z" fill="currentColor"/></svg>
				</a>
			<?php endif; ?>
		</div>
	<?php endif; ?>
</div>