body[data-bg=default] {
    --cl-primary-bg-color: #ffffff;
    --cl-primary-bg-color-opacity: #fff;
    --cl-secondary-bg-color: #fff;l
    --cl-primary-border-color: rgba(35, 40, 47, .15);
    --cl-secondary-border-color: #4c8bf5;
    --cl-primary-text-color: #000;
    --cl-secondary-text-color: #212529;
    --cl-tertiary-text-color: #212529;
    --cl-input-placeholder-color: #000;
    --cl-input-bg-color: #ff69d4;
    --cl-primary-box-shadow: rgba(0, 0, 0, 0.20) 0px 10px 30px 0px;
    --cl-secondary-box-shadow: rgba(0, 0, 0, 0.12) 0px 10px 30px 0px;
    --cl-modal-backdrop: rgba(35, 40, 47, .2);
    --cl-event-hover-bg: #4c8bf5;
    --plyr-audio-control-color: #000;
}







body[data-bg=dark] {
    --cl-primary-bg-color: #000;
    --cl-primary-bg-color-opacity: #000;
    --cl-secondary-bg-color: #000;
    --cl-primary-border-color: #282c30;
    --cl-secondary-border-color: #ff69d4;
    --cl-primary-text-color: #fff;
    --cl-secondary-text-color: #f8f9fa;
    --cl-tertiary-text-color: #e9ecef;
    --cl-input-placeholder-color: #fff;
    --cl-input-bg-color: #ff69d4;
    --cl-primary-box-shadow: rgba(0, 0, 0, 0.15) 0px 1px 10px 0px;
    --cl-secondary-box-shadow: rgba(0, 0, 0, 0.10) 0px 10px 30px 0px;
    --cl-modal-backdrop: #000;
    --cl-event-hover-bg: #4c8bf5;
    --plyr-audio-control-color: #fff;
}




body[data-skin=default] {
    --cl-primary-color: #4c8bf5;
    --plyr-color-main: #ff69d4;
    --cl-primary-color-darken-5: #4c8bf5;
    --cl-primary-link-color: #4c8bf5;
}




body[data-skin=yellow] {
    --cl-primary-color: #ffa500;
    --plyr-color-main: #ffa500;
    --cl-primary-color-darken-5: #ffa500;
    --cl-primary-link-color: #ffa500;
}






body[data-skin=pink] {
    --cl-primary-color: #ff3131;
    --plyr-color-main: #ff3131;
    --cl-primary-color-darken-5: #ff3131;
    --cl-primary-link-color: #ff3131;
}




body[data-skin=green] {
    --cl-primary-color: #7ed957;
    --plyr-color-main: #7ed957;
    --cl-primary-color-darken-5: #7ed957;
    --cl-primary-link-color: #7ed957;
}








body main.main-content-container div.main-content-container-inner div.timeline-container-wrapper div.timeline-container-inner div.timeline-container div.timeline-posts-container div.timeline-posts-ls div.post-list-item div.post-list-item__content {
        padding-left: 7px;
        padding-right: 8px;
    }







body div.cl-plyr-audio div.plyr--audio div.plyr__controls {
        padding: 12px;
        --plyr-control-icon-size: 18px;
        --plyr-control-spacing: 15px;
    }


:root {
    scroll-behavior: auto !important;
    --plyr-range-track-height: 16px;
    --plyr-range-thumb-height: 12px;
    --plyr-control-icon-size: 15px;
    --plyr-control-spacing: 8px;
    --plyr-control-radius: 0px;
    --plyr-audio-controls-background: none;
}






element.style {
}
body main.main-content-container div.main-content-container-inner div.timeline-container-wrapper div.timeline-container-inner div.timeline-container div.timeline-header div.timeline-header__botline div.cp a img {
    height: 38px;
}



body.cl-app-guest div.welcome-page div.welcome-page__body div.welcome-page__rp div.welcome-page__rp-inner div.welcome-page-form div.welcome-page-form__header a.logo img {
    height: 95%;
}











body main.main-content-container div.main-content-container-inner div.timeline-container-wrapper div.timeline-container-inner div.timeline-container div.timeline-posts-container div.timeline-posts-ls div.post-list-item div.post-list-item__content div.post-data div.post-data__content div.post-data__content-inner div.post-data-layout div.post-data-layout__content div.publication-text {
        font-size: 16.4px;
    }
}




body main.main-content-container div.main-content-container-inner div.timeline-container-wrapper div.timeline-container-inner div.timeline-container div.timeline-posts-container div.timeline-posts-ls div.post-list-item div.post-list-item__content div.post-data div.post-data__content div.post-data__content-inner div.post-data-layout div.post-data-layout__content div.publication-text {
    overflow: hidden;
    margin-bottom: 10px;
    font-size: 15px;
    line-height: 1.5;
    font-weight: 450;
    color: var(--cl-primary-text-color);
    word-break: break-word;
}






body.cl-app-guest div.welcome-page div.welcome-page__footer {
    padding-top: 15px;
    flex-shrink: 0;
    background-color: #000;
    position: relative;
    z-index: 10;
}




body.cl-app-guest div.welcome-page div.welcome-page__footer div.welcome-page__bp div.main-footer ul.footer-nav li.footer-nav-item > a {
    font-size: 13.4px;
    color: #A9A9A9;
    line-height: 15px;
    text-decoration: none;
}




body.cl-app-guest div.welcome-page div.welcome-page__body div.welcome-page__rp {
    padding-top: 5px;
    padding-bottom: 50px;
    padding-left: 5px;
}










/* Ensure the thumbnail fills the container */
.plyr__poster {
    width: 100%;
    height: 100%;
    background-size: cover; /* Ensure thumbnail fills the container */
    background-position: center; /* Center the thumbnail */
    position: absolute;
    top: 0;
    left: 0;
}













@media (max-width: 768px) {
    .timeline-header {
        position: fixed !important;
        top: 0;
        left: 0;
        width: 100%;
        z-index: 1000;
        background-color: #fff;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); /* Optional shadow */
        display: flex;
        flex-direction: column;
        justify-content: center;
        border-bottom: none; /* Ensure no border */
    }

    .timeline-header__botline {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 15px;
        box-sizing: border-box;
        border-bottom: none; /* Remove any extra border */
    }

    /* Ensure content below doesn't get hidden */
    body {
        padding-top: 60px; /* Adjust based on header height */
        margin: 0; /* Prevent extra gaps */
    }
}














html {
    overscroll-behavior-y: none; /* Prevents pull-to-refresh stretch */
}

body {
    padding-top: -1px; /* Adjust to match the header height */
    margin: 0;
    overflow-y: auto; /* Ensures scrolling still works */
    -webkit-overflow-scrolling: touch; /* Keeps smooth scrolling on iOS */
}










































/* Disable Chrome's pull-to-refresh */
body {
  overscroll-behavior: none;
}

#refreshIndicator {
  position: fixed;
  top: -50px;
  left: 50%;
  transform: translateX(-50%);
  background: #fff;
  padding: 10px 15px;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  font-size: 14px;
  font-weight: bold;
  transition: top 0.3s ease, opacity 0.3s ease;
  opacity: 0;
}



















body main.main-content-container div.main-content-container-inner div.timeline-container-wrapper div.timeline-container-inner div.timeline-container div.timeline-posts-container div.timeline-posts-ls div.post-list-item div.post-list-item__content div.post-data div.post-data__avatar div.avatar-holder img {
    width: 140%;
    height: 140%;
    border-radius: 50%;
    overflow: auto;
    background: var(--cl-primary-border-color);
    margin-top: 5px; /* Moves down */
    margin-left: -4px; /* Moves left (adjust value as needed) */
}




/* Default for mobile */
body.cl-app-thread main.main-content-container div.main-content-container-inner div.timeline-container-wrapper div.timeline-container div.timeline-posts-container div.timeline-posts-ls.thread-data div.post-list-item div.post-list-item__content div.post-data div.post-data__content div.post-data__content-inner div.post-data-layout div.post-data-layout__publisher div.post-avatar div.avatar-holder img {
    width: 127%;
    height: 127%;
    border-radius: 50%;
    overflow: auto;
    background: var(--cl-primary-border-color);
    margin-top: 18px; /* Works well on mobile */
    margin-left: 1px; /* Adjusted for alignment */
}

/* Adjust for desktops (larger screens) */
@media (min-width: 1200px) {
    body.cl-app-thread main.main-content-container div.main-content-container-inner div.timeline-container-wrapper div.timeline-container div.timeline-posts-container div.timeline-posts-ls.thread-data div.post-list-item div.post-list-item__content div.post-data div.post-data__content div.post-data__content-inner div.post-data-layout div.post-data-layout__publisher div.post-avatar div.avatar-holder img {
        margin-top: 1px; /* Corrects misalignment on desktop */
    }
}









body main.main-content-container div.main-content-container-inner div.timeline-container-wrapper div.timeline-container-inner div.timeline-container div.timeline-posts-container div.timeline-posts-ls div.post-list-item div.post-list-item__content div.post-data div.post-data__avatar div.avatar-holder img {
    width: 140%;
    height: 140%;
    border-radius: 50%;
    overflow: hidden;
    background: var(--cl-primary-border-color);
    margin-top: 6px;
    margin-left: 45px;
}




@media screen and (min-width: 1024px) {
  body main.main-content-container div.main-content-container-inner div.timeline-container-wrapper div.timeline-container-inner div.timeline-container div.timeline-posts-container div.timeline-posts-ls div.post-list-item div.post-list-item__content div.post-data div.post-data__avatar div.avatar-holder img {
    width: 140%;
    height: 140%;
    border-radius: 50%;
    overflow: hidden;
    background: var(--cl-primary-border-color);
    margin-top: 1px;
    margin-left: 45px;
  }
}








.conversation__footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    padding: 10px;
    transition: bottom 0.3s ease; /* Smooth transition when keyboard appears */
    border: none;
    background: none;
}

.conversation {
    overflow-y: auto;
    flex-grow: 1;
    padding-bottom: 80px; /* Prevents last messages from being hidden */
}

/* Fix for Mobile Keyboard Pushing Content */
@media screen and (max-width: 768px) {
    .message-input textarea:focus {
        background: none;
    }

    body.keyboard-open .conversation__footer {
        bottom: 50px; /* Adjust based on keyboard height */
    }
}






.in-feed .plyr__controls {
  display: none !important;
}
















body.cl-app-conversation div.conversation div.conversation__body div.conversation-messages div.conversation-messages__item.right div.message-data div.message-data__body div.message-text {
    background: #ff69d4;
}



body.cl-app-conversation div.conversation div.conversation__body div.conversation-messages div.conversation-messages__item div.message-data div.message-data__body div.message-text {
    padding: 12px var(--cl-primary-offset-size);
    font-size: 14px;
    color: #000;
    border-radius: var(--cl-primary-border-radius);
}



























a[onclick^="SMColibri.pin_profile_post"] {
    display: none !important;
}





.uname[style*="--cl-uname-color"] {
    display: none !important;
}
















body main.main-content-container div.main-content-container-inner div.timeline-container-wrapper div.timeline-container-inner div.timeline-container div.timeline-posts-container div.timeline-posts-ls div.post-list-item div.post-list-item__content div.post-data div.post-data__content div.post-data__content-inner div.post-data-layout div.post-data-layout__controls button.ctrls-item span.ctrls-item__icon svg path, body main.main-content-container div.main-content-container-inner div.timeline-container-wrapper div.timeline-container-inner div.timeline-container div.timeline-posts-container div.timeline-posts-ls div.post-list-item div.post-list-item__content div.post-data div.post-data__content div.post-data__content-inner div.post-data-layout div.post-data-layout__controls button.ctrls-item div.ctrls-item__icon svg path {
    fill: rgba(0, 0, 0, 0);
    stroke: var(--cl-secondary-text-color);
    stroke-width: 2;
}




body main.main-content-container div.main-content-container-inner div.timeline-container-wrapper div.timeline-container-inner div.timeline-container div.timeline-posts-container div.timeline-posts-ls div.post-list-item div.post-list-item__content div.post-data div.post-data__content div.post-data__content-inner div.post-data-layout div.post-data-layout__controls button.ctrls-item.reposted span.ctrls-item__icon svg path, body main.main-content-container div.main-content-container-inner div.timeline-container-wrapper div.timeline-container-inner div.timeline-container div.timeline-posts-container div.timeline-posts-ls div.post-list-item div.post-list-item__content div.post-data div.post-data__content div.post-data__content-inner div.post-data-layout div.post-data-layout__controls button.ctrls-item.reposted a.ctrls-item__icon svg path {
    fill: rgba(0, 0, 0, 0) !important;
}



body main.main-content-container div.main-content-container-inner div.timeline-container-wrapper div.timeline-container-inner div.timeline-container div.timeline-posts-container div.timeline-posts-ls div.post-list-item div.post-list-item__header div.publication-repost svg path {
    fill: rgba(0, 0, 0, 0);
}






body main.main-content-container div.main-content-container-inner div.timeline-container-wrapper div.timeline-container-inner div.timeline-container div.timeline-posts-container div.timeline-posts-ls div.post-list-item div.post-list-item__content div.post-data div.post-data__content div.post-data__content-inner div.post-data-layout div.post-data-layout__controls {
    display: flow;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;
    margin-left: -10px;
}










body main.main-content-container div.main-content-container-inner div.timeline-container-wrapper div.timeline-container-inner div.timeline-container div.timeline-posts-container div.timeline-posts-ls div.post-list-item div.post-list-item__content div.post-data div.post-data__content div.post-data__content-inner div.post-data-layout div.post-data-layout__controls button.ctrls-item span.ctrls-item__icon, body main.main-content-container div.main-content-container-inner div.timeline-container-wrapper div.timeline-container-inner div.timeline-container div.timeline-posts-container div.timeline-posts-ls div.post-list-item div.post-list-item__content div.post-data div.post-data__content div.post-data__content-inner div.post-data-layout div.post-data-layout__controls button.ctrls-item div.ctrls-item__icon {
    display: flow;
    align-items: center;
    justify-content: center;
    padding: 0;
    margin: 0;
    width: 40px;
    height: 40px;
    flex-shrink: 0;
    border-radius: 100%;
}












.mobi-num.ctrls-item {
    font-size: 13px !important;
    color: #999da1 !important;
    margin-top: -1px !important;
    margin-bottom: 2px !important;
    margin-left: 3px !important;
}








body main.main-content-container div.main-content-container-inner div.timeline-container-wrapper div.timeline-container-inner div.timeline-container div.timeline-posts-container div.timeline-posts-ls div.post-list-item div.post-list-item__content div.post-data div.post-data__content div.post-data__content-inner div.post-data-layout div.post-data-layout__publisher div.post-username {
    flex: 1;
    line-height: 4.5;
}







.feed_follow .follow_head h6 {
    font-size: 40px;
    font-weight: 900;
    line-height: 24px;
    background: linear-gradient(90deg, #feda75, #fa7e1e, #d62976, #962fbf, #4f5bd5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}










/* Ensuring status avatar size */
body.cl-app-home div.homepage div.homepage__swifts div.swifts-slider div.swiper div.swiper-wrapper div.swiper-slide div.swift-item div.swift-item__body div.swift-item__avatar {
    width: 97px;
    height: 97px;
    border-radius: 100%;
    border: 4px solid var(--cl-secondary-border-color);
    overflow: hidden;
}

/* Ensuring each status item has proper spacing */
body.cl-app-home div.homepage div.homepage__swifts div.swifts-slider div.swiper div.swiper-wrapper div.swiper-slide div.swift-item {
    display: flex; /* Use flex for better layout */
    flex-direction: column;
    flex-wrap: nowrap;
    justify-content: center;
    align-items: center;
    width: var(--cl-primary-avatar-size);
    overflow: initial;
    cursor: pointer;
    margin: 25px; /* Add spacing between statuses */
}

/* Alternatively, use gap on the container */
body.cl-app-home div.homepage div.homepage__swifts div.swifts-slider div.swiper div.swiper-wrapper {
    display: flex;
    gap: 10px; /* Add spacing between items */
}





/* Reduce extra space around the swift container */
#homepage-swifts-slider {
    padding-top: 5px;  /* Reduce top padding */
    padding-bottom: 5px; /* Reduce bottom padding */
    margin-top: -10px;  /* Move the section up */
    margin-bottom: -10px; /* Move the section up */
}

/* Ensure wrapper does not add extra space */
#homepage-swifts-slider .swiper-wrapper {
    align-items: center; /* Keep items centered */
    height: auto; /* Ensure no unnecessary height */
}

/* Make sure swiper-slide does not have extra spacing */
#homepage-swifts-slider .swiper-slide {
    margin-bottom: 0; /* Remove unnecessary bottom margin */
}

/* Optional: Adjust the parent container if needed */
body.cl-app-home div.homepage div.homepage__swifts {
    padding-top: 0px; /* Reduce space above */
    padding-bottom: 0px; /* Reduce space below */
    margin-top: -10px; /* Pull the section up */
    margin-bottom: -10px; /* Pull the section down */
}
















/* Apply the margin only to the post container in the home feed, excluding the thread timeline */
.timeline-posts-ls:not(.thread-data) .post-list-item {
    margin-left: -40px;  /* Stretch the post layout */
}

/* Ensure user name holder is properly aligned */
.timeline-posts-ls .post-list-item__content .user-name-holder {
    margin-left: 55px;  /* Adjust user name holder margin */
    transform: translateY(-8px);  /* Adjust as needed */
}





/* Target only the user name holder in the thread timeline, without affecting other content */
.timeline-posts-ls.thread-data .post-list-item__content .user-name-holder {
    margin-left: 3px;  /* Adjust user name holder margin */
    transform: translateY(3px);  /* Adjust user name position */
}

/* Prevent affecting other content or layout of the thread post */
.timeline-posts-ls.thread-data .post-list-item__content {
    margin-left: 0;  /* Reset any unwanted margin shifts */
}












element.style {
}
body main.main-content-container div.main-content-container-inner div.timeline-container-wrapper div.timeline-container-inner div.timeline-container div.timeline-posts-container div.timeline-posts-ls div.post-list-item div.post-list-item__content div.post-data div.post-data__content div.post-data__content-inner div.post-data-layout div.post-data-layout__content div.publication-video {
    border: 1px solid var(--cl-primary-border-color);
    border-radius: var(--cl-primary-border-radius);
    background: #e6ecf0;
    overflow: hidden;
}

.publication-video {
    display: block;
    width: 100%;
    max-width: 100%;
    margin: 0;
    box-sizing: border-box;
    border-radius: var(--cl-primary-border-radius);
    overflow: hidden;
}













/* Base styling for the mobile navbar */
body div.mobile-bottom-navbar {
    position: fixed;
    left: 5%;
    right: 5%;
    bottom: 20px !important;
    width: 90%;
    height: 65px;
    border-radius: 50px;
    z-index: 1000;
    display: flex;
    justify-content: space-around;
    align-items: center;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
    transition: background 0.3s ease;
}







@media screen and (max-width: 991px) {
  body main.main-content-container div.main-content-container-inner div.timeline-container-wrapper div.timeline-container-inner div.timeline-container div.timeline-posts-container div.timeline-posts-ls div.post-list-item div.post-list-item__content div.post-data div.post-data__content div.post-data__content-inner div.post-data-layout div.post-data-layout__controls {
    display: flow;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;
    margin-left: -5px;
  }
}


selector-of-the-element {
  margin-left: 3px !important;
}




/* Ensure timeline containers have no white background */
.timeline-posts-container,
.timeline-posts-ls,
.post-list-item {
    background: var(--cl-primary-bg-color, #f9f9f9) !important; /* fallback to a light gray */
    transition: background 0.2s;
}

/* Prevent overlays or loading backgrounds */
.timeline-posts-container::before,
.timeline-posts-container::after,
.timeline-posts-ls::before,
.timeline-posts-ls::after {
    background: transparent !important;
    box-shadow: none !important;
    content: none !important;
    display: none !important;
}

/* Remove any overlay that might appear on fast scroll */
body, html {
    background: var(--cl-primary-bg-color, #f9f9f9) !important;
}




body main.main-content-container div.main-content-container-inner div.timeline-container-wrapper div.timeline-container-inner div.timeline-container div.timeline-posts-container div.timeline-posts-ls div.post-list-item div.post-list-item__content {
    padding: 0 !important;
    border-bottom: none !important;
}




















