<div class="timeline-pubbox-container" >
	<form class="form" id="vue-pubbox-app-<?php echo($cl['pb_id']); ?>" v-on:submit="publish($event)">
		<div class="timeline-pubbox">
			<div class="timeline-pubbox__header">
				<div class="timeline-pubbox__textinput">
					<div class="pubbox-avatar">
						<img src="<?php echo($me['avatar']); ?>" alt="Avatar">
					</div>
					<div class="pubbox-input">
						<textarea ref="text_input" v-on:input.trim="text_input_trigger" v-on:blur="text_blur_trigger" class="autoresize" v-model="text" name="post_text" v-bind:placeholder="text_ph"></textarea>
					</div>
				</div>
			</div>
			<div class="timeline-pubbox__body">
				<div class="timeline-pubbox__autocomplete">
					<div class="pubbox-autocomplete" v-if="mentions.users.length">
						<div class="pubbox-autocomplete__usernames">
							<div class="pubbox-autocomplete__usernames-list">
								<div v-for="i in mentions.users" class="username-list-item" v-on:click="mention_autocomplete(i.username)">
									<div class="username-list-item__avatar">
										<img v-bind:src="i.avatar" alt="IMG">
									</div>
									<div class="username-list-item__username">
										<span class="user-name-holder">
											<span class="user-name-holder__name">
												{{i.name}}
											</span>
											<span v-if="i.verified == 1" class="user-name-holder__badge">
												<?php echo cl_icon("verified_user_badge"); ?>
											</span>
										</span>
										<span class="username">
											@{{i.username}}
										</span>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="pubbox-autocomplete" v-if="hashtags.tags.length">
						<div class="pubbox-autocomplete__hashtags">
							<div class="pubbox-autocomplete__hashtags-list">
								<div v-for="i in hashtags.tags" class="hashtag-list-item" v-on:click="hashtag_autocomplete(i.tag)">
									<div class="hashtag-list-item__hashtag">
										#{{i.tag}} <span>({{i.posts}}) <?php echo cl_translate("Posts"); ?></span>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div style="margin-top: -20px !important;">
				<div class="timeline-pubbox__emoticons" v-if="emoticons_picker.status">
					<div class="pubbox-emoticons">
						<div class="pubbox-emoticons__type-nav">
							<span class="pubbox-emoticons__type-nav-item" v-bind:class="{'active': (emoticons_picker.active_group == 'fused')}" v-on:click="emoticons_picker.active_group = 'fused'">
								<span class="emoji_group_icon">
									<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36 36"><circle fill="#99AAB5" cx="18" cy="18" r="18"/><circle fill="#E1E8ED" cx="18" cy="18" r="14"/><path fill="#66757F" d="M19 18c0 .553-.447 1-1 1-.552 0-1-.447-1-1V7c0-.552.448-1 1-1 .553 0 1 .448 1 1v11z"/><path fill="#66757F" d="M26.661 13c.276.479.112 1.09-.366 1.366l-7.795 4.5c-.478.276-1.089.112-1.365-.366s-.112-1.09.365-1.366l7.795-4.5c.478-.276 1.09-.112 1.366.366z"/></svg>
								</span>
							</span>
							<span class="pubbox-emoticons__type-nav-item" v-bind:class="{'active': (emoticons_picker.active_group == 'people')}" v-on:click="emoticons_picker.active_group = 'people'">
								<span class="emoji_group_icon">
									<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36 36"><circle fill="#FFCC4D" cx="18" cy="18" r="18"/><path fill="#664500" d="M18 21c-3.623 0-6.027-.422-9-1-.679-.131-2 0-2 2 0 4 4.595 9 11 9 6.404 0 11-5 11-9 0-2-1.321-2.132-2-2-2.973.578-5.377 1-9 1z"/><path fill="#FFF" d="M9 22s3 1 9 1 9-1 9-1-2 4-9 4-9-4-9-4z"/><ellipse fill="#664500" cx="12" cy="13.5" rx="2.5" ry="3.5"/><ellipse fill="#664500" cx="24" cy="13.5" rx="2.5" ry="3.5"/></svg>
								</span>
							</span>
							<span class="pubbox-emoticons__type-nav-item" v-bind:class="{'active': (emoticons_picker.active_group == 'nature')}" v-on:click="emoticons_picker.active_group = 'nature'">
								<span class="emoji_group_icon">
									<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36 36"><circle fill="#C1694F" cx="7" cy="6" r="6"/><circle fill="#C1694F" cx="29" cy="6" r="6"/><circle fill="#E6AAAA" cx="7" cy="6" r="4"/><circle fill="#E6AAAA" cx="29" cy="6" r="4"/><path fill="#C1694F" d="M35 22S33.692 0 18 0 1 22 1 22c0 5.872 4.499 10.323 12.216 11.61C14.311 35.06 16.044 36 18 36s3.688-.94 4.784-2.39C30.501 32.323 35 27.872 35 22z"/><circle fill="#DD2E44" cx="18" cy="30" r="4"/><path fill="#D99E82" d="M18 20S7 23.687 7 27s2.687 6 6 6c2.088 0 3.925-1.067 5-2.685C19.074 31.933 20.912 33 23 33c3.313 0 6-2.687 6-6s-11-7-11-7z"/><path fill="#272B2B" d="M11 17s0-2 2-2 2 2 2 2v2s0 2-2 2-2-2-2-2v-2zm10 0s0-2 2-2 2 2 2 2v2s0 2-2 2-2-2-2-2v-2zm-7.875 8c-1.624 1 3.25 4 4.875 4s6.499-3 4.874-4-8.124-1-9.749 0z"/></svg>
								</span>
							</span>
							<span class="pubbox-emoticons__type-nav-item" v-bind:class="{'active': (emoticons_picker.active_group == 'objects')}" v-on:click="emoticons_picker.active_group = 'objects'">
								<span class="emoji_group_icon">
									<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36 36"><circle fill="#F5F8FA" cx="18" cy="18" r="18"/><path d="M18 11c-.552 0-1-.448-1-1V3c0-.552.448-1 1-1s1 .448 1 1v7c0 .552-.448 1-1 1zm-6.583 4.5c-.1 0-.202-.015-.302-.047l-8.041-2.542c-.527-.167-.819-.728-.652-1.255.166-.527.73-.818 1.255-.652l8.042 2.542c.527.167.819.729.652 1.255-.136.426-.53.699-.954.699zm13.625-.291c-.434 0-.833-.285-.96-.722-.154-.531.151-1.085.682-1.239l6.75-1.958c.531-.153 1.085.153 1.238.682.154.531-.151 1.085-.682 1.239l-6.75 1.958c-.092.027-.186.04-.278.04zm2.001 14.958c-.306 0-.606-.14-.803-.403l-5.459-7.333c-.33-.442-.238-1.069.205-1.399.442-.331 1.069-.238 1.399.205l5.459 7.333c.33.442.238 1.069-.205 1.399-.179.134-.389.198-.596.198zm-18.294-.083c-.197 0-.395-.058-.57-.179-.454-.316-.565-.938-.25-1.392l5.125-7.375c.315-.454.938-.566 1.392-.251.454.315.565.939.25 1.392l-5.125 7.375c-.194.281-.506.43-.822.43zM3.5 27.062c-.44 0-.844-.293-.965-.738L.347 18.262c-.145-.533.17-1.082.704-1.227.535-.141 1.083.171 1.227.704l2.188 8.062c.145.533-.17 1.082-.704 1.226-.088.025-.176.035-.262.035zM22 34h-9c-.552 0-1-.447-1-1s.448-1 1-1h9c.553 0 1 .447 1 1s-.447 1-1 1zm10.126-6.875c-.079 0-.16-.009-.24-.029-.536-.132-.864-.674-.731-1.21l2.125-8.625c.133-.536.679-.862 1.21-.732.536.132.864.674.731 1.211l-2.125 8.625c-.113.455-.521.76-.97.76zM30.312 7.688c-.17 0-.342-.043-.5-.134L22.25 3.179c-.478-.277-.642-.888-.364-1.367.275-.478.886-.643 1.366-.365l7.562 4.375c.478.277.642.888.364 1.367-.185.32-.521.499-.866.499zm-24.811 0c-.312 0-.618-.145-.813-.417-.322-.45-.22-1.074.229-1.396l6.188-4.438c.449-.322 1.074-.219 1.396.229.322.449.219 1.074-.229 1.396L6.083 7.5c-.177.126-.38.188-.582.188z" fill="#CCD6DD"/><path d="M25.493 13.516l-7.208-5.083c-.348-.245-.814-.243-1.161.006l-7.167 5.167c-.343.248-.494.684-.375 1.091l2.5 8.583c.124.426.515.72.96.72H22c.43 0 .81-.274.948-.681l2.917-8.667c.141-.419-.011-.881-.372-1.136zM1.292 19.542c.058 0 .117-.005.175-.016.294-.052.55-.233.697-.494l3.375-6c.051-.091.087-.188.108-.291L6.98 6.2c.06-.294-.016-.6-.206-.832C6.584 5.135 6.3 5 6 5h-.428C2.145 8.277 0 12.884 0 18c0 .266.028.525.04.788l.602.514c.182.156.413.24.65.24zm9.325-16.547c.106.219.313.373.553.412l6.375 1.042c.04.006.081.01.121.01.04 0 .081-.003.122-.01l6.084-1c.2-.033.38-.146.495-.314.116-.168.158-.375.118-.575l-.292-1.443C22.26.407 20.18 0 18 0c-2.425 0-4.734.486-6.845 1.356l-.521.95c-.117.213-.123.47-.017.689zm20.517 2.724l-1.504-.095c-.228-.013-.455.076-.609.249-.152.173-.218.402-.175.63l1.167 6.198c.017.086.048.148.093.224 1.492 2.504 3.152 5.301 3.381 5.782.024.084.062.079.114.151.14.195.372.142.612.142h.007c.198 0 .323.094 1.768-.753.001-.083.012-.164.012-.247 0-4.753-1.856-9.064-4.866-12.281zM14.541 33.376c.011-.199-.058-.395-.191-.544l-4.5-5c-.06-.066-.131-.122-.211-.163-5.885-3.069-5.994-3.105-6.066-3.13-.078-.025-.161-.039-.242-.039-.537 0-.695.065-1.185 2.024 2.236 4.149 6.053 7.316 10.644 8.703l1.5-1.333c.149-.132.239-.319.251-.518zm17.833-8.567c-.189-.08-.405-.078-.592.005l-6.083 2.667c-.106.046-.2.116-.274.205l-4.25 5.083c-.129.154-.19.352-.172.552.02.2.117.384.272.51.683.559 1.261 1.03 1.767 1.44 4.437-1.294 8.154-4.248 10.454-8.146l-.712-1.889c-.072-.193-.221-.347-.41-.427z" fill="#31373D"/></svg>
								</span>
							</span>
							<span class="pubbox-emoticons__type-nav-item" v-bind:class="{'active': (emoticons_picker.active_group == 'places')}" v-on:click="emoticons_picker.active_group = 'places'">
								<span class="emoji_group_icon">
									<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36 36"><path fill="#292F33" d="M34 33c0 1.104-.896 2-2 2h-3c-1.104 0-2-.896-2-2v-8c0-1.104.896-2 2-2h3c1.104 0 2 .896 2 2v8zM9 33c0 1.104-.896 2-2 2H4c-1.104 0-2-.896-2-2v-8c0-1.104.896-2 2-2h3c1.104 0 2 .896 2 2v8z"/><path fill="#3B88C3" d="M9 5c4-1 14-1 18 0 3.881.97 5 11 5 11s2 0 2 4v8s-.119 3.03-4 4c-4 1-7 1-12 1s-8 0-12-1c-3.88-.97-4-4-4-4v-8s0-4 2-4C4 16 5.12 5.97 9 5z"/><path fill="#BBDDF5" d="M18 15c3.905 0 8.623.2 12 .561L29 10c-1-3-7-3-11-3S8 7 7 10l-1 5.561C9.377 15.2 14.095 15 18 15z"/><path fill="#3B88C3" d="M5 15.5c0 .829-.671 1.5-1.5 1.5h-2C.671 17 0 16.329 0 15.5S.671 14 1.5 14h2c.829 0 1.5.671 1.5 1.5zm26 0c0 .829.672 1.5 1.5 1.5h2c.828 0 1.5-.671 1.5-1.5s-.672-1.5-1.5-1.5h-2c-.828 0-1.5.671-1.5 1.5z"/><path fill="#FFCC4D" d="M11 23c0 1.657-1.343 3-3 3H7c-1.657 0-3-1.343-3-3s1.343-3 3-3h1c1.657 0 3 1.343 3 3zm21 0c0 1.657-1.344 3-3 3h-1c-1.656 0-3-1.343-3-3s1.344-3 3-3h1c1.656 0 3 1.343 3 3z"/><path fill="#269" d="M12.001 22c-.323 0-.64-.156-.833-.445-1.742-2.614-6.319-3.565-6.365-3.574-.541-.109-.892-.636-.783-1.178.109-.541.632-.891 1.176-.783.221.044 5.431 1.119 7.636 4.426.306.46.182 1.08-.277 1.387-.171.112-.364.167-.554.167zm11.998 0c-.19 0-.383-.055-.554-.168-.46-.307-.584-.927-.277-1.387 2.204-3.307 7.415-4.382 7.636-4.426.54-.106 1.067.242 1.176.783.109.542-.241 1.068-.782 1.178-.046.009-4.623.96-6.365 3.574-.193.29-.511.446-.834.446z"/><path fill="#55ACEE" d="M18 29c-5.663 0-12.639-.225-13.707-1.293-.391-.391-.391-1.023 0-1.414.344-.345.877-.386 1.267-.122.232.1 2.285.829 12.44.829s12.208-.729 12.44-.829c.391-.264.922-.223 1.267.122.391.391.391 1.023 0 1.414C30.639 28.775 23.663 29 18 29z"/><path fill="#269" d="M25 29.5c0 1.5-3.134 2.5-7 2.5s-7-1-7-2.5c0-.828 3.134-.5 7-.5s7-.328 7 .5z"/></svg>
								</span>
							</span>
							<span class="pubbox-emoticons__type-nav-item" v-bind:class="{'active': (emoticons_picker.active_group == 'symbols')}" v-on:click="emoticons_picker.active_group = 'symbols'">
								<span class="emoji_group_icon">
									<svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 36 36" fill="none">
										<path d="M36 32C36 34.209 34.209 36 32 36H4C1.791 36 0 34.209 0 32V4C0 1.791 1.791 0 4 0H32C34.209 0 36 1.791 36 4V32Z" fill="#9266CC"/>
										<path d="M18.964 4.033C11.251 3.501 4.566 9.322 4.033 17.036C3.5 24.75 9.322 31.435 17.035 31.967C24.749 32.5 31.434 26.678 31.966 18.965C32.499 11.251 26.678 4.566 18.964 4.033V4.033ZM17.781 30.016C14.464 29.956 11.823 27.217 11.884 23.898C11.943 20.583 14.683 17.94 18 18C21.317 18.06 24.056 15.419 24.118 12.103C24.164 9.545 22.594 7.355 20.36 6.446L20.511 6.275C26.028 7.449 30.123 12.371 30.017 18.218C29.895 24.857 24.42 30.137 17.781 30.016V30.016Z" fill="white"/>
										<path d="M18 26.5C19.3807 26.5 20.5 25.3807 20.5 24C20.5 22.6193 19.3807 21.5 18 21.5C16.6193 21.5 15.5 22.6193 15.5 24C15.5 25.3807 16.6193 26.5 18 26.5Z" fill="white"/>
										<path d="M18 14.5C19.3807 14.5 20.5 13.3807 20.5 12C20.5 10.6193 19.3807 9.5 18 9.5C16.6193 9.5 15.5 10.6193 15.5 12C15.5 13.3807 16.6193 14.5 18 14.5Z" fill="#9266CC"/>
									</svg>
								</span>
							</span>
						</div>
						<div class="pubbox-emoticons__list">
							<div class="pubbox-emoticons__list-group" v-for="(emojies, emoji_group_name) in emoticons_picker.icons"  v-if="emoticons_picker.active_group == emoji_group_name">
								<span v-for="(emoji_icon, emojiName) in emojies" v-bind:key="emojiName" v-on:click="emoticon_insert(emoji_icon)" v-bind:title="emojiName">
									{{emoji_icon}}
								</span>
							</div>
						</div>
					</div>
				</div>
				<div class="timeline-pubbox__open-graph" v-if="show_og_data">
					<div class="pubbox-open-graph">
						<div class="pubbox-open-graph__data">
							<div class="pubbox-open-graph__image" v-if="og_data.image">
								<img v-bind:src="og_data.image" alt="IMG">
							</div>
							<div class="pubbox-open-graph__description">
								<h5>
									{{og_data.title}}
								</h5>
								<p>
									{{og_data.description}}
								</p>
								<a v-bind:href="og_data.url" target="_blank">
									{{og_data.url}}
								</a>
							</div>
						</div>
						<button type="button" class="delete-preview" v-on:click="rm_preview_og">
							<?php echo cl_icon('close'); ?>
						</button>
					</div>
				</div>
				<div class="timeline-pubbox__images" v-if="active_media == 'image'">
					<div class="pubbox-images">
						<div class="pubbox-images__list" data-an="post-images">
							<div v-for="img in images" class="pubbox-images__list-item">
								<img v-bind:src="img.url" alt="Image">
								<button type="button" class="delete-preview" v-on:click="delete_image(img.id)">
									<?php echo cl_icon('close'); ?>
								</button>
							</div>
						</div>
					</div>
				</div>
				<div class="timeline-pubbox__video" v-if="active_media == 'video'">	
					<div class="pubbox-video">
						<div class="pubbox-video__player">
							<div class="cl-plyr-video">
								<video data-video-ratio="16:9" class="plyr" playsinline controls v-bind:data-poster="video.poster">
									<source v-bind:src="video.source" type="video/mp4">
									<source v-bind:src="video.source" type="video/webm">
									<source v-bind:src="video.source" type="video/mov">
									<source v-bind:src="video.source" type="video/3gp">
									<source v-bind:src="video.source" type="video/ogg">
								</video>
							</div>
						</div>
						<button type="button" class="delete-preview" v-on:click="delete_video">
							<?php echo cl_icon('close'); ?>
						</button>
					</div>
				</div>
				<div class="timeline-pubbox__audio" v-if="preview_audio">
					<div class="pubbox-audio">
						<div class="pubbox-audio__player">
						    <audio controls preload="metadata">
						        <source v-bind:src="audio.source" type="audio/mp3">
						        <source v-bind:src="audio.source" type="audio/mpeg">
						        <source v-bind:src="audio.source" type="audio/wav">
						    </audio>
					    </div>
					    <div class="pubbox-audio__ctrl">
						    <button type="button" class="delete-preview" v-on:click="delete_record">
								<?php echo cl_icon('close'); ?>
							</button>
						</div>
					</div>
				</div>
				<div class="timeline-pubbox__audio" v-if="preview_music">
					<div class="pubbox-audio">
						<div class="pubbox-audio__player">
						    <audio controls preload="metadata">
						        <source v-bind:src="music.source" type="audio/mp3">
						        <source v-bind:src="music.source" type="audio/mpeg">
						        <source v-bind:src="music.source" type="audio/wav">
						    </audio>
					    </div>
					    <div class="pubbox-audio__ctrl">
						    <button type="button" class="delete-preview" v-on:click="delete_music">
								<?php echo cl_icon('close'); ?>
							</button>
						</div>
					</div>
				</div>
				<div class="timeline-pubbox__doc" v-if="preview_doc">
					<div class="pubbox-document">
						<div class="pubbox-document__body">
							<div class="pubbox-document__icon">
								<?php echo cl_ficon("document"); ?>
							</div>
						    <div class="pubbox-document__name">
						    	{{document.filename}}
						    </div>
					    </div>
					    <div class="pubbox-document__ctrl">
						    <button type="button" class="delete-preview" v-on:click="delete_document">
								<?php echo cl_icon('close'); ?>
							</button>
						</div>
					</div>
				</div>
				<div class="timeline-pubbox__gifs" v-if="active_media == 'gifs'">
					<div class="pubbox-gifs">
						<div class="pubbox-gifs__preview loading" v-if="gif_source">
							<img v-bind:src="gif_source" alt="GIF-Image" v-on:load="rm_gif_preloader($event)">
							<div class="gif-preloader">
								<span class="spinner-icon">
									<?php echo cl_icon('spinner-icon'); ?>
								</span>
							</div>
							<button type="button" class="delete-preview" v-on:click="rm_preview_gif">
								<?php echo cl_icon('close'); ?>
							</button>
						</div>
						<div class="pubbox-gifs__loader" v-else-if="gifs">
							<div class="pubbox-gifs__loader-searchbar">
								<div class="searchbar-input">
									<input v-on:input="search_gifs($event)" type="text" class="form-control" placeholder="<?php echo cl_translate("Search GIF-files"); ?>">
									<span class="searchbar-input__icon">
										<?php echo cl_icon('search'); ?>
									</span>
								</div>
								<div class="searchbar-ctrl">
									<button type="button" v-on:click="close_gifs">
										<?php echo cl_icon('close'); ?>
									</button>
								</div>
							</div>
							<div class="pubbox-gifs__loader-list" data-an="post-gifs">
								<div class="row-column row-1">
									<div v-for="gif_data in gifs_r1" class="row-column__item loading">
										<img v-on:click="preview_gif($event)" v-bind:src="gif_data.thumb" v-bind:title="gif_data.title" v-bind:data-source="gif_data.src" alt="GIF-Image" v-on:load="rm_gif_preloader($event)">
										<div class="gif-preloader"></div>
									</div>
								</div>
								<div class="row-column row-2">
									<div v-for="gif_data in gifs_r2" class="row-column__item loading">
										<img v-on:click="preview_gif($event)" v-bind:src="gif_data.thumb" v-bind:title="gif_data.title" v-bind:data-source="gif_data.src" alt="GIF-Image" v-on:load="rm_gif_preloader($event)">
										<div class="gif-preloader"></div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="timeline-pubbox__poll" v-if="active_media == 'poll'">
					<div class="pubbox-poll">
						<div class="pubbox-poll__header">
							<h4>
								<?php echo cl_translate("Create a new poll"); ?>
							</h4>
						</div>
						<div class="pubbox-poll__body">
							<div class="pubbox-poll__inputs">
								<div class="pubbox-poll__input-item" v-for="(v, k) in poll">
									<div class="form-group">
										<input class="form-control" v-model.trim="v.value" type="text" v-bind:placeholder="v.title + (k += 1)" maxlength="25">
										<small>{{v.value.length}}/25</small>
										<button type="button" class="delete-poll-option" v-on:click="remove_poll_option(k)">
											<?php echo cl_icon('close'); ?>
										</button>
									</div>	
								</div>
							</div>
						</div>
						<div class="pubbox-poll__footer">
							<button v-on:click="cancel_poll" type="button" class="btn btn-custom main-grey md" title="<?php echo cl_translate("Cancel poll"); ?>">
								<?php echo cl_translate("Cancel poll"); ?>
							</button>
							<button v-bind:disabled="poll.length >= 7" type="button" class="btn btn-custom main-inline md" v-on:click="poll_option">
								<?php echo cl_translate("Add option"); ?>
							</button>
						</div>
					</div>
				</div>
				<div class="timeline-pubbox__donate" v-if="donation.status">
					<div class="pubbox-donate">
						<div class="form-group">
							<label>
								<?php echo cl_translate("Amount you want to raise"); ?>
							</label>
							<input type="number" v-model.trim="donation.donate_amount" min="0" max="20000" placeholder="<?php echo cl_money("0.00"); ?>" class="form-control">
						</div>

						<button v-on:click="cancel_donation" type="button" class="btn btn-custom main-grey md">
							<?php echo cl_translate("Cancel fundraise"); ?>
						</button>
					</div>
				</div>
			</div>
			<div class="timeline-pubbox__footer">
				<div class="timeline-pubbox__footer-topline">

					<?php if ($cl["config"]["post_images_system"] == "on"): ?>
						<button type="button" class="ctrl-item" v-on:click="select_images" v-bind:disabled="image_ctrl != true">
							<?php echo cl_ficon("image"); ?>
						</button>
					<?php endif; ?>

					<?php if ($cl["config"]["post_videos_system"] == "on"): ?>
						<button type="button" class="ctrl-item" v-on:click="select_video" v-bind:disabled="video_ctrl != true">
							<?php echo cl_ficon("video"); ?>
						</button>
					<?php endif; ?>

					<?php if ($cl["config"]["post_audio_system"] == "on"): ?>
						<button type="button" class="ctrl-item" v-on:click="select_music" v-bind:disabled="music_ctrl != true">
							<?php echo cl_ficon('music'); ?>
						</button>
					<?php endif; ?>

					<?php if ($cl["config"]["post_documents_system"] == "on"): ?>
						<button type="button" class="ctrl-item" v-on:click="select_doc" v-bind:disabled="doc_ctrl != true">
							<?php echo cl_ficon('document'); ?>
						</button>
					<?php endif; ?>

					<?php if ($cl["config"]["post_record_system"] == "on"): ?>
						<!-- Audio record CTRLs -->
							<button type="button" v-if="audio_rec.is_recording == false" class="ctrl-item" v-on:click="record_audio_start" v-bind:disabled="audio_ctrl != true">
								<?php echo cl_ficon("mic_on"); ?>
							</button>
							<button type="button" v-if="audio_rec.is_recording == true" class="ctrl-item audio-recording" v-on:click="record_audio_stop" v-bind:disabled="audio_ctrl != true || audio_rec.record_time < 1">
								<?php echo cl_ficon("record_stop"); ?> <span class="record-timer">{{audio_rec.record_ftime}}</span>
							</button>
						<!-- Audio record CTRLs -->
					<?php endif; ?>
					
					<?php if ($cl["config"]["post_polls_system"] == "on"): ?>
						<button type="button" class="ctrl-item" v-on:click="create_poll" v-bind:disabled="poll_ctrl != true">
							<?php echo cl_ficon('poll'); ?>
						</button>
					<?php endif; ?>

					<?php if ($cl["config"]["post_gifs_system"] == "on"): ?>
						<button type="button" class="ctrl-item" v-on:click="select_gifs" v-bind:disabled="gif_ctrl != true">
							<?php echo cl_ficon('gif'); ?>
						</button>
					<?php endif; ?>

					<button v-if="emoticons_picker.status" v-on:click="emoticon_picker" type="button" class="ctrl-item">
						<?php echo cl_ficon("dismiss"); ?>
					</button>
					<button v-else v-on:click="emoticon_picker" type="button" class="ctrl-item h-mobile">
						<?php echo cl_ficon("emoji"); ?>
					</button>

					<?php if ($cl["config"]["donation_system_status"] == "on"): ?>
						<button v-on:click="create_donation" type="button" class="ctrl-item">
							<?php echo cl_ficon("donation"); ?>
						</button>
					<?php endif; ?>

					<button type="button" class="ctrl-item text-length ml-auto">
						<small v-show="text.length" v-bind:class="{'len-error': (text.length > settings.max_length) }">
							{{text.length}}/{{settings.max_length}}
						</small>
					</button>
				</div>
				<div class="timeline-pubbox__footer-botline">
					<div class="post-privacy" v-if="post_privacy">
						<button class="privacy-settings dropdown" type="button">
							<div class="dropdown-toggle" data-toggle="dropdown">
								<span class="d-inline-flex align-items-center flex-wn">
									<span class="flex-item icon" v-if="post_privacy == 'everyone'">
										<?php echo cl_ficon('earth'); ?>
									</span>
									<span class="flex-item icon" v-else-if="post_privacy == 'mentioned'">
										<?php echo cl_ficon('comment_mention'); ?>
									</span>
									<span class="flex-item icon" v-else>
										<?php echo cl_ficon('followers'); ?>
									</span>
									<span class="flex-item flex-grow-1 label">
										{{sdds.privacy[post_privacy]}}
									</span>
								</span>
							</div>
							<div class="dropdown-menu dropdown-icons">
								<a class="dropdown-item" href="javascript:void(0);" v-on:click="post_privacy = 'everyone'">
									<span class="flex-item dropdown-item-icon">
										<?php echo cl_ficon('earth'); ?>
									</span>
									<span class="flex-item">
										<?php echo cl_translate('Everyone can reply'); ?>
									</span>
									<span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo" v-if="post_privacy == 'everyone'">
										<?php echo cl_ficon('checkmark'); ?>
									</span>
								</a>
								<div class="dropdown-divider"></div>
								<a class="dropdown-item" href="javascript:void(0);" v-on:click="post_privacy = 'mentioned'">
									<span class="flex-item dropdown-item-icon">
										<?php echo cl_ficon('comment_mention'); ?>
									</span>
									<span class="flex-item">
										<?php echo cl_translate('Only mentioned people'); ?>
									</span>
									<span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo" v-if="post_privacy == 'mentioned'">
										<?php echo cl_ficon('checkmark'); ?>
									</span>
								</a>
								<div class="dropdown-divider"></div>
								<a class="dropdown-item" href="javascript:void(0);" v-on:click="post_privacy = 'followers'">
									<span class="flex-item dropdown-item-icon">
										<?php echo cl_ficon('followers'); ?>
									</span>
									<span class="flex-item">
										<?php echo cl_translate('Only my followers'); ?>
									</span>
									<span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo" v-if="post_privacy == 'followers'">
										<?php echo cl_ficon('checkmark'); ?>
									</span>
								</a>
							</div>
						</button>
					</div>
					<div class="post-publication">
						<button v-if="submitting" disabled="true" type="submit" class="btn-custom main-inline md">
							<?php echo cl_translate("Publishing"); ?>...
						</button>
						<button v-else v-bind:disabled="valid_form != true" type="submit" class="btn-custom main-inline md">
							<?php echo cl_translate("Publish"); ?>
						</button>
					</div>
				</div>
			</div>
			<div v-if="progress_bar_status" class="timeline-pubbox__progress">
				<div class="progress-line" v-bind:style="{'width': progress_bar_value + '%'}">{{progress_bar_value}}%</div>
			</div>
		</div>
</div>
		<input multiple="multiple" type="file" class="d-none" data-an="images-input" accept="image/*" v-on:change="upload_images($event)">
		<input type="file" class="d-none" data-an="video-input" accept="video/*" v-on:change="upload_video($event)">
		<input type="file" class="d-none" data-an="music-input" accept="audio/*" v-on:change="upload_music($event)">
		<input type="file" class="d-none" data-an="doc-input" v-on:change="upload_document($event)">
		<input type="hidden" class="d-none" value="<?php echo fetch_or_get($cl['csrf_token'],'none'); ?>" name="hash">
	</form>
</div>