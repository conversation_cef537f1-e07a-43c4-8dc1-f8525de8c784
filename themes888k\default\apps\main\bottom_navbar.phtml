<div class=" animate__animated animate__bounce mobile-bottom-navbar" data-app="mobile-navbar" id="navbar">
	<div class="mobile-bottom-navbar-inner">
		<button class="navbar-ctrl" onclick="SMColibri.toggleSB();">
			<span class="icon avatar">
						<img style="position:relative; border-radius: 20px !important;" src="<?php echo($me['avatar']); ?>" alt="Avatar" width="30px" height="30px">
					</span>
		</button>
		<button data-navitem="home" class="navbar-ctrl">
			<a href="<?php echo cl_link("/"); ?>">
			<?php echo cl_ficon('home'); ?>
		</a>
		</button>
		<button  class="navbar-ctrl"  data-toggle="modal" data-target="#add_new_post">
		
			<a data-target="#add_new_post">
			<?php echo cl_ficon('addd'); ?>
		</a>
		</button>
		<button data-navitem="notifications" class="navbar-ctrl <?php if($cl['pn'] == 'notifications') {echo('active');} ?>" data-anchor="<?php echo cl_link('notifications'); ?>">
			<?php echo cl_ficon('notifications'); ?>
			<span class="info-indicators" data-an="new-notifs"><?php echo fetch_or_get($me['new_notifs']); ?></span>
		</button>
		<button data-navitem="chat" class="navbar-ctrl <?php if($cl['pn'] == 'chat') {echo('active');} ?>" data-anchor="<?php echo cl_link("chats"); ?>">
			<?php echo cl_ficon('chat'); ?>
			<span class="info-indicators" data-an="new-messages"><?php echo fetch_or_get($me['new_messages']); ?></span>
		</button>
	</div>
</div>
<script>
var prevScrollpos = window.pageYOffset;
window.onscroll = function() {
var currentScrollPos = window.pageYOffset;
  if (prevScrollpos > currentScrollPos) {
    document.getElementById("navbar").style.top = "90%";
  } else {
    document.getElementById("navbar").style.top = "950px";
  }
  prevScrollpos = currentScrollPos;
}

</script>