<?php if (in_array($cl['thread_data']['post'], array("0", "2"))): ?>
<div class="timeline-posts-container">
			<div class="timeline-posts-ls">
				<?php echo cl_template('profile/includes/suspended_profile'); ?>
			</div>
		</div>
	<?php else: ?>
<div class="post-list-item" data-an="thread-data" style="border-bottom: 1px solid var(--cl-primary-border-color); padding-bottom: 15px; !important;">
	<?php if (not_empty($cl['thread_data']['post']['me_blocked'])): ?>
		<div class="post-placeholder">
			<div class="d-flex flex-wn align-items-center">
				<div class="flex-item">
					<div class="icon">
						<?php echo cl_ficon("block"); ?>
					</div>
				</div>
				<div class="felx-item">
					<p>
						<?php echo cl_translate("This user ({@uname@}) has blocked you from accessing their posts", array(
							"uname" => cl_html_el("a", $cl['thread_data']['post']['owner']['username'], array(
								"href" => $cl['thread_data']['post']['owner']['url'],
								"data-spa" => "true"
							))
						)); ?>
					</p>
				</div>
			</div>
		</div>
	<?php elseif (empty($cl['thread_data']['post']['can_see'])): ?>
		<div class="post-placeholder">
			<div class="d-flex flex-wn align-items-center">
				<div class="flex-item">
					<div class="icon">
						<?php echo cl_ficon("people_swap"); ?>
					</div>
				</div>
				<div class="felx-item">
					<p>
						<?php echo cl_translate("Only followers of this user ({@uname@}) can see their posts", array(
							"uname" => cl_html_el("a", $cl['thread_data']['post']['owner']['username'], array(
								"href" => $cl['thread_data']['post']['owner']['url'],
								"data-spa" => "true"
							))
						)); ?>
					</p>
				</div>
			</div>
		</div>
	<?php else: ?>
		<?php if (not_empty($cl['thread_data']['post']['is_repost'])): ?>
			<div class="post-list-item__header">
				<div class="publication-repost">
					<?php if (not_empty($cl['thread_data']['post']['is_reposter'])): ?>
						<a href="<?php echo $me['url']; ?>" data-spa="true">
							<?php echo cl_translate('You reposted'); ?>
						</a>
					<?php else: ?>
						<a href="<?php echo($cl['thread_data']['post']['reposter']['url']); ?>" data-spa="true">
							<?php echo cl_translate('{@uname@} reposted', array('uname' => $cl['thread_data']['post']['reposter']['name'])) ?>
						</a>
					<?php endif; ?>

					<span class="publication-repost__icon">
						<?php echo cl_ficon('repeat'); ?>
					</span>
				</div>
			</div>
		<?php endif; ?>

		<div class="post-list-item__content">
			<div class="post-data">
				<div class="post-data__content">	
					<?php if (not_empty($cl['thread_data']['post']['is_blocked'])): ?>
						<div class="post-data__content-hidden" data-softhidden-post="<?php echo($cl['thread_data']['post']['id']); ?>">
							<div class="soft-hidden-post">
								<div class="d-flex align-items-center flex-wn">
									<div class="flex-item flex-grow-1">
										<p><?php echo cl_translate('This is a message from the user you blocked'); ?></p>
									</div>
									<div class="flex-item">
										<button class="btn btn-custom main-outline sm" onclick="SMColibri.show_post(<?php echo($cl['thread_data']['post']['id']); ?>, 'blocked');">
											<?php echo cl_translate('View'); ?>
										</button>
									</div>	
								</div>
							</div>
						</div>
					<?php elseif(not_empty($cl['thread_data']['post']['is_reported'])): ?>
						<div class="post-data__content-hidden" data-softhidden-post="<?php echo($cl['thread_data']['post']['id']); ?>">
							<div class="soft-hidden-post">
								<div class="d-flex align-items-center flex-wn">
									<div class="flex-item flex-grow-1">
										<p>
											<?php echo cl_translate('This post is currently under review'); ?>	
										</p>
									</div>
									<div class="flex-item">
										<button class="btn btn-custom main-outline sm" onclick="SMColibri.show_post(<?php echo($cl['thread_data']['post']['id']); ?>, 'reported');">
											<?php echo cl_translate('View'); ?>
										</button>
									</div>	
								</div>
							</div>
						</div>
					<?php endif; ?>
				
					<div class="post-data__content-inner">
						<div class="post-data-layout">
							<div class="post-data-layout__publisher">
								<div class="post-avatar">
									<a class="block-link" href="<?php echo($cl['thread_data']['post']['owner']['url']); ?>" data-spa="true">
										<div class="avatar-holder">
											<img class="lozad" data-src="<?php echo($cl['thread_data']['post']['owner']['avatar']); ?>">
										</div> 
									</a>
								</div>
								<div class="post-username">
									<a href="<?php echo($cl['thread_data']['post']['owner']['url']); ?>" data-spa="true" data-uinfo-lbox="<?php echo($cl['thread_data']['post']['owner']['id']); ?>" data-toggle="popover" data-placement="bottom">
										<span class="user-name-holder">
											<span class="user-name-holder__name" style="font-size: 19px !important;">
												<?php echo($cl['thread_data']['post']['owner']['username']); ?>

											</span>
										<?php

										?>
										
											<?php if ($cl['thread_data']['post']['owner']['verified'] == '1'): ?>
												<span class="user-name-holder__badge" style="font-size: 13px !important; margin-top: 1px;">
													<?php echo cl_icon("verified_user_badge"); ?>
												</span> 
											<?php endif; ?>
											<?php if ($cl['thread_data']['post']['owner']['is_premium'] == '1'): ?>

<span style="margin-left: 5px; margin-top: -3px;">
		<img src="{%config theme_url%}/statics/img/vip.png" width="16" height="16">
</span>
<?php endif; ?>
										</span>
									</a><br>
									<span class="uname" style=" color:  var(--cl-uname-color) !important; font-size: 15px !important; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; max-width: 90px !important;"> 
									@<?php echo ($cl['thread_data']['post']['owner']['username']); ?></span>
								</div >
<div class="ctrls-item dropleft "  style="font-size: 15px !important; margin-top:5px; color:  var(--cl-secondary-text-color);" >
	<?php if (not_empty($cl['thread_data']['post']['edited'])): ?>
										<span title="<?php echo cl_translate("Edited"); ?>: <?php echo cl_date("h:i A - M d, Y", $cl['thread_data']['post']['edited']); ?>">
										
										<img src="/themes/default/statics/fd_icons/edit-ex2.svg" width="18" height="18"> 
										</span>
									<?php endif; ?>
									<?php echo($cl['thread_data']['post']['time']); ?>

									
								</div> &nbsp;&nbsp;
									<div class="ctrls-item dropleft"  >
									<div class=" ctrls-item__icon" data-toggle="dropdown">
									<svg fill="var(--cl-secondary-text-color)" width="20px" height="20px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">

<g data-name="Layer 2">

<g data-name="more-horizotnal">

<rect width="24" height="24" opacity="0"/>

<circle cx="12" cy="12" r="2"/>

<circle cx="19" cy="12" r="2"/>

<circle cx="5" cy="12" r="2"/>

</g>

</g>

</svg>
									</div>
									<div class="dropdown-menu dropdown-icons" style="border: solid 1px var(--cl-primary-border-color);
											box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;">
										<?php if (not_empty($cl['thread_data']['post']['is_owner'])): ?>
											<?php if ($cl['thread_data']['post']["type"] == "poll"): ?>
												<?php if ($cl['thread_data']['post']["poll_status"] == "active"): ?>
													<a onclick="SMColibri.stop_poll(<?php echo($cl['thread_data']['post']['id']); ?>);" class="dropdown-item" href="javascript:void(0);">
														<span class="flex-item dropdown-item-icon">
															<?php echo cl_ficon('poll'); ?>
														</span>
														<span class="flex-item">
															<?php echo cl_translate('Stop this poll'); ?>
														</span>
														<span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
															<?php echo cl_ficon('timer_off'); ?>
														</span>
													</a>
													<div class="dropdown-divider"></div>
												<?php endif; ?>
											<?php endif; ?>
										<?php endif; ?>

										<a class="dropdown-item" href="<?php echo($cl['thread_data']['post']['owner']['url']); ?>" data-spa="true">
											<span class="flex-item dropdown-item-icon">
												<?php echo cl_ficon('person'); ?>
											</span>
											<span class="flex-item">
												<?php echo($cl['thread_data']['post']['owner']['name']); ?>
											</span>
											<span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
												<?php echo cl_ficon('open'); ?>
											</span>
										</a>

										<?php if ($cl["config"]["post_video_download_system"] == "on" && $cl['thread_data']['post']['type'] == "video"): ?>
											<div class="dropdown-divider"></div>
											<a download="<?php echo cl_strf("%s-Video-Publication", $cl["config"]["name"]); ?>" class="dropdown-item" href="<?php echo cl_get_media(fetch_or_get($cl['thread_data']['post']['media'][0]['src'],'')); ?>">
												<span class="flex-item dropdown-item-icon">
													<?php echo cl_ficon('arrow_download'); ?>
												</span>
												<span class="flex-item">
													<?php echo cl_translate('Download video'); ?>
												</span>
												<span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
													<?php echo cl_ficon('video'); ?>
												</span>
											</a>
										<?php endif; ?>

										<?php if ($cl["config"]["post_audio_download_system"] == "on" && $cl['thread_data']['post']['type'] == "audio"): ?>
											<div class="dropdown-divider"></div>
											<a download="<?php echo cl_strf("%s-Audio-Publication", $cl["config"]["name"]); ?>" class="dropdown-item" href="<?php echo cl_get_media(fetch_or_get($cl['thread_data']['post']['media'][0]['src'],'')); ?>">
												<span class="flex-item dropdown-item-icon">
													<?php echo cl_ficon('arrow_download'); ?>
												</span>
												<span class="flex-item">
													<?php echo cl_translate('Download audio'); ?>
												</span>
												<span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
													<?php echo cl_ficon('music'); ?>
												</span>
											</a>
										<?php endif; ?>
										
										<?php if ($cl['thread_data']['post']['type'] == "document"): ?>
											<div class="dropdown-divider"></div>
											<a download="<?php echo $cl['thread_data']['post']['media'][0]['x']["filename"]; ?>" class="dropdown-item" href="<?php echo cl_get_media(fetch_or_get($cl['thread_data']['post']['media'][0]['src'],'')); ?>">
												<span class="flex-item dropdown-item-icon">
													<?php echo cl_ficon('arrow_download'); ?>
												</span>
												<span class="flex-item">
													<?php echo cl_translate('Download document'); ?>
												</span>
												<span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
													<?php echo cl_ficon('document'); ?>
												</span>
											</a>
										<?php endif; ?>

										<div class="dropdown-divider"></div>
										<?php if (not_empty($cl['thread_data']['post']['can_edit'])): ?>
											<a onclick="SMColibri.edit_post('<?php echo $cl['thread_data']['post']['id']; ?>');" class="dropdown-item" href="javascript:void(0);">
												<span class="flex-item dropdown-item-icon">
													<?php echo cl_ficon('note_edit'); ?>
												</span>
												<span class="flex-item">
													<?php echo cl_translate('Edit post'); ?>
												</span>
												<span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
													<?php echo cl_ficon('text'); ?>
												</span>
											</a>
											<div class="dropdown-divider"></div>
										<?php endif; ?>
											
										<?php if (not_empty($cl['thread_data']['post']['can_delete'])): ?>
											<a class="dropdown-item" href="javascript:void(0);">
												<span class="flex-item dropdown-item-icon">
													<?php echo cl_ficon('delete'); ?>
												</span>
												<span style="color: #f50000 !important;" class="flex-item" onclick="SMColibri.delete_post('<?php echo $cl['thread_data']['post']['id']; ?>');">
													<?php echo cl_translate('Delete'); ?>
												</span>
											</a>
											<div class="dropdown-divider"></div>
										<?php endif; ?>
										<a onclick="SMColibri.show_likes('<?php echo $cl['thread_data']['post']['id']; ?>');" class="dropdown-item" href="javascript:void(0);">
											<span class="flex-item dropdown-item-icon">
												<?php echo cl_ficon('thumb_like-drop'); ?>
											</span>
											<span class="flex-item ">
												<?php echo cl_translate('Show likes'); ?>
											</span>
											<span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
												<?php echo cl_ficon('users_list'); ?>
											</span>
										</a>
										<a class="dropdown-item" href="javascript:void(0);">
											<span class="flex-item dropdown-item-icon">
												<?php echo cl_ficon('bookmark'); ?>
											</span>
											<span class="flex-item" onclick="SMColibri.bookmark_post('<?php echo $cl['thread_data']['post']['id']; ?>', this);">
												<?php echo ((empty($cl['thread_data']['post']['has_saved'])) ? cl_translate('Bookmark') : cl_translate('Unbookmark')); ?>
											</span>
										</a>
										<a data-clipboard-text="<?php echo($cl['thread_data']['post']['url']); ?>" class="dropdown-item clip-board-copy" href="javascript:void(0);">
											<span class="flex-item dropdown-item-icon">
												<?php echo cl_ficon('copy'); ?>
											</span>
											<span class="flex-item">
												<?php echo cl_translate('Copy link'); ?>
											</span>
										</a>
										<?php if (empty($cl['thread_data']['post']['is_owner'])): ?>
											<div class="dropdown-divider"></div>
											<a onclick="SMColibri.report_post(<?php echo($cl['thread_data']['post']['id']); ?>);" class="dropdown-item" href="javascript:void(0);">
												<span class="flex-item dropdown-item-icon">
													<?php echo cl_ficon('flag'); ?>
												</span>
												<span class="flex-item">
													<?php echo cl_translate('Report post'); ?>
												</span>
												<span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
													<?php echo cl_ficon('warning'); ?>
												</span>
											</a>
										<?php endif; ?>
										<?php if (not_empty($cl['thread_data']['post']['is_owner'])): ?>
											<div class="dropdown-divider"></div>
											<a onclick="SMColibri.pin_profile_post(<?php echo($cl['thread_data']['post']['id']); ?>, this);" class="dropdown-item" href="javascript:void(0);">
												<span class="flex-item dropdown-item-icon">
													<?php echo cl_ficon('pin'); ?>
												</span>
												<span class="flex-item" data-itag="text">
													<?php if ($cl['thread_data']['post']['profile_pinned'] == "Y"): ?>
														<?php echo cl_translate('Unpin from my profile'); ?>
													<?php else: ?>
														<?php echo cl_translate('Pin to my profile'); ?>
													<?php endif; ?>
												</span>
												<span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
													<?php echo cl_ficon('note'); ?>
												</span>
											</a>
										<?php endif; ?>
										<div class="dropdown-divider"></div>
										<a onclick="SMColibri.share_post('<?php echo($cl['thread_data']['post']['url']); ?>','<?php echo urlencode($cl['thread_data']['post']['url']); ?>');" class="dropdown-item" href="javascript:void(0);">
											<span class="flex-item dropdown-item-icon">
												<?php echo cl_ficon('share-drop'); ?>
											</span>
											<span class="flex-item">
												<?php echo cl_translate('Share'); ?>
											</span>
										</a>
									</div>
								</div>
							</div>

							<div class="post-data-layout__content" style="margin-top: 5px !important;">
								<?php if ($cl['thread_data']['post']['is_donation_post'] == "Y"): ?>
									<?php if (not_empty($cl['thread_data']['post']["title"])): ?>
										<div class="publication-title">
											<?php echo($cl['thread_data']['post']['title']); ?>
										</div>
									<?php endif; ?>
								<?php endif; ?>

								<?php if (not_empty($cl['thread_data']['post']['text'])): ?>
									<?php 
										$cl['thread_data']['post']['text'] = cl_rn2br($cl['thread_data']['post']['text']);
										$cl['thread_data']['post']['text'] = cl_strip_brs($cl['thread_data']['post']['text']);
									?>
									<div class="publication-text" style="font-size: 18px !important; font-weight: 500;" data-post-text="<?php echo($cl['thread_data']['post']['id']); ?>">
										<?php echo($cl['thread_data']['post']['text']); ?>
									</div>
								<?php endif; ?>
								<?php if ($cl['thread_data']['post']["content_view"] == true): ?>
									<?php if ($cl['thread_data']['post']['type'] == 'image' && not_empty($cl['thread_data']['post']['media'])): ?>
										<?php if (count($cl['thread_data']['post']['media']) == 1): ?>
											<div class="lozad-media">
												<div class="publication-image issafe-<?php echo strtolower(fetch_or_get($cl['thread_data']['post']['media'][0]['is_safe'], 'Y')); ?>">
													<a href="<?php echo cl_get_media(fetch_or_get($cl['thread_data']['post']['media'][0]['src'],'')); ?>" class="fbox-media">
														<img class="lozad" data-src="<?php echo cl_get_media(fetch_or_get($cl['thread_data']['post']['media'][0]['src'],'')); ?>" alt="Picture">
													</a>
												</div>
											</div>
										<?php else: ?>
											<div class="publication-images-collage">
												<?php if (count($cl['thread_data']['post']['media']) == 2): ?>
													<?php echo cl_template("thread/includes/img_grid/c2_images"); ?>
												<?php elseif (count($cl['thread_data']['post']['media']) == 3): ?>
													<?php echo cl_template("thread/includes/img_grid/c3_images"); ?>
												<?php elseif (count($cl['thread_data']['post']['media']) == 4): ?>
													<?php echo cl_template("thread/includes/img_grid/c4_images"); ?>
												<?php elseif (count($cl['thread_data']['post']['media']) == 5): ?>
													<?php echo cl_template("thread/includes/img_grid/c5_images"); ?>
												<?php elseif (count($cl['thread_data']['post']['media']) == 6): ?>
													<?php echo cl_template("thread/includes/img_grid/c6_images"); ?>
												<?php elseif (count($cl['thread_data']['post']['media']) == 7): ?>
													<?php echo cl_template("thread/includes/img_grid/c7_images"); ?>
												<?php elseif (count($cl['thread_data']['post']['media']) == 8): ?>
													<?php echo cl_template("thread/includes/img_grid/c8_images"); ?>
												<?php elseif (count($cl['thread_data']['post']['media']) == 9): ?>
													<?php echo cl_template("thread/includes/img_grid/c9_images"); ?>
												<?php elseif (count($cl['thread_data']['post']['media']) == 10): ?>
													<?php echo cl_template("thread/includes/img_grid/c10_images"); ?>
												<?php endif; ?>
											</div>
										<?php endif; ?>
									<?php elseif($cl['thread_data']['post']['type'] == 'video' && not_empty($cl['thread_data']['post']['media'])): ?>
										<?php
											$video_src = cl_get_media(fetch_or_get($cl['thread_data']['post']['media'][0]['src'], ''));
											$video_hls = str_replace('.mp4', '.m3u8', $video_src);
											$video_id  = $cl['thread_data']['post']['id'];
											$poster    = cl_get_media(fetch_or_get($cl['thread_data']['post']['media'][0]['x']['poster_thumb'], ''));
											$ratio     = fetch_or_get($cl['thread_data']['post']['media'][0]['x']["ratio"], '16:9');

											// Try to get real video dimensions from the actual file
											$real_ratio = $ratio;
											$video_file_path = cl_full_path($cl['thread_data']['post']['media'][0]['src']);

											if (file_exists($video_file_path) && !empty($cl['thread_data']['post']['media'][0]['src'])) {
												try {
													if ($ratio === '8:6' || $ratio === '16:9' || empty($ratio)) {
														require_once(cl_full_path("core/libs/getID3/getid3/getid3.php"));
														$getID3 = new getID3;
														$file_info = $getID3->analyze($video_file_path);

														if (isset($file_info["video"]["resolution_x"]) && isset($file_info["video"]["resolution_y"])) {
															$width = intval($file_info["video"]["resolution_x"]);
															$height = intval($file_info["video"]["resolution_y"]);

															if ($width > 0 && $height > 0) {
																if (!function_exists('video_gcd')) {
																	function video_gcd($a, $b) {
																		return $b ? video_gcd($b, $a % $b) : $a;
																	}
																}
																$gcd = video_gcd($width, $height);
																$ratio_w = $width / $gcd;
																$ratio_h = $height / $gcd;
																$real_ratio = $ratio_w . ":" . $ratio_h;
															}
														}
													}
												} catch (Exception $e) {
													$real_ratio = $ratio;
												}
											}

											$ratio_parts = explode(':', $real_ratio);
											$width_ratio = floatval($ratio_parts[0]);
											$height_ratio = floatval($ratio_parts[1]);
											$aspect_ratio_decimal = ($height_ratio > 0) ? ($width_ratio / $height_ratio) : 0;
											$css_aspect_ratio = ($height_ratio > 0) ? ($width_ratio . '/' . $height_ratio) : '16/9';

											$container_class = '';
											if ($aspect_ratio_decimal < 0.9) {
												$container_class = 'video-container-vertical';
											} elseif ($aspect_ratio_decimal > 1.1) {
												$container_class = 'video-container-horizontal';
											} else {
												$container_class = 'video-container-square';
											}
										?>
										<div class="lozad-media" style="max-width: 98% !important; max-height: auto;">
											<div class="publication-video <?php echo $container_class; ?>" data-video-ratio="<?php echo($real_ratio); ?>">
												<div class="cl-plyr-video" style="aspect-ratio: <?php echo($css_aspect_ratio); ?>; width: 100%;">
													<video
														id="video-player-thread-<?php echo $video_id; ?>"
														class="plyr lazy-hls"
														preload="none"
														playsinline
														controls
														data-hls="<?php echo $video_hls; ?>"
														data-video-id="<?php echo $video_id; ?>"
														data-video-ratio="<?php echo $real_ratio; ?>"
														poster="<?php echo $poster; ?>"
														style="width: 100%; height: 100%; object-fit: contain;"
													></video>
												</div>
											</div>
										</div>
										<?php elseif($cl['thread_data']['post']['type'] == 'audio' && not_empty($cl['thread_data']['post']['media'])): ?>
										<div class="publication-audio">
											<div class="cl-plyr-audio" style="width: 80% !important; margin-bottom: 10px;">
												<audio controls preload="metadata" class="plyr">
											        <source src="<?php echo cl_get_media(fetch_or_get($cl['thread_data']['post']['media'][0]['src'],'')); ?>" type="audio/mpeg">
											        <source src="<?php echo cl_get_media(fetch_or_get($cl['thread_data']['post']['media'][0]['src'],'')); ?>" type="audio/wav">
											        <source src="<?php echo cl_get_media(fetch_or_get($cl['thread_data']['post']['media'][0]['src'],'')); ?>" type="audio/mp3">
											    </audio>
											</div>
										</div>
									<?php elseif($cl['thread_data']['post']['type'] == 'document' && not_empty($cl['thread_data']['post']['media'])): ?>
										<div class="publication-document">
											<a href="<?php echo cl_get_media($cl['thread_data']['post']['media'][0]['src']); ?>" target="_blank" class="document-file">
												<div class="document-file__icon">
													<?php echo cl_ficon("document"); ?>
												</div>
												<div class="document-file__body">
													<?php echo($cl['thread_data']['post']['media'][0]['x']["filename"]); ?>
												</div>
												<div class="document-file__type">
													<?php echo($cl['thread_data']['post']['media'][0]['x']["file_type"]); ?>-<?php echo cl_translate("FILE"); ?>
												</div>
											</a>
										</div>
									<?php elseif($cl['thread_data']['post']['type'] == 'gif' && not_empty($cl['thread_data']['post']['media'])): ?>
										<div class="lozad-media">
											<div class="publication-image">
 							<a href="<?php echo fetch_or_get($cl['thread_data']['post']['media'][0]['src'],''); ?>" class="fbox-media">
													<img class="lozad" data-src="<?php echo fetch_or_get($cl['thread_data']['post']['media'][0]['src'],''); ?>" alt="GIF-Image">
												</a>
											</div>
										</div>
									<?php elseif($cl['thread_data']['post']['type'] == 'poll' && not_empty($cl['thread_data']['post']['poll'])): ?>
										<div class="publication-poll" data-post-poll="<?php echo($cl['thread_data']['post']['id']); ?>" data-status="<?php echo($cl['thread_data']['post']['poll']['has_voted']); ?>" data-stopped="<?php echo($cl['thread_data']['post']['poll_status']); ?>">
											<div class="publication-poll__inner">
												<?php foreach ($cl['thread_data']['post']['poll']['options'] as $i => $poll_data): ?>
													<div class="publication-poll__option" onclick="SMColibri.vote_poll(<?php echo($cl['thread_data']['post']['id']); ?>, <?php echo($i); ?>);" data-poll-option="<?php echo($i); ?>">
														<div class="bar-icon">
															<?php echo cl_ficon("checkmark_circle"); ?>
														</div>
														<div class="bar-text">
															<p>
																<?php echo $poll_data["option"]; ?>
															</p>
														</div>
														<div class="bar-num">
															<b>
																<?php if (not_empty($cl['thread_data']['post']['poll']['has_voted'])): ?>
																	<?php echo $poll_data["percentage"]; ?>%
																<?php endif; ?>
															</b>
														</div>

														<?php if (not_empty($cl['thread_data']['post']['poll']['has_voted'])): ?>
															<span class="bar-slider" style="width: <?php echo $poll_data["percentage"]; ?>%;"></span>
														<?php else: ?>
															<span class="bar-slider"></span>
														<?php endif; ?>
													</div>
												<?php endforeach; ?>

												<div class="publication-poll__total-votes" style="text-transform: capitalize;">
													<?php echo cl_translate("people voted"); ?>
													<span data-an="total-poll-voted"  style="font-weight: bold;">
														<?php
															$total_votes = 0;
															$poll_data = json($cl['thread_data']['post']['poll_data']);

															foreach ($poll_data as $pod) {
																$total_votes = ($total_votes += $pod["votes"]);
															}

															echo  $total_votes;
														?>
													</span>

													

													<?php if ($cl['thread_data']['post']["poll_status"] == "stopped"): ?>
														- <?php echo cl_translate("Poll timed out"); ?>
													<?php endif; ?>
												</div>
											</div>
										</div>
									<?php elseif(not_empty($cl['thread_data']['post']['og_data'])): ?>
										<?php if (not_empty($cl['thread_data']['post']['og_data']['video_embed'])): ?>
											<div class="publication-og">
												<div class="publication-og__inner embeded-iframe">
													<div class="publication-og__image">
														<div class="lozad-media">
															<a href="<?php echo($cl['thread_data']['post']['og_data']['video_embed']); ?>" class="fbox-media">
																<img class="lozad" data-src="<?php echo($cl['thread_data']['post']['og_data']['image']); ?>" alt="Video">
																<div class="video-play-button">
																	<span class="video-play-button__arrow">
																		<?php echo cl_ficon("play"); ?>
																	</span>
																</div>
															</a>
														</div>
													</div>
													<div class="publication-og__description">
														<h5>
															<?php echo($cl['thread_data']['post']['og_data']['title']); ?>
														</h5>
														<?php if (not_empty($cl['thread_data']['post']['og_data']['description'])): ?>
															<p>
																<?php echo($cl['thread_data']['post']['og_data']['description']); ?>
															</p>
														<?php else: ?>
															<p>
																<a target="_blank" href="<?php echo($cl['thread_data']['post']['og_data']['video_embed']); ?>">
																	<?php echo($cl['thread_data']['post']['og_data']['video_embed']); ?>
																</a>
															</p>
														<?php endif; ?>
													</div>
												</div>
											</div>
										<?php else: ?>
											<div class="publication-og">
												<div class="publication-og__inner link" data-href="<?php echo($cl['thread_data']['post']['og_data']['url']); ?>">
													<?php if (not_empty($cl['thread_data']['post']['og_data']['image'])): ?>
														<div class="publication-og__image">
															<img src="<?php echo($cl['thread_data']['post']['og_data']['image']); ?>" alt="IMG">
														</div>
													<?php endif; ?>
													
													<div class="publication-og__description">
														<h5>
															<?php echo($cl['thread_data']['post']['og_data']['title']); ?>
														</h5>
														<p>
															<?php echo($cl['thread_data']['post']['og_data']['description']); ?>
														</p>
														<a href="<?php echo($cl['thread_data']['post']['og_data']['url']); ?>" target="_blank">
															<?php echo($cl['thread_data']['post']['og_data']['url']); ?>
														</a>
													</div>
												</div>
											</div>
										<?php endif; ?>
									<?php endif; ?>
								<?php else: ?>
									<div class="publication-paid">
										<div class="publication-paid__header">
											<div class="pubowner-avatar">
												<img class="lozad" data-src="<?php echo($cl['thread_data']['post']['owner']['avatar']); ?>">
											</div>
											<div class="pubowner-avatar">
												<img class="lozad" data-src="<?php echo cl_link("themes/default/statics/img/premium-avatar.png"); ?>">
											</div>
										</div>
										<div class="publication-paid__body">
											<h5>
												<?php echo cl_translate("Subscribe to Unlock"); ?>
											</h5>
											<p>
												<?php echo cl_translate("For {@price@} / Monthly", array("price" => cl_money($cl['thread_data']['post']['owner']['subscription_price']))); ?>
											</p>
										</div>
										<div class="publication-paid__footer">
											<button class="btn btn-custom main-inline lg btn-block" onclick="SMColibri.subscribe(<?php echo $cl['thread_data']['post']['user_id']; ?>);">
												<?php echo cl_translate("Subscribe now"); ?> / <?php echo cl_money($cl['thread_data']['post']['owner']['subscription_price']); ?>
											</button>
										</div>
									</div>
								<?php endif; ?>

								<?php if ($cl['thread_data']['post']["content_view"] == true): ?>
									<?php if ($cl['thread_data']['post']['is_donation_post'] == "Y"): ?>
										<div class="publication-funding">
											<div class="publication-funding__header">
												<?php echo cl_translate("{@raised@} raised of {@goal@} goal", array(
													"raised" => cl_html_el("span", cl_money($cl['thread_data']['post']["donation_raised"]), array("class" => "raised", "data-an" => "donation-raised")),
													"goal" => cl_money($cl['thread_data']['post']["donation_amount"])
												)); ?>
											</div>
											<div class="publication-funding__body">
												<div class="funding-progress">
													<div class="funding-progress__bar" data-an="donation-raised-percent" style="width: <?php echo $cl['thread_data']['post']["donation_raised_percent"] . "%;"; ?>;"></div>
												</div>
												<div class="funding-total">
													<span>
														<span data-an="donations-total"><?php echo($cl['thread_data']['post']["donations_total"]); ?></span> <?php echo cl_translate("donations"); ?>
													</span>
													<span>
														<span data-an="donations-left-amount">
															<?php
																if ($cl['thread_data']['post']["donation_raised"] >= $cl['thread_data']['post']["donation_amount"]) {
																	echo cl_money("0.00");
																}
																else{
																	echo cl_money($cl['thread_data']['post']["donation_amount"] - $cl['thread_data']['post']["donation_raised"]);
																}
															 ?>
														</span>

														<?php echo cl_translate("to go"); ?>
													</span>
												</div>
											</div>
											<?php if ($cl['thread_data']['post']["is_owner"] != true): ?>
												<div class="publication-funding__footer">
													<button onclick="SMColibri.donate_post(<?php echo($cl['thread_data']['post']["id"]); ?>)" class="btn target-url-btn btn-custom main-green lg btn-block">
														<?php echo cl_translate("Donate now"); ?>
													</button>
												</div>
											<?php else: ?>
												<div class="publication-funding__footer">
													<button onclick="SMColibri.donate_self();" class="btn target-url-btn btn-custom main-green lg btn-block">
														<?php echo cl_translate("Donate now"); ?>
													</button>
												</div>
											<?php endif; ?>
										</div>
									<?php endif; ?>
								<?php endif; ?>
							</div>

							<div class="post-data-layout__date" style="position:absolute; left: 90%; margin-top: 5px;">
								
								<?php if ($cl['thread_data']['post']['target'] == 'pub_reply' && not_empty($cl['thread_data']['post']['reply_to'])): ?>
									
								<?php else: ?>
									<?php if (not_empty($cl['thread_data']['post']['is_owner']) && $cl['thread_data']['post']['target'] == 'publication'): ?>
										<div class="publication-privacy">
											<button class="privacy-settings dropleft" type="button">
												<div class="dropdown-toggle" data-toggle="dropdown">
													<?php if ($cl['thread_data']['post']['priv_wcr'] == "everyone"): ?>
														<span class="d-inline-flex align-items-center flex-wn">
															<span class="flex-item icon">
																<?php echo cl_ficon('earth'); ?>
															</span>
															<span class="flex-item flex-grow-1 label">
																<?php echo cl_translate(''); ?>
															</span>
														</span>
													<?php elseif($cl['thread_data']['post']['priv_wcr'] == "mentioned"): ?>
														<span class="d-inline-flex align-items-center flex-wn">
															<span class="flex-item icon">
																<?php echo cl_ficon('comment_mention'); ?>
															</span>
															<span class="flex-item flex-grow-1 label">
																<?php echo cl_translate(''); ?>
															</span>
														</span>
													<?php else: ?>
														<span class="d-inline-flex align-items-center flex-wn">
															<span class="flex-item icon">
																<?php echo cl_ficon('followers'); ?>
															</span>
															<span class="flex-item flex-grow-1 label">
																<?php echo cl_translate(''); ?>
															</span>
														</span>
													<?php endif; ?>
												</div>
												<div class="dropdown-menu dropdown-icons">
													<a class="dropdown-item" href="javascript:void(0);" onclick="SMColibri.post_privacy('everyone', <?php echo $cl['thread_data']['post']['id']; ?>);">
														<span class="flex-item dropdown-item-icon">
															<?php echo cl_ficon('earth'); ?>
														</span>
														<span class="flex-item">
															<?php echo cl_translate('Everyone can reply'); ?>
														</span>
														<?php if ($cl['thread_data']['post']['priv_wcr'] == "everyone"): ?>
															<span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
																<?php echo cl_ficon('checkmark'); ?>
															</span>
														<?php endif; ?>
													</a>
													<div class="dropdown-divider"></div>
													<a class="dropdown-item" href="javascript:void(0);" onclick="SMColibri.post_privacy('mentioned', <?php echo $cl['thread_data']['post']['id']; ?>);">
														<span class="flex-item dropdown-item-icon">
															<?php echo cl_ficon('comment_mention'); ?>
														</span>
														<span class="flex-item">
															<?php echo cl_translate('Only mentioned people'); ?>
														</span>
														<?php if ($cl['thread_data']['post']['priv_wcr'] == "mentioned"): ?>
															<span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
																<?php echo cl_ficon('checkmark'); ?>
															</span>
														<?php endif; ?>
													</a>
													<div class="dropdown-divider"></div>
													<a class="dropdown-item" href="javascript:void(0);" onclick="SMColibri.post_privacy('followers', <?php echo $cl['thread_data']['post']['id']; ?>);">
														<span class="flex-item dropdown-item-icon">
															<?php echo cl_ficon('followers'); ?>
														</span>
														<span class="flex-item">
															<?php echo cl_translate('Only my followers'); ?>
														</span>
														<?php if ($cl['thread_data']['post']['priv_wcr'] == "followers"): ?>
															<span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
																<?php echo cl_ficon('checkmark'); ?>
															</span>
														<?php endif; ?>
													</a>
												</div>
											</button>
										</div>
									<?php endif; ?>
								<?php endif; ?>
							</div>
						
						<div class="post-data-layout__controls safari-only">
							
								<?php if (empty($cl['thread_data']['post']['has_liked'])): ?>

							

									<button class="ctrls-item" onclick="SMColibri.like_post('<?php echo $cl['thread_data']['post']['id']; ?>', this);">

										<span class="ctrls-item__icon">
											<section class="like"></section>
											<?php echo cl_ficon('thumb_like'); ?>
										</span>
										
									</button>
								<?php else: ?>
									<button class="ctrls-item liked" onclick="SMColibri.like_post('<?php echo $cl['thread_data']['post']['id']; ?>', this);">
										<span class="ctrls-item__icon">
											<?php echo cl_ficon('thumb_like'); ?>
										</span>
									
									</button>
								<?php endif; ?>
							
								<?php if (not_empty($cl['thread_data']['can_reply'])): ?>
									<button class="ctrls-item" onclick="SMColibri.pub_reply('<?php echo $cl['thread_data']['post']['id']; ?>');">
										<span class="ctrls-item__icon">
											<?php echo cl_ficon('comment'); ?>
										</span>
									
									</button>
								<?php else: ?>
									<button class="ctrls-item" disabled="true">
										<span class="ctrls-item__icon">
											<?php echo cl_ficon('comment'); ?>
										</span>
										
									</button>
								<?php endif; ?>
								<?php if (empty($cl['thread_data']['post']['has_reposted'])): ?>
									<button onclick="SMColibri.repost('<?php echo $cl['thread_data']['post']['id']; ?>', this);" class="ctrls-item" data-an="repost-ctrl">
										<span class="ctrls-item__icon">
											<?php echo cl_ficon('repeat'); ?>
										</span>
										<span class="num" data-an="reposts-count">
											<?php echo $cl['thread_data']['post']['reposts_count']; ?>
										</span>
									</button>
								<?php else: ?>
									<button onclick="SMColibri.repost('<?php echo $cl['thread_data']['post']['id']; ?>', this);" class="ctrls-item reposted" data-an="repost-ctrl">
										<span class="ctrls-item__icon">
											<?php echo cl_ficon('repeat'); ?>
										</span>
										<span class="num" data-an="reposts-count">
											<?php echo $cl['thread_data']['post']['reposts_count']; ?>
										</span>
									</button>
								<?php endif; ?>

								<button class="ctrls-item" onclick="SMColibri.share_post('<?php echo $cl['thread_data']['post']['url']; ?>','<?php echo urlencode($cl['thread_data']['post']['url']); ?>');">
									<span class="ctrls-item__icon">
										<?php echo cl_ficon('share'); ?>
									</span>
								</button>
						
							
							</div>
							<div class="ctrls-item" style="font-size: 14px; color: #999da1; margin-bottom: 5px; margin-left: 5px;">
										 <span class="num" data-an="likes-count">
											<?php echo $cl['thread_data']['post']['likes_count']; ?>
										</span> <a  style="color: #999da1 !important;" onclick="SMColibri.show_likes('<?php echo $cl['thread_data']['post']['id']; ?>');" href="javascript:void(0);"> <?php echo cl_translate("Likes"); ?></a> • <span class="num"><?php echo $cl['thread_data']['post']['replys_count']; ?></span>  <?php echo cl_translate("Comments"); ?>
									</div>
						</div>
					</div>	
				</div>
			</div>
		</div>
		
	<?php endif;?>
</div>

<?php if (not_empty($cl["moder_data"])): ?>
	<?php echo cl_template('cpanel/assets/edit_user_perms/scripts/app_master_script'); ?>
<?php endif; ?>
<?php endif;?>