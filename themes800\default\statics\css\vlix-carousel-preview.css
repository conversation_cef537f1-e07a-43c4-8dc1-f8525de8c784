/* VliX Carousel 5-Second Preview Styles - Theme Adaptive Design */

/* CSS Custom Properties for Theme Support */
:root {
    /* Light mode colors (fallback) */
    --vlix-background: #ffffff;
    --vlix-card-background: #ffffff;
    --vlix-text-color: #333333;
    --vlix-text-muted: #666666;
    --vlix-border-color: rgba(0, 0, 0, 0.1);
    --vlix-border-hover: rgba(0, 0, 0, 0.15);
    --vlix-shadow-color: rgba(0, 0, 0, 0.15);
    --vlix-shadow-hover: rgba(0, 0, 0, 0.2);
    --vlix-primary-color: #007bff;
    --vlix-primary-alpha: rgba(0, 123, 255, 0.5);
    --vlix-success-color: #28a745;
    --vlix-success-alpha: rgba(40, 167, 69, 0.3);
}

/* Dark mode colors */
@media (prefers-color-scheme: dark) {
    :root {
        --vlix-background: #1a1a1a;
        --vlix-card-background: #2d2d2d;
        --vlix-text-color: #ffffff;
        --vlix-text-muted: #cccccc;
        --vlix-border-color: rgba(255, 255, 255, 0.1);
        --vlix-border-hover: rgba(255, 255, 255, 0.15);
        --vlix-shadow-color: rgba(0, 0, 0, 0.3);
        --vlix-shadow-hover: rgba(0, 0, 0, 0.4);
        --vlix-primary-color: #0d6efd;
        --vlix-primary-alpha: rgba(13, 110, 253, 0.5);
        --vlix-success-color: #198754;
        --vlix-success-alpha: rgba(25, 135, 84, 0.3);
    }
}

/* Support for theme classes (if your site uses theme classes) */
.dark-mode {
    --vlix-background: #1a1a1a;
    --vlix-card-background: #2d2d2d;
    --vlix-text-color: #ffffff;
    --vlix-text-muted: #cccccc;
    --vlix-border-color: rgba(255, 255, 255, 0.1);
    --vlix-border-hover: rgba(255, 255, 255, 0.15);
    --vlix-shadow-color: rgba(0, 0, 0, 0.3);
    --vlix-shadow-hover: rgba(0, 0, 0, 0.4);
    --vlix-primary-color: #0d6efd;
    --vlix-primary-alpha: rgba(13, 110, 253, 0.5);
    --vlix-success-color: #198754;
    --vlix-success-alpha: rgba(25, 135, 84, 0.3);
}

.light-mode {
    --vlix-background: #ffffff;
    --vlix-card-background: #ffffff;
    --vlix-text-color: #333333;
    --vlix-text-muted: #666666;
    --vlix-border-color: rgba(0, 0, 0, 0.1);
    --vlix-border-hover: rgba(0, 0, 0, 0.15);
    --vlix-shadow-color: rgba(0, 0, 0, 0.15);
    --vlix-shadow-hover: rgba(0, 0, 0, 0.2);
    --vlix-primary-color: #007bff;
    --vlix-primary-alpha: rgba(0, 123, 255, 0.5);
    --vlix-success-color: #28a745;
    --vlix-success-alpha: rgba(40, 167, 69, 0.3);
}

/* Support for site's theme system using body[data-bg] */
body[data-bg="dark"] {
    --vlix-background: #1a1a1a;
    --vlix-card-background: #2d2d2d;
    --vlix-text-color: #ffffff;
    --vlix-text-muted: #cccccc;
    --vlix-border-color: rgba(255, 255, 255, 0.1);
    --vlix-border-hover: rgba(255, 255, 255, 0.15);
    --vlix-shadow-color: rgba(0, 0, 0, 0.3);
    --vlix-shadow-hover: rgba(0, 0, 0, 0.4);
    --vlix-primary-color: #0d6efd;
    --vlix-primary-alpha: rgba(13, 110, 253, 0.5);
    --vlix-success-color: #198754;
    --vlix-success-alpha: rgba(25, 135, 84, 0.3);
}

body[data-bg="light"] {
    --vlix-background: #ffffff;
    --vlix-card-background: #ffffff;
    --vlix-text-color: #333333;
    --vlix-text-muted: #666666;
    --vlix-border-color: rgba(0, 0, 0, 0.1);
    --vlix-border-hover: rgba(0, 0, 0, 0.15);
    --vlix-shadow-color: rgba(0, 0, 0, 0.15);
    --vlix-shadow-hover: rgba(0, 0, 0, 0.2);
    --vlix-primary-color: #007bff;
    --vlix-primary-alpha: rgba(0, 123, 255, 0.5);
    --vlix-success-color: #28a745;
    --vlix-success-alpha: rgba(40, 167, 69, 0.3);
}

/* Enhanced carousel wrapper with header - Theme Adaptive */
.vlix-carousel-wrapper {
    margin: 24px 0;
    padding: 0;
    background: transparent;
    border: none;
    box-shadow: none;
    overflow: visible; /* Allow videos to be fully visible */
    position: relative;
    width: 100%; /* Full width */
}

/* Enhanced header section - Theme Adaptive */
.vlix-carousel-header {
    padding: 24px 20px 16px;
    background: transparent;
    border-bottom: none;
    position: relative;
}

.vlix-carousel-title {
    color: var(--vlix-text-color);
    font-size: 18px;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.vlix-carousel-title::before {
    content: '🎬';
    font-size: 20px;
}

.vlix-carousel-subtitle {
    display: none !important;
}

/* Enhanced carousel container - Theme Adaptive */
.vlix-carousel-container {
    position: relative;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */
    padding: 0px 20px 20px; /* Add bottom padding for better spacing */
    background: transparent;
    gap: 12px; /* Good spacing between tall videos */
    display: flex;
    overflow-x: auto;
    width: 100%; /* Full width container */
}

.vlix-carousel-container::-webkit-scrollbar {
    display: none; /* Chrome/Safari */
}

/* Large square video slides - MUCH BIGGER - Theme Adaptive */
.vlix-video-slide {
    position: relative;
    flex-shrink: 0;
    width: 280px; /* Instagram-style width */
    height: 500px; /* Much taller like Instagram stories/reels */
    border-radius: 0; /* Square form - no rounded edges */
    overflow: hidden;
    background: #000;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: none;
    border: none;
}

.vlix-video-slide:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: none;
    border: none;
}

.vlix-video-slide:active {
    transform: translateY(-2px) scale(1.01);
}

/* Enhanced video element styling - Square Form */
.vlix-video-slide video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 0; /* Square form - no rounded edges */
    background: #000;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.vlix-video-slide video.vlix-preview {
    pointer-events: none; /* Prevent direct video controls interaction */
}

/* Subtle hover shimmer effect */
.vlix-video-slide::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.05),
        transparent
    );
    transition: left 0.5s ease;
    z-index: 2;
    pointer-events: none;
}

.vlix-video-slide:hover::after {
    left: 100%;
}

/* Playing indicator - Theme Adaptive */
.vlix-video-slide.playing {
    box-shadow: none;
    border: none;
}

.vlix-video-slide.playing::before {
    content: '';
    position: absolute;
    top: 8px;
    right: 8px;
    width: 8px;
    height: 8px;
    background: var(--vlix-success-color);
    border-radius: 50%;
    box-shadow:
        0 0 0 2px var(--vlix-success-alpha),
        0 0 6px var(--vlix-success-alpha);
    z-index: 3;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.1);
    }
}

/* Hide play icon overlay - we want pure video previews */
.vlix-play-icon {
    display: none !important;
}

/* Hide video overlay with user info - we want clean video previews */
.vlix-video-overlay {
    display: none !important;
}

/* Hide any user info elements */
.vlix-video-user,
.vlix-video-avatar,
.vlix-video-username {
    display: none !important;
}

/* Loading state */
.vlix-video-slide.loading video {
    opacity: 0.5;
}

.vlix-video-slide.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 3;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Error state */
.vlix-video-slide.error {
    background: #333;
}

.vlix-video-slide.error::before {
    content: '⚠️';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 24px;
    z-index: 3;
}

/* Premium loading state */
.vlix-video-slide.loading video {
    opacity: 0.3;
    filter: blur(1px);
}

.vlix-video-slide.loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 32px;
    height: 32px;
    border: 3px solid rgba(255, 255, 255, 0.2);
    border-top: 3px solid rgba(103, 126, 234, 0.8);
    border-radius: 50%;
    animation: premiumSpin 1.2s cubic-bezier(0.4, 0, 0.2, 1) infinite;
    z-index: 4;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

@keyframes premiumSpin {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) rotate(360deg);
        opacity: 1;
    }
}

/* Enhanced responsive adjustments - Square Form */
@media (max-width: 768px) {
    .vlix-carousel-wrapper {
        margin: 16px 0;
    }

    .vlix-carousel-header {
        padding: 20px 16px 12px;
    }

    .vlix-carousel-title {
        font-size: 16px;
    }

    .vlix-carousel-subtitle {
        display: none !important;
    }

    .vlix-carousel-container {
        padding: 0px 16px 16px; /* Add bottom padding on mobile */
        gap: 10px; /* Tight spacing for mobile */
        width: 100%; /* Full width on mobile */
    }

    .vlix-video-slide {
        width: 200px; /* Instagram mobile width */
        height: 360px; /* Instagram mobile height - very tall */
        border-radius: 0; /* Keep square form */
    }

    .vlix-video-slide video {
        border-radius: 0; /* Keep square form */
    }
}

/* Smooth scrolling for carousel */
.vlix-carousel-container {
    scroll-behavior: smooth;
}

/* Enhanced focus states for accessibility - Theme Adaptive */
.vlix-video-slide:focus {
    outline: none;
    box-shadow: none;
    transform: translateY(-2px) scale(1.01);
    border: none;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .vlix-video-slide {
        transition: none;
    }

    .vlix-video-slide:hover {
        transform: none;
        box-shadow: none;
        border: none;
    }

    .vlix-video-slide::after {
        display: none;
    }

    @keyframes premiumSpin {
        0% { transform: translate(-50%, -50%) rotate(0deg); }
        100% { transform: translate(-50%, -50%) rotate(0deg); }
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; transform: scale(1); }
        50% { opacity: 1; transform: scale(1); }
    }
}

/* Force transparent backgrounds for all theme modes */
body[data-bg="dark"] .vlix-carousel-wrapper,
body[data-bg="light"] .vlix-carousel-wrapper,
body .vlix-carousel-wrapper {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
}

body[data-bg="dark"] .vlix-carousel-header,
body[data-bg="light"] .vlix-carousel-header,
body .vlix-carousel-header {
    background: transparent !important;
    border-bottom: none !important;
}

body[data-bg="dark"] .vlix-carousel-container,
body[data-bg="light"] .vlix-carousel-container,
body .vlix-carousel-container {
    background: transparent !important;
}

body[data-bg="dark"] .vlix-inline-carousel,
body[data-bg="light"] .vlix-inline-carousel,
body .vlix-inline-carousel {
    background: transparent !important;
}

/* Remove all borders and shadows from video slides */
body[data-bg="dark"] .vlix-video-slide,
body[data-bg="light"] .vlix-video-slide,
body .vlix-video-slide,
.vlix-video-slide.playing,
.vlix-video-slide:hover,
.vlix-video-slide:focus,
.vlix-video-slide:active {
    border: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;
}

/* Ensure videos are fully square */
body[data-bg="dark"] .vlix-video-slide video,
body[data-bg="light"] .vlix-video-slide video,
body .vlix-video-slide video {
    border-radius: 0 !important;
    border: none !important;
}
