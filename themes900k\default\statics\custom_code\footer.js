<script src="https://cdn.jsdelivr.net/npm/hls.js@1.5.15"></script>
<script>
if ('scrollRestoration' in history) {
  history.scrollRestoration = 'manual';
}

const defaultVolume = 0.7;
let currentPlayingVideo = null;
let allObservedVideos = [];
let globalMute = true;
const manuallyPausedVideos = new WeakSet();
const lastPlaybackPositions = new WeakMap();
const isIOS = /iP(ad|hone|od)/i.test(navigator.userAgent);

// --- PER‑VIDEO resume tracker (instead of one global flag) ---
const resumedVideos = new WeakSet();
let resumeSrc = null;
let resumeTime = 0;
// ------------------------------------------------------------

// --- PATCH: Save last playing video and its time on unload ---
function saveScrollPos() {
  sessionStorage.setItem('scrollPos:' + location.pathname, window.pageYOffset);
}

function restoreScrollPos() {
  const pos = sessionStorage.getItem('scrollPos:' + location.pathname);
  if (pos !== null) window.scrollTo(0, parseInt(pos, 10));
}

window.addEventListener('beforeunload', () => {
  if (currentPlayingVideo) {
    sessionStorage.setItem(
      'resumeVideoSrc',
      currentPlayingVideo.currentSrc || currentPlayingVideo.src
    );
    sessionStorage.setItem(
      'resumeVideoTime',
      currentPlayingVideo.currentTime || 0
    );
  }
  saveScrollPos();
});

window.addEventListener('pagehide', (event) => {
  if (!event.persisted) return;
  saveScrollPos();
});
window.addEventListener('popstate', restoreScrollPos);

// --- NEW: Robust resume logic from second code ---
function tryResumeLastPlayedVideo() {
  if (!resumeSrc) return;
  // Only resume if not already resumed
  let resumed = false;
  allObservedVideos.forEach(video => {
    const src = video.currentSrc || video.src;
    if (!resumed && src === resumeSrc && !resumedVideos.has(video)) {
      lastPlaybackPositions.set(video, resumeTime);
      currentPlayingVideo = video;
      video.currentTime = resumeTime;
      video.scrollIntoView({ block: 'center' });
      // Try to play the video
      video.play().catch(() => {});
      resumedVideos.add(video);
      resumed = true;
    }
  });
}
// ------------------------------------------------------------

window.addEventListener('DOMContentLoaded', () => {
  restoreScrollPos();
  // load resume values
  resumeSrc = sessionStorage.getItem('resumeVideoSrc');
  resumeTime = parseFloat(sessionStorage.getItem('resumeVideoTime') || '0');

  initializeVideos();
  observeDOMChanges();

  // initial resume attempt for any already‑in‑DOM videos
  setTimeout(() => {
    tryResumeLastPlayedVideo();
    tryAutoplayMostVisibleVideo();
  }, 300);

  setTimeout(tryAutoplayMostVisibleVideo, 200);
});

function enableUnmuteOnInteractionIOS() {
  if (!isIOS) return;
  const unmuteAll = () => {
    globalMute = false;
    allObservedVideos.forEach(v => {
      v.muted = false;
      v.volume = defaultVolume;
      if (v._plyr) {
        v._plyr.muted = false;
        v._plyr.volume = defaultVolume;
      }
      if (!v.paused && v.readyState > 2) {
        v.pause();
        setTimeout(() => v.play().catch(() => {}), 50);
      }
    });
    window.removeEventListener('touchstart', unmuteAll);
    window.removeEventListener('scroll', unmuteAll);
  };
  window.addEventListener('touchstart', unmuteAll, { once: true });
  window.addEventListener('scroll', unmuteAll, { once: true });
}

function initializeVideos(container = document) {
  const vids = container.querySelectorAll('video.plyr:not([data-initialized])');
  vids.forEach(video => {
    video.dataset.initialized = 'true';
    video.setAttribute('preload', 'none');
    const player = new Plyr(video);
    video.muted = globalMute;
    player.volume = globalMute ? 0 : defaultVolume;

    // --- per‑video resume check for infinite‑scroll additions ---
    const src = video.currentSrc || video.src;
    if (resumeSrc && src === resumeSrc && !resumedVideos.has(video)) {
      lastPlaybackPositions.set(video, resumeTime);
      currentPlayingVideo = video;
      video.currentTime = resumeTime;
      resumedVideos.add(video);
    }
    // --------------------------------------------------------------

    video.addEventListener('play', () => {
      manuallyPausedVideos.delete(video);
      pauseAllVideos(true, video);
      currentPlayingVideo = video;
      video._hlsInstance?.startLoad();
    });

    video.addEventListener('pause', () => {
      if (!video._autoPaused && document.visibilityState === 'visible') {
        manuallyPausedVideos.add(video);
      }
      video._autoPaused = false;
      video._hlsInstance?.stopLoad();
    });

    video.addEventListener('timeupdate', () => {
      lastPlaybackPositions.set(video, video.currentTime);
    });

    video.addEventListener('waiting', () => {
      if (!manuallyPausedVideos.has(video)) video._hlsInstance?.startLoad();
    });

    video.addEventListener('canplay', () => {
      if (!manuallyPausedVideos.has(video) && video === getMostVisibleVideo()) {
        video.play().catch(() => {});
      }
    });

    video.addEventListener('error', () => {
      if (!manuallyPausedVideos.has(video)) {
        const lastT = lastPlaybackPositions.get(video) || 0;
        video.pause();
        video.currentTime = lastT;
        setTimeout(() => video.play().catch(() => {}), 800);
      }
    });

    player.on('volumechange', () => {
      if (!player.muted) {
        globalMute = false;
        allObservedVideos.forEach(v => {
          v.muted = false;
          v.volume = defaultVolume;
          if (!v.paused && v.readyState > 2) {
            v.pause();
            setTimeout(() => v.play().catch(() => {}), 50);
          }
        });
      }
    });

    video.addEventListener('loadedmetadata', () => {
      const w = video.videoWidth, h = video.videoHeight;
      video.parentNode?.style.setProperty('aspect-ratio', `${w}/${h}`);
    });

    const idx = allObservedVideos.length;
    if (video.dataset.hls) {
      let retryCount = 0, url = video.dataset.hls;
      video._attachHLS = (eager = false) => {
        if (video._hlsAttached || video._hlsAttaching) return;
        video._hlsAttaching = true;
        if (Hls.isSupported()) {
          const hls = new Hls({
            maxBufferLength: 2,
            maxBufferSize: 256 * 1024,
            maxBufferHole: 1.5,
            backBufferLength: 30,
            lowLatencyMode: false,
            autoStartLoad: eager,
            startFragPrefetch: false,
            progressive: true,
          });
          hls.loadSource(url);
          hls.attachMedia(video);
          hls.on(Hls.Events.MANIFEST_PARSED, () => {
            video._readyToPlay = true;
            video._hlsAttached = true;
            if (!eager) hls.stopLoad();
          });
          hls.on(Hls.Events.ERROR, (_, data) => {
            if (!data.fatal) return;
            if (data.type === Hls.ErrorTypes.NETWORK_ERROR && retryCount < 3) {
              retryCount++;
              hls.startLoad();
            } else if (data.type === Hls.ErrorTypes.MEDIA_ERROR) {
              hls.recoverMediaError();
            } else {
              hls.destroy();
              video._hlsAttached = false;
              setTimeout(() => video._attachHLS(true), 1000);
            }
          });
          video._hlsInstance = hls;
        } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
          video.src = url;
          video._readyToPlay = video._hlsAttached = true;
        }
      };
      if (idx < 5) video._attachHLS(true);
      else if (idx < 10) video._attachHLS(false);

      video.addEventListener('click', () => {
        if (!video._hlsAttached) video._attachHLS(true);
        pauseAllVideos(true, video);
        video.play().catch(() => {});
        currentPlayingVideo = video;
      });
    } else {
      video._readyToPlay = true;
      video.addEventListener('click', () => {
        pauseAllVideos(true, video);
        video.play().catch(() => {});
        currentPlayingVideo = video;
      });
    }

    allObservedVideos.push(video);
  });

  // --- PATCH: try resume after new videos initialized ---
  tryResumeLastPlayedVideo();

  setupPreloadObserver();
  setupVisibilityPause();
  enableUnmuteOnInteractionIOS();
  setTimeout(tryAutoplayMostVisibleVideo, 200);
  setupCenterObserver();
}

function setupPreloadObserver() {
  const obs = new IntersectionObserver(entries => {
    entries.forEach(e => {
      if (e.isIntersecting && e.target._attachHLS && !e.target._hlsAttached) {
        e.target._attachHLS(false);
      }
    });
  }, { rootMargin: '200px 0px', threshold: 0.01 });
  allObservedVideos.forEach(v => obs.observe(v));
}

function setupVisibilityPause() {
  const obs = new IntersectionObserver(entries => {
    entries.forEach(e => {
      const v = e.target;
      if (e.isIntersecting) {
        manuallyPausedVideos.delete(v);
        requestAnimationFrame(tryAutoplayMostVisibleVideo);
      } else if (!v.paused) {
        v._autoPaused = true;
        v.pause();
      }
    });
  }, { rootMargin: '0px', threshold: 0.2 });
  allObservedVideos.forEach(v => obs.observe(v));
}

function getMostVisibleVideo() {
  let max = 0, top = null;
  allObservedVideos.forEach(v => {
    const r = v.getBoundingClientRect(),
          h = r.height,
          vis = Math.max(0,
            Math.min(r.bottom, innerHeight) -
            Math.max(r.top, 0)
          ),
          ratio = vis / h;
    if (ratio > 0.4 && ratio > max && !manuallyPausedVideos.has(v)) {
      max = ratio;
      top = v;
    }
  });
  return top;
}

function tryAutoplayMostVisibleVideo() {
  if (document.hidden) return;
  const top = getMostVisibleVideo();
  if (!top || !top._readyToPlay) return;
  if (currentPlayingVideo === top && !top.paused) return;
  const t = lastPlaybackPositions.get(top);
  if (t != null) top.currentTime = t;
  pauseAllVideos(true, top);
  top.muted = globalMute;
  top.volume = globalMute ? 0 : defaultVolume;
  if (!top._pendingPlay) {
    top._pendingPlay = true;
    top.play()
      .then(() => { currentPlayingVideo = top; })
      .catch(() => {})
      .finally(() => { top._pendingPlay = false; });
  }
}

function observeDOMChanges() {
  const mo = new MutationObserver(muts => {
    muts.forEach(m => m.addedNodes.forEach(n => {
      if (n.nodeType !== 1) return;
      if (n.matches('video.plyr')) {
        globalMute = true;
        initializeVideos(n.parentNode);
        tryResumeLastPlayedVideo();
      } else if (n.querySelector('video.plyr')) {
        globalMute = true;
        initializeVideos(n);
        tryResumeLastPlayedVideo();
      }
    }));
  });
  mo.observe(document.body, { childList: true, subtree: true });
}

function pauseAllVideos(force = false, except = null) {
  allObservedVideos.forEach(v => {
    if (!v.paused && v !== except) {
      v._autoPaused = !force;
      v.pause();
    }
  });
}

function setupCenterObserver() {
  const centerObserver = new IntersectionObserver(entries => {
    entries.forEach(entry => {
      const v = entry.target;
      if (entry.isIntersecting) manuallyPausedVideos.delete(v);
      if (!v._readyToPlay || manuallyPausedVideos.has(v)) return;

      if (entry.isIntersecting) {
        const lastT = lastPlaybackPositions.get(v);
        if (lastT != null) v.currentTime = lastT;
        pauseAllVideos(true, v);
        v.muted = globalMute;
        v.volume = globalMute ? 0 : defaultVolume;
        if (!v._pendingPlay) {
          v._pendingPlay = true;
          v.play().catch(() =>{}).finally(() => { v._pendingPlay = false; });
        }
        currentPlayingVideo = v;
      } else if (v === currentPlayingVideo && !v._autoPaused) {
        v._autoPaused = true;
        v.pause();
      }
    });
  }, {
    root: null,
    rootMargin: '-50% 0px -50% 0px',
    threshold: 0
  });

  allObservedVideos.forEach(v => centerObserver.observe(v));

  const mo = new MutationObserver(muts => {
    muts.forEach(m => m.addedNodes.forEach(n => {
      if (n.nodeType !== 1) return;
      const vids = n.matches('video.plyr') ? [n] : Array.from(n.querySelectorAll('video.plyr'));
      vids.forEach(v => centerObserver.observe(v));
    }));
  });
  mo.observe(document.body, { childList: true, subtree: true });
}

function isElementVisible(el) {
  const rect = el.getBoundingClientRect();
  const visibleHeight = Math.max(0,
    Math.min(rect.bottom, window.innerHeight) -
    Math.max(rect.top, 0)
  );
  return (visibleHeight / rect.height) > 0.4;
}

function onReturnWithScrollFlip() {
  allObservedVideos.forEach(v => manuallyPausedVideos.delete(v));
  pauseAllVideos(true);
  window.scrollBy(0, 1);
  setTimeout(() => window.scrollBy(0, -1), 50);

  let target = (currentPlayingVideo && isElementVisible(currentPlayingVideo))
               ? currentPlayingVideo
               : getMostVisibleVideo();

  if (!target && resumeSrc) {
    target = allObservedVideos.find(v => (v.currentSrc || v.src) === resumeSrc);
    if (target && !resumedVideos.has(target)) {
      lastPlaybackPositions.set(target, resumeTime);
      target.currentTime = resumeTime;
      target.scrollIntoView({ block: 'center' });
      resumedVideos.add(target);
    }
  }
  if (!target) return;
  target._hlsInstance?.startLoad();
  const pos = lastPlaybackPositions.get(target) || 0;
  target.currentTime = pos;
  target.muted = globalMute;
  target.volume = globalMute ? 0 : defaultVolume;
  target.play().catch(() => {});
  currentPlayingVideo = target;
}

window.addEventListener('focus', onReturnWithScrollFlip);
window.addEventListener('pageshow', e => { if (e.persisted) onReturnWithScrollFlip(); });
document.addEventListener('visibilitychange', () => {
  if (document.visibilityState === 'visible') onReturnWithScrollFlip();
});
window.addEventListener('online', onReturnWithScrollFlip);

window.addEventListener('resize', () => {
  clearTimeout(window._resizeTimer);
  window._resizeTimer = setTimeout(tryAutoplayMostVisibleVideo, 50);
});

let scrollTimeout;
window.addEventListener('scroll', () => {
  allObservedVideos.forEach(v => manuallyPausedVideos.delete(v));
  clearTimeout(scrollTimeout);
  scrollTimeout = setTimeout(tryAutoplayMostVisibleVideo, 100);
});
</script>
