<div class="timeline-placeholder">
	<?php if ($cl['page_tab'] == "posts"): ?>
		<div class="icon">
			<div class="icon__bg">
				<?php echo cl_ficon('note'); ?>
			</div>
		</div>
	<?php elseif ($cl['page_tab'] == "media"): ?>
		<div class="icon">
			<div class="icon__bg">
				<?php echo cl_ficon('image'); ?>
			</div>
		</div>
	<?php else: ?>
		<div class="icon">
			<div class="icon__bg">
				<?php echo cl_ficon('thumb_like'); ?>
			</div>
		</div>
	<?php endif; ?>

	<div class="pl-message">
		<?php if ($cl['page_tab'] == 'posts'): ?>
			<h4>
				<?php echo cl_translate("No posts yet!"); ?>
			</h4>
			<?php if (not_empty($cl['prof_user']['owner'])): ?>
				<p>
					<?php echo cl_translate("There are no posts your profile yet. in order to post something click on the button (New post) in the lower left corner"); ?>
				</p>
			<?php else: ?>
				<p>
					<?php echo cl_translate("There are no posts in this profile yet. If you want to receive this user's publications on your timeline, click the (follow) button if you have not already done so"); ?>
				</p>
			<?php endif; ?>
		<?php elseif($cl['page_tab'] == 'media'): ?>
			<h4>
				<?php echo cl_translate("No media yet!"); ?>
			</h4>
			<?php if (not_empty($cl['prof_user']['owner'])): ?>
				<p>
					<?php echo cl_translate("There are no media your profile yet. in order to post Video, Images or GIFs click on the button (New post) in the lower left corner"); ?>
				</p>
			<?php else: ?>
				<p>
					<?php echo cl_translate("There are no media publications in this profile yet. If you want to receive this user's publications on your timeline, click the (follow) button if you have not already done so"); ?>
				</p>
			<?php endif; ?>
		<?php else: ?>
			<h4>
				<?php echo cl_translate("No likes yet!"); ?>
			</h4>
			<?php if (not_empty($cl['prof_user']['owner'])): ?>
				<p>
					<?php echo cl_translate("You don’t have any favorite posts yet. All posts that you like will be displayed here."); ?>
				</p>
			<?php else: ?>
				<p>
					<?php echo cl_translate("This user seems to have not liked any posts yet. All liked posts by this user will be displayed here."); ?>
				</p>
			<?php endif; ?>
		<?php endif; ?>
	</div>
</div>