.jq-toast-wrap,
.jq-toast-wrap * {
    margin: 0;
    padding: 0;
}
.jq-toast-wrap {
    display: block;
    position: fixed;
    min-width: 320px;
    pointer-events: none !important;
    letter-spacing: normal;
    z-index: 9000 !important;
}
.jq-toast-wrap.bottom-left {
    bottom: 20px;
    left: 20px;
}
.jq-toast-wrap.bottom-right {
    bottom: 20px;
    right: 40px;
}
.jq-toast-wrap.top-left {
    top: 20px;
    left: 20px;
}
.jq-toast-wrap.top-right {
    top: 20px;
    right: 40px;
}
.jq-toast-single {
    display: block;
    width: 100%;
    padding: 20px;
    margin: 0 0 5px;
    border-radius: 0px;
    font-size: 13px;
    font-family: arial, sans-serif;
    line-height: 17px;
    position: relative;
    pointer-events: all !important;
    background-color: #444;
    color: #fff;
    min-width: 300px;
    max-width: 420px;
}
.jq-toast-single h2 {
    font-family: arial, sans-serif;
    font-size: 16px;
    margin: 0 0 7px;
    background: 0 0;
    color: inherit;
    line-height: inherit;
    font-weight: 500;
    letter-spacing: -0.02em;
}
.jq-toast-single a {
    color: #eee;
    text-decoration: none;
    font-weight: 700;
    border-bottom: 1px solid #fff;
    padding-bottom: 3px;
    font-size: 12px;
}
.jq-toast-single ul {
    margin: 0 0 0 15px;
    background: 0 0;
    padding: 0;
}
.jq-toast-single ul li {
    list-style-type: disc !important;
    line-height: 17px;
    background: 0 0;
    margin: 0;
    padding: 0;
    letter-spacing: normal;
}
.close-jq-toast-single {
    position: absolute;
    top: 3px;
    right: 7px;
    font-size: 14px;
    cursor: pointer;
}
.jq-toast-loader {
    display: block;
    position: absolute;
    top: -2px;
    height: 2px;
    width: 0;
    left: 0;
    border-radius: 1px;
    background-color: #14171a !important;
}
.jq-toast-loaded {
    width: 100%;
}
.jq-has-icon {
    padding: 18px 22px 18px 56px;
    background-repeat: no-repeat;
    background-position: 18px center;
}
.jq-icon-info {
    background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' fill='none' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 1.999c5.524 0 10.002 4.478 10.002 10.002 0 5.523-4.478 10.001-10.002 10.001-5.524 0-10.002-4.478-10.002-10.001C1.998 6.477 6.476 1.999 12 1.999Zm0 1.5a8.502 8.502 0 1 0 0 17.003A8.502 8.502 0 0 0 12 3.5Zm-.004 7a.75.75 0 0 1 .744.648l.007.102.003 5.502a.75.75 0 0 1-1.493.102l-.007-.101-.003-5.502a.75.75 0 0 1 .75-.75ZM12 7.003a.999.999 0 1 1 0 1.997.999.999 0 0 1 0-1.997Z' fill='%23ffffff'/%3E%3C/svg%3E");
    background-color: rgba(20, 23, 26, 65%);
    color: #ffffff;
    background-color: rgba(20, 23, 26, 65%);
}
.jq-icon-warning {
    background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' fill='none' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10.91 2.782a2.25 2.25 0 0 1 2.975.74l.083.138 7.759 14.009a2.25 2.25 0 0 1-1.814 3.334l-.154.006H4.243a2.25 2.25 0 0 1-2.041-3.197l.072-.143L10.031 3.66a2.25 2.25 0 0 1 .878-.878Zm9.505 15.613-7.76-14.008a.75.75 0 0 0-1.254-.088l-.057.088-7.757 14.008a.75.75 0 0 0 .561 1.108l.095.006h15.516a.75.75 0 0 0 .696-1.028l-.04-.086-7.76-14.008 7.76 14.008ZM12 16.002a.999.999 0 1 1 0 1.997.999.999 0 0 1 0-1.997ZM11.995 8.5a.75.75 0 0 1 .744.647l.007.102.004 4.502a.75.75 0 0 1-1.494.103l-.006-.102-.004-4.502a.75.75 0 0 1 .75-.75Z' fill='%23ffffff'/%3E%3C/svg%3E");
    background-color: rgba(20, 23, 26, 65%);
    color: #ffffff;
    background-color: rgba(20, 23, 26, 65%);
}
.jq-icon-error {
    background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' fill='none' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12.45 2.15C14.992 4.057 17.587 5 20.25 5a.75.75 0 0 1 .75.75V11c0 5.001-2.958 8.676-8.725 10.948a.75.75 0 0 1-.55 0C5.958 19.676 3 16 3 11V5.75A.75.75 0 0 1 3.75 5c2.663 0 5.258-.943 7.8-2.85a.75.75 0 0 1 .9 0ZM12 3.678c-2.42 1.71-4.923 2.648-7.5 2.8V11c0 4.256 2.453 7.379 7.5 9.442 5.047-2.063 7.5-5.186 7.5-9.442V6.478c-2.577-.152-5.08-1.09-7.5-2.8ZM12 16a.75.75 0 1 1 0 1.5.75.75 0 0 1 0-1.5Zm0-8.996a.75.75 0 0 1 .743.648l.007.102v6.498a.75.75 0 0 1-1.493.102l-.007-.102V7.754a.75.75 0 0 1 .75-.75Z' fill='%23ffffff'/%3E%3C/svg%3E");
    background-color: rgba(20, 23, 26, 65%);
    color: #ffffff;
    background-color: rgba(20, 23, 26, 65%);
}
.jq-icon-success {
    background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' fill='none' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M4.53 12.97a.75.75 0 0 0-1.06 1.06l4.5 4.5a.75.75 0 0 0 1.06 0l11-11a.75.75 0 0 0-1.06-1.06L8.5 16.94l-3.97-3.97Z' fill='%23ffffff'/%3E%3C/svg%3E");
    color: #ffffff;
    background-color: rgba(20, 23, 26, 65%);
    background-color: rgba(20, 23, 26, 65%);
}
