<div class="cp-app-container" data-app="email-settings">
    <div class="current-page-name">
        <div class="lp">
            <h1>
                E-mail settings (SMTP)
            </h1>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        Manage mailing system settings
                    </h2>
                </div>
                <div class="body">
                    <form class="form" data-an="form1">
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group" data-an="smtp_username-input">
                                    <label>
                                        SMTP username
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config smtp_username%}" name="smtp_username" type="text" class="form-control" placeholder="Enter the username of your SMTP server">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group" data-an="smtp_host-input">
                                    <label>
                                        SMTP host
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config smtp_host%}" name="smtp_host" type="text" class="form-control" placeholder="Enter the host name of your SMTP server">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group" data-an="smtp_password-input">
                                    <label>
                                        SMTP password
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config smtp_password%}" name="smtp_password" type="passwords" class="form-control" placeholder="Enter the password of your SMTP server">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group" data-an="smtp_encryption-input">
                                    <label>
                                        SMTP encryption
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="smtp_encryption" class="form-control">
                                            <option value="ssl" <?php if($cl['config']['smtp_encryption'] == 'ssl') { echo('selected'); } ?>>SSL</option>
                                            <option value="tls" <?php if($cl['config']['smtp_encryption'] == 'tls') { echo('selected'); } ?>>TLS</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group" data-an="smtp_or_mail-input">
                                    <label>
                                        Mail server (SMTP or Mail)
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="smtp_or_mail" class="form-control">
                                            <option value="smtp" <?php if($cl['config']['smtp_or_mail'] == 'smtp') { echo('selected'); } ?>>SMTP</option>
                                            <option value="mail" <?php if($cl['config']['smtp_or_mail'] == 'mail') { echo('selected'); } ?>>MAIL</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group" data-an="smtp_port-input">
                                    <label>
                                        SMTP port
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config smtp_port%}" name="smtp_port" type="number" class="form-control" placeholder="Enter the port of your SMTP server">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12 no-mb">
                                <div class="form-group no-mb">
                                    <button data-an="submit-ctrl" type="submit" class="btn btn-primary">
                                        Save changes
                                    </button>
                                    <hr>
                                    <small class="info-feedback">
                                        <p>
                                            If you entered credentials from the SMTP server and still does not work sending emails, try combinations as shown below
                                        </p>
                                        <ol>
                                            <li>
                                                SMTP encryption: <b>SSL</b> with SMTP port: <b>465</b>
                                            </li>
                                            <li>
                                                SMTP encryption: <b>TLS</b> with SMTP port: <b>587</b>
                                            </li>
                                        </ol>
                                    </small>
                                </div>
                            </div>
                        </div>
                        <input type="hidden" class="d-none" value="<?php echo fetch_or_get($cl['csrf_token'],'none'); ?>" name="hash">
                    </form>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        Check the (SMTP) server
                    </h2>
                </div>
                <div class="body">
                    <div class="inline-alertbox-wrapper">
                        <div class="inline-alertbox info">
                            <div class="icon">
                                <?php echo cl_ficon("info"); ?>
                            </div>
                            <div class="alert-message">
                                <p>
                                    Please note that it is very important for you to verify that your SMTP server is working after saving your SMTP credentials.
                                    <br><br>

                                    Use this feature to send a test email to the email address below to make sure your SMTP is set up and working.
                                </p>
                            </div>
                        </div>
                    </div>
                    <form class="form" data-an="form2">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group" data-an="test_email-input">
                                    <label>
                                        E-Mail address
                                    </label>
                                    <div class="form-line">
                                        <input name="test_email" type="text" class="form-control" placeholder="Enter a email address">
                                    </div>
                                    <small class="info-feedback">
                                        <p>
                                            Enter the email address to which the test message will be sent
                                        </p>
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12 no-mb">
                                <div class="form-group no-mb">
                                    <button data-an="submit-ctrl" type="submit" class="btn btn-primary">
                                        Send test message
                                    </button>
                                </div>
                            </div>
                        </div>
                        <input type="hidden" class="d-none" value="<?php echo fetch_or_get($cl['csrf_token'],'none'); ?>" name="hash">
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php echo cl_template('cpanel/assets/email_settings/scripts/app_master_script'); ?>