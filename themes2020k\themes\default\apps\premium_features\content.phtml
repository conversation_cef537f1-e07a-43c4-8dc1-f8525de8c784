<div class="timeline-container">
	<div class="timeline-header" data-el="tl-header">
        <div class="timeline-header__botline">
    		<div class="lp">
    			<div class="nav-link-holder">
    				<a href="<?php echo cl_link("settings/premium"); ?>" data-spa="true">
    					<?php echo cl_translate("Premium features"); ?>
    				</a>
    			</div>
    		</div>
    		<div class="cp">
    			<a href="<?php echo cl_link("home"); ?>">
    				<img src="{%config site_logo%}" alt="Logo">
    			</a>
    		</div>
    		<div class="rp">
    			<div class="nav-link-holder">
    				<span class="go-back" onclick="SMColibri.go_back();">
    					<?php echo cl_ficon('arrow_back'); ?>
    				</span>
    			</div>
    		</div>
        </div>
	</div>
	<div class="profile-settings">
		<div class="profile-settings__content">
			<div class="settings-form">
				<form class="form" id="cl-premium-features-vue-app" v-on:submit="submit_form($event)">
					<div class="form-group no-mb">
                        <div class="form-toggle">
                            <div class="form-toggle__lp">
                                <?php echo cl_ficon("image_off"); ?>
                            </div>
                            <div class="form-toggle__mp">
                                <span class="form-toggle__title">
                                    <?php echo cl_translate("Hide native ads"); ?>
                                </span>
                                <span class="form-toggle__desc">
                                    <?php echo cl_translate("Remove native user ads from my timeline"); ?>
                                </span>
                            </div>
                            <div class="form-toggle__rp">
                                <div class="form-check">
                                    <input name="disable_native_ads" type="checkbox" class="form-check-input" <?php if(not_empty($me["premium_settings"]["disable_native_ads"])) {echo("checked");} ?>>
                                </div> 
                            </div>
                        </div>
                    </div>
                    <div class="form-group no-mb">
                        <div class="form-toggle">
                            <div class="form-toggle__lp">
                                <?php echo cl_ficon("eye_hide"); ?>
                            </div>
                            <div class="form-toggle__mp">
                                <span class="form-toggle__title">
                                    <?php echo cl_translate("Hide AdSense ads"); ?>
                                </span>
                                <span class="form-toggle__desc">
                                    <?php echo cl_translate("Remove AdSense (Google Ads, etc..) ads from my timeline and sidebars"); ?>
                                </span>
                            </div>
                            <div class="form-toggle__rp">
                                <div class="form-check">
                                    <input name="disable_adsense_ads" type="checkbox" class="form-check-input" <?php if(not_empty($me["premium_settings"]["disable_adsense_ads"])) {echo("checked");} ?>>
                                </div> 
                            </div>
                        </div>
                    </div>
                  
                    <div class="form-group padding-x-offset" v-if="unsuccessful_attempt">
                        <div class="invalid-main-feedback">
                            <?php echo cl_translate("Something went wrong while trying to save your changes, please try again later"); ?>
                        </div>
                    </div>
                    <div class="form-group padding-x-offset" v-else>
                        <div class="form-info-label">
                            <?php echo cl_translate("Check the box of ads that you do not want to see on your account (Disabling ads speeds up content loading)"); ?>
                        </div>
                    </div>
                    <input type="hidden" class="d-none" value="<?php echo fetch_or_get($cl['csrf_token'],'none'); ?>" name="hash">
                    <div class="form-group padding-x-offset no-mb">
                    	<button v-if="submitting != true" type="submit" class="btn btn-block btn-custom main-inline lg">
	                        <?php echo cl_translate("Save changes"); ?>
	                    </button>
	                    <button v-else disabled="true" type="button" class="btn btn-block btn-custom main-inline lg">
	                        <?php echo cl_translate("Please wait"); ?>
	                    </button>
                    </div>
				</form>
			</div>
		</div>
	</div>

	<script>
		"use strict";

		$(document).ready(function($) {
			Vue.use(window.vuelidate.default);
			var VueValids = window.validators;

			new Vue({
				el: "#cl-premium-features-vue-app",
				data: {
					submitting: false,
					unsuccessful_attempt: false
				},
				methods: {
					submit_form: function(_self = null) {
						_self.preventDefault();

						var _app_ = this;

						$(_self.target).ajaxSubmit({
							url: "<?php echo cl_link("native_api/settings/save_premium_features_settings"); ?>",
							type: 'POST',
							dataType: 'json',
							beforeSend: function() {
								_app_.submitting = true;
								_app_.unsuccessful_attempt = false;
							},
							success: function(data) {
								if (data.status == 200) {
									cl_bs_notify("<?php echo cl_translate("Your changes has been successfully saved!"); ?>", 5000, "success");
								}
								else{
									_app_.unsuccessful_attempt = true;
								}
							},
							complete: function() {
								_app_.submitting = false;
							}
						});
					}
				}
			});
		});
	</script>
</div>


