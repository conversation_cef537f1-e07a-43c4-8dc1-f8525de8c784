<div class="timeline-container">
	<div class="timeline-header" data-el="tl-header">
		<div class="timeline-header__botline">
			<div class="lp">
				<div class="nav-link-holder">
					<a href="<?php echo cl_link("settings/city"); ?>" data-spa="true">
						<?php echo cl_translate("Change city name"); ?>
					</a>
				</div>
			</div>
			<div class="cp">
				<a href="<?php echo cl_link("home"); ?>">
					<img src="{%config site_logo%}" alt="Logo">
				</a>
			</div>
			<div class="rp">
				<div class="nav-link-holder">
					<span class="go-back" onclick="SMColibri.go_back();">
						<?php echo cl_ficon('arrow_back'); ?>
					</span>
				</div>
			</div>
		</div>
	</div>
	<div class="profile-settings">
		<div class="profile-settings__content">
			<div class="settings-form">
				<form class="form" id="cl-city-settings-vue-app" v-on:submit="submit_form($event)">
	                <div class="form-group no-mb">
                        <label>
                            <?php echo cl_translate("Your city"); ?>
                        </label>
                        <input v-model.trim="$v.city.$model" type="text" name="city" class="form-control" placeholder="<?php echo cl_translate("Enter your city name"); ?>">
                        <div class="invalid-main-feedback" v-if="is_valid_city">
                            {{invalid_feedback_city}}
                        </div>
                    </div>
                    <div class="form-group" v-if="unsuccessful_attempt">
                        <div class="invalid-main-feedback">
                            <?php echo cl_translate("Something went wrong while trying to save your changes, please try again later"); ?>
                        </div>
                    </div>
                    <div class="form-group" v-else>
                        <div class="form-info-label">
                            <?php echo cl_translate("Please note that your city name will appear on your profile page. If you want to hide it, leave this field blank."); ?>
                        </div>
                    </div>
                    <input type="hidden" class="d-none" value="<?php echo fetch_or_get($cl['csrf_token'],'none'); ?>" name="hash">
                    <div class="form-group no-mb d-flex">
                    	<button v-if="submitting != true" v-bind:disabled="$v.$invalid == true" type="submit" class="ml-auto btn btn-custom main-inline lg">
	                        <?php echo cl_translate("Save changes"); ?>
	                    </button>
	                    <button v-else disabled="true" type="button" class="ml-auto btn btn-custom main-inline lg">
	                        <?php echo cl_translate("Please wait"); ?>
	                    </button>
                    </div>
	            </form>
			</div>
		</div>
	</div>

	<script>
		"use strict";

		$(document).ready(function($) {
			Vue.use(window.vuelidate.default);

			new Vue({
				el: "#cl-city-settings-vue-app",
				data: {
					submitting: false,
					unsuccessful_attempt: false,
					invalid_feedback_city: "",
					city: "<?php echo($me["city"]) ?>"
				},
				computed: {
					is_valid_city: function() {
						if (this.$v.city.$error) {
							this.invalid_feedback_city = "<?php echo cl_translate("The city name you entered is too long. The maximum length is 30 characters"); ?>";
							return true;
						}

						else {
							this.invalid_feedback_city = "";
							return false;
						}
					}
				},
				validations: {
					city: {
						max_length: window.validators.maxLength(30)
					}
				},
				methods: {
					submit_form: function(_self = null) {
						_self.preventDefault();

						var _app_ = this;

						$(_self.target).ajaxSubmit({
							url: "<?php echo cl_link("native_api/settings/save_profile_city"); ?>",
							type: 'POST',
							dataType: 'json',
							beforeSend: function() {
								_app_.submitting = true;
								_app_.unsuccessful_attempt = false;
							},
							success: function(data) {
								if (data.status == 200) {
									cl_bs_notify("<?php echo cl_translate("Your changes has been successfully saved!"); ?>", 5000, "success");
								}

								else {
									_app_.unsuccessful_attempt = true;
								}
							},
							complete: function() {
								_app_.submitting = false;
							}
						});
					}
				}
			});
		});
	</script>
</div>