<div class="cp-app-container" data-app="timezone">
    <div class="current-page-name">
        <div class="lp">
            <h1>
                TimeZone
            </h1>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        Manage TimeZone settings
                    </h2>
                </div>
                <div class="body">
                    <form class="form" data-an="form">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="form-group" data-an="timezone-input">
                                    <label>
                                        Timezone
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="timezone" class="form-control" data-size="10">
                                            <?php foreach ($cl["timezones"] as $tzn => $tzc): ?>
                                                <option value="<?php echo($tzn); ?>" <?php if($cl['config']['timezone'] == $tzn) { echo('selected'); } ?>>
                                                    <?php echo($tzn); ?> / <small><?php echo($tzc); ?></small>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group no-mb">
                       		<button data-an="submit-ctrl" type="submit" class="btn btn-primary">
                                Save changes
                            </button>
                        </div>
                        <input type="hidden" class="d-none" value="<?php echo fetch_or_get($cl['csrf_token'],'none'); ?>" name="hash">
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo cl_template('cpanel/assets/timezone/scripts/app_master_script'); ?>