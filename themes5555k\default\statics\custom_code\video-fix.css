/**
 * Full-width Video CSS Fix
 * Ensures all videos in timeline, thread, profile, and explore feeds
 * stretch full-width inside their containers.
 */

/* Video container core properties */
.publication-video,
div.publication-video,
body main.main-content-container div.main-content-container-inner div.timeline-container-wrapper div.timeline-container-inner div.timeline-container div.timeline-posts-container div.timeline-posts-ls div.post-list-item div.post-list-item__content div.post-data div.post-data__content div.post-data__content-inner div.post-data-layout div.post-data-layout__content div.publication-video {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    display: block !important;
    box-sizing: border-box !important;
}

/* Ensure video elements themselves also stretch */
.publication-video video,
div.publication-video video,
.tiktok-video,
.vlix-post-media video {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    display: block !important;
}

/* Ensure video player stretches to container */
body div.cl-plyr-video,
body div.cl-plyr-video div.plyr--video,
body div.cl-plyr-video div.plyr--video video.plyr {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    display: block !important;
}

/* Fix for cl-plyr-video object-fit to make it fill width */
body div.cl-plyr-video div.plyr--video video.plyr {
    object-fit: contain !important;
}

/* Maintain border radius and overflow properties */
.publication-video,
div.publication-video {
    border-radius: var(--cl-primary-border-radius);
    overflow: hidden;
}

/* Specific fixes for thread timeline */
body.cl-app-thread .thread-data .publication-video,
body.cl-app-thread .thread-data div.publication-video {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
}

/* Specific fixes for profile feed */
body.cl-app-profile .profile-feed .publication-video,
body.cl-app-profile .profile-feed div.publication-video {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
}

/* Specific fixes for explore page */
body.cl-app-explore .explore-feed .publication-video,
body.cl-app-explore .explore-feed div.publication-video {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
}

/* Video inner wrapper - if there's an inner wrapper forcing width */
.publication-video > div,
.publication-video > .inner-video-wrapper {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    display: block !important;
}

/* Responsive adjustments */
@media (max-width: 991px) {
    .publication-video,
    div.publication-video,
    .publication-video video,
    div.publication-video video {
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 !important;
    }
} 