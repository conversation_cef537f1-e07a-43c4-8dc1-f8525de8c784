<div class="timeline-container">
	<div class="timeline-header" data-el="tl-header">
		<div class="timeline-header__botline">
			<div class="lp">
				<div class="nav-link-holder">
					<a href="<?php echo cl_link("settings/phone"); ?>" data-spa="true">
						<?php echo cl_translate("Phone number"); ?>
					</a>
				</div>
			</div>
			<div class="cp">
				<a href="<?php echo cl_link("home"); ?>">
					<img src="{%config site_logo%}" alt="Logo">
				</a>
			</div>
			<div class="rp">
				<div class="nav-link-holder">
					<span class="go-back" onclick="SMColibri.go_back();">
						<?php echo cl_ficon('arrow_back'); ?>
					</span>
				</div>
			</div>
		</div>
	</div>
	<div class="profile-settings">
		<div class="profile-settings__content">
			<div class="settings-form">
				<form class="form" id="cl-phone-settings-vue-app" v-on:submit="submit_form($event)">
	                <div class="form-group no-mb">
                        <label>
                            <?php echo cl_translate("Phone"); ?>
                        </label>
                        <input v-model.trim="$v.phone.$model" type="tel" class="form-control" name="phone" placeholder="<?php echo cl_translate("Enter your phone number"); ?>">
                        <div class="invalid-main-feedback" v-if="is_valid_phone">
                            {{invalid_feedback_phone}}
                        </div>
                    </div>
                    <div class="form-group" v-if="unsuccessful_attempt">
                        <div class="invalid-main-feedback">
                            <?php echo cl_translate("Something went wrong while trying to save your changes, please try again later"); ?>
                        </div>
                    </div>
                    <div class="form-group" v-else>
                        <div class="form-info-label">
                            <?php echo cl_translate("Please note that after changing the phone number, the phone number that you use during authorization will be replaced by a new one"); ?>
                        </div>
                    </div>
                    <input type="hidden" class="d-none" value="<?php echo fetch_or_get($cl['csrf_token'],'none'); ?>" name="hash">
	                <div class="form-group no-mb d-flex">
	                	<button v-if="submitting != true" v-bind:disabled="$v.$invalid == true" type="submit" class="ml-auto btn btn-custom main-inline lg">
	                        <?php echo cl_translate("Save changes"); ?>
	                    </button>
	                    <button v-else disabled="true" type="button" class="ml-auto btn btn-custom main-inline lg">
	                        <?php echo cl_translate("Please wait"); ?>
	                    </button>
	                </div>
	            </form>
			</div>
		</div>
	</div>

	<script>
		"use strict";

		$(document).ready(function($) {
			Vue.use(window.vuelidate.default);
			var VueValids = window.validators;

			new Vue({
				el: "#cl-phone-settings-vue-app",
				data: {
					submitting: false,
					unsuccessful_attempt: false,
					invalid_feedback_phone: "",
					phone: "<?php echo($me["phone"]) ?>",
					doubling_phone: false,
					phone_server_error: false
				},
				computed: {
					is_valid_phone: function() {
						if (this.$v.phone.required == true && this.$v.phone.$error) {
							this.invalid_feedback_phone = "<?php echo cl_translate("The phone number you entered does not match the valid format."); ?>";
							return true;
						}

						else if(this.doubling_phone == true) {
							this.invalid_feedback_phone = "<?php echo cl_translate("This phone number is already taken"); ?>";
							return true;
						}

						else if(this.phone_server_error == true) {
							this.invalid_feedback_phone = "<?php echo cl_translate("An error occurred when trying to send an SMS with a confirmation code to your number. Please check that you entered the number correctly and try again, if the error persists, contact technical support"); ?>";
							return true;
						}

						else {
							this.invalid_feedback_phone = "";
							return false;
						}
					}
				},
				validations: {
					phone: {
						required: VueValids.required,
						numeric: VueValids.numeric,
						min_length: VueValids.minLength(7),
						max_length: VueValids.maxLength(15),
					}
				},
				methods: {
					submit_form: function(_self = null) {
						_self.preventDefault();
						var _app_ = this;

						$(_self.target).ajaxSubmit({
							url: "<?php echo cl_link("native_api/settings/save_profile_phone"); ?>",
							type: 'POST',
							dataType: 'json',
							beforeSend: function() {
								_app_.submitting = true;
								_app_.unsuccessful_attempt = false;
								_app_.doubling_phone = false;
								_app_.phone_server_error = false;
							},
							success: function(data) {
								if (data.status == 200) {
									setTimeout(function() {
										SMColibri.spa_load("<?php echo cl_link('confirm_phone'); ?>");
									}, 1000);
								}

								else if(data.status == 405) {
									_app_.phone_server_error = true;
								}

								else if(data.err_code == "doubling_phone") {
									_app_.doubling_phone = true;
								}

								else {
									_app_.unsuccessful_attempt = true;
								}
							},
							complete: function() {
								_app_.submitting = false;
							}
						});
					}
				}
			});
		});
	</script>
</div>