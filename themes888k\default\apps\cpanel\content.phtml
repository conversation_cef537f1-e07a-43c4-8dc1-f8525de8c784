﻿<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=Edge">
        <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
        <title>
            Control panel
        </title>
        <link rel="icon" href="{%config site_fav%}" type="image/png">
        <link rel="stylesheet" href="{%config theme_url%}/apps/cpanel/statics/css/vendor/bootstrapv5-0-2/bootstrapv5-0-2.css?v={%config update_date%}">
        <link href="{%config theme_url%}/statics/css/libs/animate.min.css?v={%config update_date%}" rel="stylesheet" />
        <link href="{%config theme_url%}/apps/cpanel/statics/css/style.css?v={%config update_date%}" rel="stylesheet">
        <link href="{%config theme_url%}/apps/cpanel/statics/css/libs/waitME.css?v={%config update_date%}" rel="stylesheet">
        
        <?php if (not_empty($cl["app_statics"])): ?>
            <?php if (not_empty($cl["app_statics"]["styles"])): ?>
                <?php foreach ($cl["app_statics"]["styles"] as $file_path): ?>
                    <link rel="stylesheet" href="<?php echo($file_path); ?>">
                <?php endforeach; ?>
            <?php endif; ?>
        <?php endif; ?>

        <script src="{%config theme_url%}/statics/js/libs/jquery-3.5.1.min.js"></script>
        <script src="{%config theme_url%}/apps/cpanel/statics/js/libs/bootstrap.bundle.min.js"></script>
        <script src="{%config theme_url%}/statics/js/libs/waitme/waitMe.min.js"></script>
        <script src="{%config theme_url%}/apps/cpanel/statics/js/admin.js"></script>
        <script src="{%config theme_url%}/statics/js/libs/clipboard.min.js"></script>

        <?php if ($cl["server_mode"] == 'dev'): ?>
            <script src="{%config theme_url%}/statics/js/libs/vuejs/vue-v2.6.11.dev.min.js?v={%config update_date%}"></script>
        <?php else: ?>
            <script src="{%config theme_url%}/statics/js/libs/vuejs/vue-v2.6.11.min.js?v={%config update_date%}"></script>
        <?php endif; ?>

        <?php if (not_empty($cl["app_statics"])): ?>
            <?php if (not_empty($cl["app_statics"]["scripts"])): ?>
                <?php foreach ($cl["app_statics"]["scripts"] as $file_path): ?>
                    <script src="<?php echo($file_path); ?>"></script>
                <?php endforeach; ?>
            <?php endif; ?>
        <?php endif; ?>
    </head>
    <body>

        <a href="https://freekassa.ru" target="_blank" rel="noopener noreferrer">
  <img src="https://cdn.freekassa.ru/banners/big-dark-1.png" title="Прием платежей на сайте">
</a>
        <?php echo cl_template('cpanel/assets/main/sidebar'); ?>

        <section class="content">
            <div class="main-content-block-ctrl">
                <button class="btn" onclick="SMC_CPanel.menu_toggle();">
                    <?php echo cl_ikon("ellypsis"); ?>
                </button>
            </div>
            <div class="main-content-block-body">
                <?php echo($cl['http_res']); ?>
            </div>
        </section>

        <input id="csrf-token" type="hidden" class="hidden d-none" value="<?php echo fetch_or_get($cl['csrf_token'],'none'); ?>">

        <?php echo cl_template('cpanel/assets/main/scripts/app_master_script'); ?>

        <div data-app="black-hole"></div>
    </body>
</html>
