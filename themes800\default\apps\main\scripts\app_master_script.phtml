<script>
	"use strict";

	(function(window) {
		function _SMColibri() {
			var data = {
				url: "{%config url%}"
			};

			var _smc = Object({
				curr_pn: "<?php echo fetch_or_get($cl["pn"], "none"); ?>",
				curr_url: "<?php echo fetch_or_get($cl["curr_url"], $cl["config"]["url"]); ?>",
				PS: Object({}),
				vue: Object({}),
				is_logged_user: '<?php echo (($cl["is_logged"] == true) ? 1 : 0); ?>',
				back_url: data.url,
				timeout: false,
				msb_upinterval: "<?php echo fetch_or_get($cl["config"]["page_update_interval"], "30"); ?>",
				userlbox: {
					interv: 0,
					status: false,
					lifetm: 1000,
					curr_id: false,
					curr_el: false,
					pre_delay: false
				}
			});

			if (_smc.curr_pn != "conversation") {
				if ($(window).width() > 1024) {
					_smc.lsb = new StickySidebar('[data-app="left-sidebar"]', {
						topSpacing: 0,
						bottomSpacing: 0
					});

					_smc.rsb = new StickySidebar('[data-app="right-sidebar"]', {
						topSpacing: 0,
						bottomSpacing: 0
					});
				}
			}

			_smc.toggleSB = function(a = false) {
				var left_sidebar = $('[data-app="left-sidebar"]');

				if (a) {
					if (a == "show") {
						left_sidebar.addClass('show');

						$('body').addClass("mobile-sb-open").append($('<div>', {
							'class': 'sb-open-overlay',
							'data-app': 'lsb-back-drop'
						}));
					}
					else{
						left_sidebar.removeClass('show').promise().done(function() {
							$('body').removeClass("mobile-sb-open").find('div[data-app="lsb-back-drop"]').remove();
						});
					}
				}

				else{
					if (left_sidebar.hasClass("show")) {
						left_sidebar.removeClass('show').promise().done(function() {
							$('body').removeClass("mobile-sb-open").find('div[data-app="lsb-back-drop"]').remove();
						});
					}
					else{
						left_sidebar.addClass('show');

						$('body').addClass("mobile-sb-open").append($('<div>', {
							'class': 'sb-open-overlay',
							'data-app': 'lsb-back-drop'
						}));
					}
				}
			}

			_smc.toggleSBMenu = function(e = false) {
				var left_sidebar = $('[data-app="left-sidebar"]');

				left_sidebar.find("div.sidebar__nav-group_collapse").slideToggle(100);
				

				if (left_sidebar.find("div.sidebar__nav-group_collapse").hasClass("open")) {
					$(e).find("a").text("<?php echo cl_translate('More'); ?>");
					left_sidebar.find("div.sidebar__nav-group_collapse").removeClass("open");
				}
				else{
					$(e).find("a").text("<?php echo cl_translate('Less'); ?>");
					left_sidebar.find("div.sidebar__nav-group_collapse").addClass("open");
				}
			}

			_smc.init = function() {
				_smc.fix_sidebars();
				_smc.update_msb_indicators();
				_smc.update_ilb();	

				window.history.replaceState({state: "new", back_url: window.location.href}, "", window.location.href);
			}

			_smc.fix_sidebars = function() {
				if (_smc.curr_pn != "chat") {
					if ($(window).width() > 1024) {
						if (_smc.lsb) {
							_smc.lsb.updateSticky();
						}
						
						if (_smc.rsb) {
							_smc.rsb.updateSticky();
						}
					}
				}
			}
			
			_smc.get_cookie = function(name = "") {
			    var matches = document.cookie.match(new RegExp(
			      "(?:^|; )" + name.replace(/([\.$?*|{}\(\)\[\]\\\/\+^])/g, '\\$1') + "=([^;]*)"
			    ));

			    return (matches ? decodeURIComponent(matches[1]) : undefined);
			}

			_smc.is_logged = function(){
				if (_smc.is_logged_user == '1') {
					return true;
				}

				return false;
			}

			_smc.user_lbox = function(id = false, el = false) {
				if ($.isNumeric(id)) {
					if(cl_empty(_smc.userlbox.curr_id) != true && _smc.userlbox.curr_id == id) {
						return false;
					}

					else{

						if (_smc.userlbox.pre_delay !== false) {
							clearTimeout(_smc.userlbox.pre_delay);

							_smc.userlbox.pre_delay = false;
						}

						_smc.userlbox.pre_delay = setTimeout(function() {
							$.ajax({
								url: '<?php echo cl_link("native_api/main/user_lbox"); ?>',
								type: 'GET',
								dataType: 'json',
								data: {id: id},
								beforeSend: function() {
									_smc.user_lbox_rm();

									_smc.userlbox.curr_id = id;
									_smc.userlbox.curr_el = el;
								}
							}).done(function(data) {
								if (data.status == 200) {
									if (_smc.userlbox.curr_id) {
										_smc.userlbox.status = true;
										_smc.userlbox.lifetm = 1000;

										el.popover({
											html: true,
											content: data.html,
											placement: "auto"
										});

										el.popover("show");
									}
								}
								else {
									_smc.errorMSG();
								}
							});
						}, 1500);
					}
				}
			}

			_smc.display_settings = function(a = "show") {
				$('[data-app="display-settings-app"]').modal(a);
			}

			_smc.user_lbox_rm = function() {

				if (_smc.userlbox.curr_el) {
					_smc.userlbox.curr_el.popover("dispose");
				}

				else {
					if ($("[data-app='user-info-lbox']").length) {
						$("div.popover").each(function(index, el) {
							$(el).popover("dispose");
						});
					}
				}
				
				_smc.userlbox.curr_id = false;
				_smc.userlbox.status  = false;
				_smc.userlbox.curr_el = false;
			}

			_smc.max_upload = function(size = 0) {
				var max_upload_size = "{%config max_upload_size%}";
				
				if (size > max_upload_size) {
					cl_bs_notify("<?php echo cl_translate('The file you selected ({0}) exceeds the maximum file size limit ({1})'); ?>".format(cl_format_bytes(size), cl_format_bytes(max_upload_size)), 5000, "error");
					
					return false;
				}

				else {
					return true;
				}
			}

			_smc.post_privacy = function(priv = "everyone", id = false) {
				if ($.isNumeric(id)) {
					$.ajax({
						url: '<?php echo cl_link("native_api/main/post_privacy"); ?>',
						type: 'POST',
						dataType: 'json',
						data: {id: id, priv: priv},
						beforeSend: function() {
							_smc.progress_bar("show");
						}
					}).done(function(data) {
						if (data.status == 200) {
							_smc.spa_reload();
						}
						else {
							_smc.errorMSG();
						}
					}).always(function() {
						setTimeout(function() {
							_smc.progress_bar("end");
						}, 1500);
					});
				}

				else {
					return false;
				}
			}

			_smc.subscribe = function(profile_id = false) {
				if (_smc.is_logged()) {
					if ($.isNumeric(profile_id) && profile_id) {
						$.ajax({
							url: '<?php echo cl_link("native_api/subscription/get_form"); ?>',
							type: 'GET',
							dataType: 'json',
							data: {profile_id: profile_id},
						}).done(function(data) {
							if (data.status == 200) {
								$('div[data-app="black-hole"]').append($(data.html)).find('div[data-app="subscription-app"]').modal("show");
							}
							else {
								_smc.errorMSG();
							}
						});
					}
				}
				else{
					_smc.req_auth();
				}
			}

			_smc.confirm_action = function(data = {}) {
				var modal    = "<div class='modal fadeIn confirm-actions-modal vh-center' tabindex='-1' data-onclose='remove' role='dialog' aria-hidden='true' data-keyboard='false' data-backdrop='static'><div class='modal-dialog modal-md' role='document'><div class='modal-content'><div class='modal-body'><h4>{0}</h4><p>{1}</p></div><div class='modal-footer'><button id='confirm-actions-confirm' type='button' class='btn btn-custom main-inline md btn-block'>{3}</button><button id='confirm-actions-cancel' type='button' class='btn btn-custom main-gray md btn-block'>{2}</button></div></div></div></div>";
				var title    = data['title'];
				var message  = data['message'];
				var btn_1    = data['btn_1'];
				var btn_2    = data['btn_2'];
				var modal    = modal.format(title,message,btn_1,btn_2);
				var deferred = new $.Deferred();

				_smc.user_lbox_rm();

				$(document).on('click', '#confirm-actions-confirm', function(event) {
					$(this).attr("disabled", true).text("<?php echo cl_translate('Please wait'); ?>");
					deferred.resolve();
				});

				$(document).on('click', '#confirm-actions-cancel', function(event) {
					deferred.reject();
				});

				$('div[data-app="black-hole"]').append($(modal)).find('div.confirm-actions-modal').modal('show');

				return deferred.promise();
			}

			_smc.req_auth = function() {
				var modal = "<div class='modal fadeIn info-popup-modal' tabindex='-1' data-onclose='remove' role='dialog' aria-hidden='true' data-keyboard='false' data-backdrop='static'><div class='modal-dialog modal-md' role='document'><div class='modal-content'><div class='modal-body'><h4><?php echo cl_translate('This action requires authorization!'); ?></h4><p><?php echo cl_translate('Please log in to your account in order to perform this action, or register if you do not already have an account on -{@site_name@}', array('site_name'=> cl_html_el('b', $cl['config']['name']))); ?></p></div><div class='modal-footer'><button data-dismiss='modal' type='button' class='btn btn-custom main-inline lg' data-href='<?php echo cl_link('guest'); ?>'><?php echo cl_translate('Login now'); ?></button> <button data-dismiss='modal' type='button' class='btn btn-custom main-gray lg'><?php echo cl_translate('Cancel'); ?></button></div></div></div></div>";

				$('div[data-app="black-hole"]').append($(modal)).find('div.info-popup-modal').modal('show');
			}

			_smc.info = function(title = "", body = "") {
				var modal = "<div class='modal fadeIn info-popup-modal' tabindex='-1' data-onclose='remove' role='dialog' aria-hidden='true' data-keyboard='false' data-backdrop='static'><div class='modal-dialog modal-md' role='document'><div class='modal-content'><div class='modal-body'><h4>{0}</h4><p>{1}</p></div><div class='modal-footer'><button data-dismiss='modal' type='button' class='btn btn-block btn-custom main-inline md btn-block'><?php echo cl_translate('Okey!'); ?></button></div></div></div></div>";
				var modal = modal.format(title, body);
				
				$('div[data-app="black-hole"]').append($(modal)).find('div.info-popup-modal').modal('show');
			}

			_smc.vote_poll = function(id = false, index = false) {
				if (_smc.is_logged()) {
					if ($.isNumeric(id) && $.isNumeric(index)) {
						var post_poll = $('[data-post-poll="{0}"]'.format(id));

						if (post_poll.length && post_poll.data("status") != 1 && post_poll.data("stopped") != "stopped") {
							post_poll.data("status", 1);

							$.ajax({
								url: '<?php echo cl_link("native_api/main/vote_poll"); ?>',
								type: 'POST',
								dataType: 'json',
								data: {
									id: id,
									option: index
								}
							}).done(function(data) {
								if (data.status == 200) {
									for (var i = 0; i < data.poll.options.length; i++) {
										var poll_option = post_poll.find('[data-poll-option="{0}"]'.format(i));
										var data_option = data.poll.options[i];

										if (poll_option && poll_option.length) {
											if (data_option.active != undefined) {
												poll_option.addClass("active");
											}

											poll_option.find('b').text("{0}%".format(data_option.percentage));
											poll_option.find('span').css("width", "{0}%".format(data_option.percentage));
										}
									}

									var total_poll_votes = Number(post_poll.find('[data-an="total-poll-voted"]').text());
									post_poll.find('[data-an="total-poll-voted"]').text(total_poll_votes += 1);
								}
								else {
									_smc.errorMSG();
								}
							});
						}
						else {
							return false;
						}
					}
				}

				else {
					_smc.req_auth();
				}
			}

			_smc.stop_poll = function(id = false) {
				if (_smc.is_logged()) {
					if ($.isNumeric(id)) {
						var post_poll = $('[data-post-poll="{0}"]'.format(id));

						if (post_poll.length && post_poll.data("stopped") != "stopped") {
							post_poll.data("stopped", "stopped");

							$.ajax({
								url: '<?php echo cl_link("native_api/main/stop_poll"); ?>',
								type: 'POST',
								dataType: 'json',
								data: {
									id: id
								}
							}).done(function(data) {
								if (data.status == 200) {
									cl_bs_notify("<?php echo cl_translate("Your poll has been successfully stopped"); ?>", 5000, "info");
								}
								else {
									_smc.errorMSG();
								}
							});
						}
						else {
							return false;
						}
					}
				}

				else {
					_smc.req_auth();
				}
			}

			_smc.pub_reply = function(id = false) {
				if (_smc.is_logged()) {
					if ($.isNumeric(id) && id) {
						_smc.vue.pubbox2.post_privacy = false;
						_smc.vue.pubbox2.thread_id    = id;

						$("div#add_new_post").modal('show');
					}
				}
				else {
					_smc.req_auth();
				}
			}

			_smc.show_post = function(id = false, type = false) {
				if ($.isNumeric(id) && id) {
					if (type == "blocked") {
						var promise = SMColibri.confirm_action({
							btn_1: "<?php echo cl_translate("Cancel"); ?>",
							btn_2: "<?php echo cl_translate("View"); ?>",
							title: "<?php echo cl_translate("Please confirm your actions!"); ?>",
							message: "<?php echo cl_translate("Are you sure you want to see the content of this post? Please note that the owner of the post is on your blacklist."); ?>"
						});
			        }
			        else if(type == "reported") {
			        	var promise = SMColibri.confirm_action({
							btn_1: "<?php echo cl_translate("Cancel"); ?>",
							btn_2: "<?php echo cl_translate("View"); ?>",
							title: "<?php echo cl_translate("Please confirm your actions!"); ?>",
							message: "<?php echo cl_translate("This post has been hidden from you due to your complaint, as it may contain inappropriate content for you. Are you sure you want to see the content of this post?"); ?>"
						});
			        }

			        promise.done(function() {
						$('[data-softhidden-post="{0}"]'.format(id)).remove();
						$("div.confirm-actions-modal").modal("hide");
					});

					promise.fail(function() {
			            $("div.confirm-actions-modal").modal("hide");
			        });
				}
			}

			_smc.delete_post = function(id = false) {
				if ($.isNumeric(id) && id) {
					if (_smc.is_logged()) {
			        	var promise = SMColibri.confirm_action({
							btn_1: "<?php echo cl_translate("Cancel"); ?>",
							btn_2: "<?php echo cl_translate("Delete"); ?>",
							title: "<?php echo cl_translate("Please confirm your actions!"); ?>",
							message: "<?php echo cl_translate("Please note that if you delete this post, then with the removal of this post all posts related to this thread will also be permanently deleted!"); ?>"
						});

						promise.done(function() {
							$.ajax({
								url: '<?php echo cl_link("native_api/main/delete_post"); ?>',
								type: 'POST',
								dataType: 'json',
								data: {id: id},
							}).done(function(data) {
								if (data.status != 200) {
									_smc.errorMSG();
								}
								else {
									if (_smc.curr_pn == 'home') {
										var hp_tl_app = $('div[data-app="homepage"]');

										hp_tl_app.find('div[data-an="entry-list"]').find('div[data-list-item="{0}"]'.format(id)).slideUp(200, function() {
											$(this).remove();

											if (hp_tl_app.find('div[data-an="entry-list"]').find('div[data-list-item]').length < 1) {
												$(window).reloadPage(50);
											}
										});
									}
									else if (_smc.curr_pn == 'profile') {
										var profile_app = $('div[data-app="profile"]');

										profile_app.find('div[data-an="entry-list"]').find('div[data-list-item="{0}"]'.format(id)).slideUp(200, function() {
											$(this).remove();

											if (profile_app.find('div[data-an="entry-list"]').find('div[data-list-item]').length < 1) {
												$(window).reloadPage(50);
											}
										});
									}
									else if(_smc.curr_pn == 'bookmarks') {
										var bookmarks_app = $('div[data-app="bookmarks"]');

										bookmarks_app.find('div[data-an="entry-list"]').find('div[data-list-item="{0}"]'.format(id)).slideUp(200, function() {
											$(this).remove();

											if (bookmarks_app.find('div[data-an="entry-list"]').find('div[data-list-item]').length < 1) {
												$(window).reloadPage(50);
											}
										});
									}
									else if(_smc.curr_pn == 'explore') {
										var search_app = $('div[data-app="explore"]');

										search_app.find('div[data-an="entry-list"]').find('div[data-list-item="{0}"]'.format(id)).slideUp(200, function() {
											$(this).remove();
										});
									}
									else {
										cl_redirect(data.url);
									}
								}
							}).always(function() {
								$("div.confirm-actions-modal").modal("hide");
							});
				        });

						promise.fail(function() {
				            $("div.confirm-actions-modal").modal("hide");
				        });
					}
					else{
						_smc.req_auth();
					}
				}
			}

			_smc.delete_account = function(id = false) {
				if ($.isNumeric(id) && id) {
					if (_smc.is_logged()) {
			        	var promise = SMColibri.confirm_action({
							btn_1: "<?php echo cl_translate("Cancel"); ?>",
							btn_2: "<?php echo cl_translate("Delete account"); ?>",
							title: "<?php echo cl_translate("Please confirm your actions!"); ?>",
							message: "<?php echo cl_translate("Please note that if you delete this user, then all posts, replies, and all other data associated with this user will also be deleted!"); ?>"
						});

						promise.done(function() {
							$.ajax({
								url: '<?php echo cl_link("native_api/cpanel/delete_user"); ?>',
								type: 'POST',
								dataType: 'json',
								data: {id: id},
							}).done(function(data) {
								if (data.status != 200) {
									_smc.errorMSG();
								}
								else {
									_smc.go_back();
								}
							}).always(function() {
								$("div.confirm-actions-modal").modal("hide");
							});
				        });

						promise.fail(function() {
				            $("div.confirm-actions-modal").modal("hide");
				        });
					}
					else{
						_smc.req_auth();
					}
				}
			}

			_smc.suspend_account = function(id = false) {
				if ($.isNumeric(id) && id) {
					if (_smc.is_logged()) {
			        	var promise = SMColibri.confirm_action({
							btn_1: "<?php echo cl_translate("Cancel"); ?>",
							btn_2: "<?php echo cl_translate("Suspend account"); ?>",
							title: "<?php echo cl_translate("Please confirm your actions!"); ?>",
							message: "<?php echo cl_translate("Please note that if you delete this user, then all posts, replies, and all other data associated with this user will also be deleted!"); ?>"
						});

						promise.done(function() {
							$.ajax({
								url: '<?php echo cl_link("native_api/cpanel/toggle_user_status"); ?>',
								type: 'POST',
								dataType: 'json',
								data: {id: id},
							}).done(function(data) {
								if (data.status != 200) {
									_smc.errorMSG();
								}
								else {
									_smc.spa_reload();
								}
							}).always(function() {
								$("div.confirm-actions-modal").modal("hide");
							});
				        });

						promise.fail(function() {
				            $("div.confirm-actions-modal").modal("hide");
				        });
					}
					else{
						_smc.req_auth();
					}
				}
			}

			_smc.unsuspend_account = function(id = false) {
				if ($.isNumeric(id) && id) {
					$.ajax({
						url: '<?php echo cl_link("native_api/cpanel/toggle_user_status"); ?>',
						type: 'POST',
						dataType: 'json',
						data: {id: id},
						beforeSend: function() {
							_smc.progress_bar("start");
						}
					}).done(function(data) {
						if (data.status != 200) {
							_smc.errorMSG();
						}
						else {
							_smc.spa_reload();
						}
					}).always(function() {
						_smc.progress_bar("end");
					});
				}
			}

			_smc.logout = function() {
				var promise = SMColibri.confirm_action({
					btn_1: "<?php echo cl_translate("Cancel"); ?>",
					btn_2: "<?php echo cl_translate("Logout"); ?>",
					title: "<?php echo cl_translate("Leaving already?"); ?>",
					message: "<?php echo cl_translate("Are you sure that you want to log out from your account?"); ?>"
				});

				promise.done(function() {
					cl_redirect("<?php echo cl_link("logout"); ?>");
		        });

				promise.fail(function() {
		            $("div.confirm-actions-modal").modal("hide");
		        });
			}

			_smc.like_post = function(id = false, event = false) {
				if (_smc.is_logged()) {
					if ($.isNumeric(id) && id) {
						// Instant visual feedback - don't disable button
						const $button = $(event);
						const $countSpan = $button.find('span[data-an="likes-count"]');
						const currentCount = parseInt($countSpan.text()) || 0;
						const wasLiked = $button.hasClass('liked');

						// Immediate visual update
						if (wasLiked) {
							$button.removeClass('liked');
							$countSpan.text(Math.max(0, currentCount - 1));
						} else {
							$button.addClass('liked');
							$countSpan.text(currentCount + 1);
						}

						// Quick scale animation for instant feedback
						$button.css('transform', 'scale(1.1)');
						setTimeout(() => {
							$button.css('transform', 'scale(1)');
						}, 150);

						// Send AJAX request in background
						$.ajax({
							url: '<?php echo cl_link("native_api/main/like_post"); ?>',
							type: 'POST',
							dataType: 'json',
							data: {id: id},
						}).done(function(data) {
							if (data.status == 200) {
								// Update with server response (in case of discrepancy)
								$countSpan.text(data.likes_count);
							} else {
								// Revert on error
								if (wasLiked) {
									$button.addClass('liked');
									$countSpan.text(currentCount);
								} else {
									$button.removeClass('liked');
									$countSpan.text(currentCount);
								}
								_smc.errorMSG();
							}
						}).fail(function() {
							// Revert on failure
							if (wasLiked) {
								$button.addClass('liked');
								$countSpan.text(currentCount);
							} else {
								$button.removeClass('liked');
								$countSpan.text(currentCount);
							}
						});
					}
				}
				else{
					_smc.req_auth();
				}
			}

			_smc.repost = function(id = false, event = false) {
				if (_smc.is_logged()) {
					if ($.isNumeric(id) && id) {
						$(event).attr('disabled', 'true');

						$.ajax({
							url: '<?php echo cl_link("native_api/main/repost"); ?>',
							type: 'POST',
							dataType: 'json',
							data: {id: id},
						}).done(function(data) {
							if (data.status == 200) {
								if ($(event).hasClass('reposted')) {
									$(event).removeClass('reposted');

									$(event).removeClass('reposted').addClass('animated bounceIn').promise().done(function(){
										delay(function() {
											$(event).removeClass('animated bounceIn');
										}, 1000);
									});

									$(event).find('span[data-an="reposts-count"]').text(data.reposts_count);

									if (_smc.curr_pn == 'home' || _smc.curr_pn == 'profile') {

										var timeline_app = ((_smc.curr_pn == 'home') ? $('div[data-app="homepage"]') : $('div[data-app="profile"]'));
										var orig_post    = timeline_app.find('div[data-an="entry-list"]').find('[data-list-item="{0}"]'.format(id));
										var repost       = timeline_app.find('div[data-an="entry-list"]').find('[data-repost="{0}"]'.format(data.repost_id));

										if (repost.length) {
											repost.slideUp(200, function() {
												$(this).remove();
											});
										}

										if (orig_post.length) {
											orig_post.find('button[data-an="repost-ctrl"]').removeClass('reposted');
											orig_post.find('span[data-an="reposts-count"]').text(data.reposts_count);
											orig_post.find('span[data-an="reposts-count"]').addClass('animated bounceIn').promise().done(function(){
												delay(function() {
													$(event).removeClass('animated bounceIn');
												}, 1000);
											});
										}
									}
								}
								else {
									$(event).addClass('reposted').addClass('animated bounceIn').promise().done(function(){
										delay(function() {
											$(event).removeClass('animated bounceIn');
										}, 1000);
									});

									$(event).find('span[data-an="reposts-count"]').text(data.reposts_count);
								}
							}
							else {
								_smc.errorMSG();
							}
						}).always(function() {
							$(event).removeAttr('disabled');
						});
					}
				}
				else{
					_smc.req_auth();
				}
			}

			_smc.share_post = function(url = false, encoded_url = false) {
				if (url && encoded_url) {
					var modal = "<div class='modal fadeIn share-post-modal' data-an='share-post' tabindex='-1' data-onclose='remove' role='dialog' aria-hidden='true' data-keyboard='false' data-backdrop='static'><div class='modal-dialog modal-md' role='document'><div class='modal-content'><div class='modal-header'><div class='modal-header__inner'><h5 class='modal-title'><?php echo cl_translate('Share post'); ?></h5><span class='dismiss-modal' data-dismiss='modal'><?php echo str_replace('"', '\'', cl_ficon('dismiss')); ?></span></div></div><div class='modal-body'><div class='social-media-links'><div class='row'><div class='col-3'><a href='https://twitter.com/intent/tweet?url={0}' target='_blank' class='link-item twitter'><?php echo str_replace('"', '\'', cl_icon('logos/logo-twitter')); ?> Twiiter</a></div><div class='col-3'><a href='https://www.facebook.com/sharer.php?u={0}' target='_blank' class='link-item facebook'><?php echo str_replace('"', '\'', cl_icon('logos/logo-facebook')); ?> Facebook</a></div><div class='col-3'><a href='https://www.linkedin.com/shareArticle?mini=true&url={0}' target='_blank' class='link-item linkedin'><?php echo str_replace('"', '\'', cl_icon('logos/logo-linkedin')); ?> Linked In</a></div><div class='col-3'><a href='https://www.pinterest.ru/pin/create/button/?url={0}' target='_blank' class='link-item pinterest'><?php echo str_replace('"', '\'', cl_icon('logos/logo-pinterest')); ?> Pinterest</a></div><div class='col-3'><a href='https://www.reddit.com/submit?url={0}' target='_blank' class='link-item reddit'><?php echo str_replace('"', '\'', cl_icon('logos/logo-reddit')); ?> Reddit</a></div><div class='col-3'><a href='https://api.whatsapp.com/send?text={0}' target='_blank' class='link-item whatsapp'><?php echo str_replace('"', '\'', cl_icon('logos/logo-whatsapp')); ?> WhatsApp</a></div><div class='col-3'><a href='https://telegram.me/share/url?url={0}' target='_blank' class='link-item telegram'><?php echo str_replace('"', '\'', cl_icon('logos/logo-telegram')); ?> Telegram</a></div><div class='col-3'><a href='https://vk.com/share.php?url={0}' target='_blank' class='link-item vkontakte'><?php echo str_replace('"', '\'', cl_icon('logos/logo-vk')); ?> Vkontakte</a></div></div></div><div class='raw-link'><p><?php echo cl_translate('Or copy link'); ?></p><div class='link-input'><input type='text' readonly='true' value='{1}' id='copy-link-input'><button data-clipboard-target='#copy-link-input' data-clipboard-action='copy' class='clip-board-copy copy-link'><?php echo str_replace('"', '\'', cl_ficon('copy')); ?></button></div></div></div></div></div></div>";
					var modal = modal.format(encoded_url,url);

					$('body').find('[data-app="black-hole"]').append($(modal)).promise().done(function() {
						$('body').find('[data-app="black-hole"]').find('[data-an="share-post"]').modal('show');
					});
				}
			}

			_smc.isURL = function(str = "") {
				var regex = new RegExp("^(https?:\\/\\/)?((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|((\\d{1,3}\\.){3}\\d{1,3}))(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*(\\?[;&a-z\\d%_.~+=-]*)?(\\#[-a-z\\d_]*)?$", "i"); 
			  	return regex.test(str);
			}

			_smc.show_likes = function(id = false) {
				if ($.isNumeric(id) && id) {
					$.ajax({
						url: '<?php echo cl_link("native_api/main/show_likes"); ?>',
						type: 'POST',
						dataType: 'json',
						data: {id: id},
					}).done(function(data) {
						if (data.status == 200) {
							$('div[data-app="black-hole"]').append($(data.html)).find('div[data-app="post-likes-list"]').modal('show');
						}

						else if(data.status == 404) {
							_smc.info("<?php echo cl_translate("No likes yet!"); ?>", "<?php echo cl_translate("This post appears to have no likes yet. To like this post, click {@svg_icon@} below it.", array('svg_icon' => "")); ?>");
						}

						else {
							_smc.errorMSG();
						}
					});
				}
			}

			_smc.edit_post = function(id = false) {
				if (_smc.is_logged()) {
					if ($.isNumeric(id) && id) {
						$.ajax({
							url: '<?php echo cl_link("native_api/edit_post/get_data"); ?>',
							type: 'GET',
							dataType: 'json',
							data: {id: id},
						}).done(function(data) {
							if (data.status == 200) {
								$('div[data-app="black-hole"]').append($(data.html)).find('div[data-app="post-text-edit"]').modal('show');
							}
							else {
								_smc.errorMSG();
							}
						});
					}
				}
				else{
					_smc.req_auth();
				}
			}

			_smc.donate_post = function(id = false) {
				if (_smc.is_logged()) {
					if ($.isNumeric(id) && id) {
						$.ajax({
							url: '<?php echo cl_link("native_api/donate/get_form"); ?>',
							type: 'GET',
							dataType: 'json',
							data: {id: id},
						}).done(function(data) {
							if (data.status == 200) {
								$('div[data-app="black-hole"]').append($(data.html)).find('div[data-app="donate-post"]').modal('show');
							}
							else {
								_smc.errorMSG();
							}
						});
					}
				}
				else{
					_smc.req_auth();
				}
			}

			_smc.donate_self = function() {
				if (_smc.is_logged()) {
					_smc.info("You are not permitted to transfer funds to your own account", "As the creator of this fundraiser, you are not allowed to make contributions to your own cause. However, all other individuals who come across this post are encouraged to support the fundraiser by making donations");
				}
				else{
					_smc.req_auth();
				}
			}

			_smc.pin_profile_post = function(id = false, _self = false) {
				if (_smc.is_logged()) {
					if ($.isNumeric(id) && id) {
						$.ajax({
							url: '<?php echo cl_link("native_api/pin_profile_post/pin_post"); ?>',
							type: 'POST',
							dataType: 'json',
							data: {id: id}
						}).done(function(data) {
							if (data.status == 200) {
								if (data.status_code == '1') {
									cl_bs_notify("<?php echo cl_translate("Post has been pinned on your profile page"); ?>", 3000, "info");

									$(_self).find('[data-itag]').text("<?php echo cl_translate('Unpin from my profile'); ?>");
								}
								else {
									cl_bs_notify("<?php echo cl_translate("Post has been unpinned from your profile page"); ?>", 3000, "info");
									$(_self).find('[data-itag]').text("<?php echo cl_translate('Pin to my profile'); ?>");
								}
							}
							else {
								_smc.errorMSG();
							}
						});
					}
				}
				else {
					_smc.req_auth();
				}
			}

			_smc.pin_admin_post = function(id = false, _self = false) {
				if (_smc.is_logged()) {
					if ($.isNumeric(id) && id) {
						$.ajax({
							url: '<?php echo cl_link("native_api/cpanel/pin_feed_post"); ?>',
							type: 'POST',
							dataType: 'json',
							data: {id: id}
						}).done(function(data) {
							if (data.status == 200) {
								if (data.status_code == '1') {
									cl_bs_notify("<?php echo cl_translate("Post has been pinned on user feed pages"); ?>", 3000, "info");

									$(_self).find('[data-itag]').text("<?php echo cl_translate('Unpin post from feeds'); ?>");
								}
								else {
									cl_bs_notify("<?php echo cl_translate("Post has been unpinned from user feed pages"); ?>", 3000, "info");
									$(_self).find('[data-itag]').text("<?php echo cl_translate('Pin post to feeds'); ?>");
								}
							}
							else {
								_smc.errorMSG();
							}
						});
					}
				}
				else {
					_smc.req_auth();
				}
			}

			_smc.bookmark_post = function(id = false, _self = false) {
				if (_smc.is_logged()) {
					if ($.isNumeric(id) && id) {
						$.ajax({
							url: '<?php echo cl_link("native_api/main/bookmark_post"); ?>',
							type: 'POST',
							dataType: 'json',
							data: {id: id, a: 'save'},
						}).done(function(data) {
							if (data.status == 200) {
								if (data.status_code == '1') {
									cl_bs_notify("<?php echo cl_translate("Post has been bookmarked!"); ?>", 3000, "info");

									$(_self).find('[data-itag]').text("<?php echo cl_translate('Unbookmark'); ?>");
								}
								else {
									cl_bs_notify("<?php echo cl_translate("Post has been deleted from bookmarks!"); ?>", 3000, "info");
									$(_self).find('[data-itag]').text("<?php echo cl_translate('Bookmark'); ?>");
								}
							}
							else {
								_smc.errorMSG();
							}
						});
					}
				}
				else {
					_smc.req_auth();
				}
			}

			_smc.load_likes = function(id = false, event = false) {
				if ($.isNumeric(id) && id) {
					var _self     = $(event);
					var likes_ls  = $('div[data-app="post-likes-list"]');
					var last_item = likes_ls.find('div[data-list-item]').last();

					if (last_item.length) {
						$.ajax({
							url: '<?php echo cl_link("native_api/main/load_likes"); ?>',
							type: 'GET',
							dataType: 'json',
							data: {id: id, offset: last_item.data('list-item')},
							beforeSend: function() {
								_self.attr('disabled', 'true').text("<?php echo cl_translate("Please wait"); ?>");
							}
						}).done(function(data) {
							if (data.status == 200) {
								likes_ls.find('div[data-an="users-ls"]').append($(data.html));

								_self.removeAttr('disabled').text("<?php echo cl_translate("Show more"); ?>");
							}
							else {
								_self.text("<?php echo cl_translate("That is all for now!"); ?>");
							}
						});
					}
				}
			}

			_smc.follow = function(event = false) {
				if (event) {
					if (_smc.is_logged()) {
						var target = $(event);

				        if(target.data('action') == 'unfollow') {
				        	var promise = SMColibri.confirm_action({
								btn_1: "<?php echo cl_translate("Cancel"); ?>",
								btn_2: "<?php echo cl_translate("Unfollow"); ?>",
								title: "<?php echo cl_translate("Please confirm your actions!"); ?>",
								message: "<?php echo cl_translate("Please note that, if you unsubscribe then this user's posts will no longer appear in the feed on your main page."); ?>",
							});

							promise.done(function() {
					            target.data('action', 'follow');

					            target.text("<?php echo cl_translate("Follow"); ?>");
								target.replaceClass('main-inline','main-outline');

								$.ajax({
									url: '<?php echo cl_link("native_api/main/follow"); ?>',
									type: 'POST',
									dataType: 'json',
									data: {id: target.data('id')},
								}).done(function(data) {
									if (data.status != 200) {
										_smc.errorMSG();
									}
									else {
										// Update all follow buttons for this user across the site
										var userId = target.data('id');
										var userName = target.data('user-name');

										// Update all follow buttons with the same user ID
										$('button[data-id="' + userId + '"]').each(function() {
											var btn = $(this);
											if (btn.hasClass('btn-follow')) {
												btn.data('action', 'follow');
												btn.text("<?php echo cl_translate("Follow"); ?>");
												btn.removeClass('main-inline').addClass('main-outline');
											}
										});

										// Update data-is-following attribute on all posts by this user
										$('[data-user-id="' + userId + '"]').attr('data-is-following', '0');

										// Update any post items that have this user as publisher
										$('.post-list-item').each(function() {
											var postItem = $(this);
											var postUserId = postItem.find('[data-uinfo-lbox="' + userId + '"]').length;
											if (postUserId > 0) {
												postItem.attr('data-is-following', '0');
											}
										});

										// Also update posts by checking the username link href
										$('.post-list-item').each(function() {
											var postItem = $(this);
											var userLink = postItem.find('.post-username a[href*="/u/' + userName + '"], .post-username a[href*="/' + userName + '"]');
											if (userLink.length > 0) {
												postItem.attr('data-is-following', '0');
											}
										});

										// Update shorts viewer follow buttons
										_smc.updateShortsFollowButtons(userId, false);

										// Update profile page if we're viewing this user's profile
										_smc.updateProfileFollowStatus(userId, userName, false);

										if (_smc.curr_pn == "profile") {
											if (data.refresh != undefined) {
												$(window).reloadPage(1000);
											}
										}
									}
								}).always(function() {
									$("div.confirm-actions-modal").modal("hide");
								});

								cl_bs_notify("<?php echo cl_translate("You unfollowed <b>@{0}</b>"); ?>".format(target.data("user-name")), 3000, "success");
					        });

							promise.fail(function() {
					            $("div.confirm-actions-modal").modal("hide");
					        });
				        }

				        else {
				        	target.data('action', 'unfollow');
							target.text("<?php echo cl_translate("Following"); ?>");
							target.replaceClass('main-outline','main-inline');

							delay(function(){
								target.text("<?php echo cl_translate("Unfollow"); ?>");
							}, 1500);


				        	$.ajax({
								url: '<?php echo cl_link("native_api/main/follow"); ?>',
								type: 'POST',
								dataType: 'json',
								data: {id: target.data('id')},
							}).done(function(data) {
								if (data.status != 200) {
									_smc.errorMSG();
								}
								else {
									// Update all follow buttons for this user across the site
									var userId = target.data('id');
									var userName = target.data('user-name');

									console.log('Following user ' + userId + ' (' + userName + ')');

									// Update all follow buttons with the same user ID
									$('button[data-id="' + userId + '"]').each(function() {
										var btn = $(this);
										if (btn.hasClass('btn-follow')) {
											btn.data('action', 'unfollow');
											btn.text("<?php echo cl_translate("Unfollow"); ?>");
											btn.removeClass('main-outline').addClass('main-inline');
										}
									});

									// Update data-is-following attribute on all posts by this user
									$('[data-user-id="' + userId + '"]').attr('data-is-following', '1');

									// Update any post items that have this user as publisher
									var updatedPosts = 0;
									$('.post-list-item').each(function() {
										var postItem = $(this);
										var postUserId = postItem.find('[data-uinfo-lbox="' + userId + '"]').length;
										if (postUserId > 0) {
											postItem.attr('data-is-following', '1');
											updatedPosts++;
										}
									});

									// Also update posts by checking the username link href
									$('.post-list-item').each(function() {
										var postItem = $(this);
										var userLink = postItem.find('.post-username a[href*="/u/' + userName + '"], .post-username a[href*="/' + userName + '"]');
										if (userLink.length > 0) {
											postItem.attr('data-is-following', '1');
											updatedPosts++;
										}
									});

									console.log('Updated ' + updatedPosts + ' post items for user ' + userId);

									// Update shorts viewer follow buttons
									_smc.updateShortsFollowButtons(userId, true);

									// Update profile page if we're viewing this user's profile
									_smc.updateProfileFollowStatus(userId, userName, true);

									if (_smc.curr_pn == "profile") {
										if (data.refresh != undefined) {
											$(window).reloadPage(1000);
										}
									}
								}
							});

							cl_bs_notify("<?php echo cl_translate("You followed <b>@{0}</b>"); ?>".format(target.data("user-name")), 3000, "success");
				        }
					}
					else{
						_smc.req_auth();
					}
				}
			}

			// Function to update follow buttons in shorts viewer
			_smc.updateShortsFollowButtons = function(userId, isFollowing) {
				// Store follow status in localStorage for persistence across page reloads
				try {
					var followCache = JSON.parse(localStorage.getItem('followStatusCache') || '{}');
					followCache[userId] = isFollowing;
					localStorage.setItem('followStatusCache', JSON.stringify(followCache));
				} catch(e) {
					console.log('Error saving follow status to localStorage:', e);
				}

				// Also store in memory cache
				if (!window.followStatusCache) {
					window.followStatusCache = {};
				}
				window.followStatusCache[userId] = isFollowing;

				// Update shorts viewer follow buttons for this user
				$('#shortsViewer .shorts-publisher button[data-id="' + userId + '"]').each(function() {
					var btn = $(this);
					var userName = btn.data('user-name');

					if (isFollowing) {
						// User is now followed - remove the follow button completely
						btn.remove();
					} else {
						// User is now unfollowed - show follow button
						btn.replaceWith('<button class="btn-follow" onclick="SMColibri.follow(this);" data-action="follow" data-user-name="' + userName + '" data-id="' + userId + '">Follow</button>');
					}
				});

				// If user was unfollowed and no button exists, add it back
				if (!isFollowing) {
					$('#shortsViewer .shorts-publisher').each(function() {
						var publisher = $(this);
						var usernameLink = publisher.find('.username');
						if (usernameLink.length > 0) {
							var linkHref = usernameLink.attr('href') || '';
							// Check if this publisher is for the user we just unfollowed
							if (linkHref.includes('/' + userName) || linkHref.includes('/u/' + userName)) {
								// Check if follow button already exists
								if (publisher.find('button[data-id="' + userId + '"]').length === 0) {
									// Add follow button back
									publisher.append('<button class="btn-follow" onclick="SMColibri.follow(this);" data-action="follow" data-user-name="' + userName + '" data-id="' + userId + '">Follow</button>');
								}
							}
						}
					});
				}

				console.log('Updated follow status for user ' + userId + ' to ' + isFollowing);
			}

			// Function to update profile page follow status and counts
			_smc.updateProfileFollowStatus = function(userId, userName, isFollowing) {
				// Check if we're on a profile page
				if (_smc.curr_pn == "profile") {
					// Get current URL to check if it's this user's profile
					var currentUrl = window.location.href;
					var profileUsername = currentUrl.split('/').pop().split('?')[0];

					// If we're on this user's profile page, update the follower count
					if (profileUsername === userName || currentUrl.includes('/' + userName)) {
						var followerCountElement = $('.user-profile__counter .counter-item span').first();
						if (followerCountElement.length > 0) {
							var currentCount = parseInt(followerCountElement.text().replace(/,/g, '')) || 0;
							var newCount = isFollowing ? currentCount + 1 : Math.max(0, currentCount - 1);
							followerCountElement.text(newCount.toLocaleString());
							console.log('Updated follower count for ' + userName + ' to ' + newCount);
						}
					}
				}

				// Update follow buttons on any profile page
				$('.user-profile__actions button[data-id="' + userId + '"]').each(function() {
					var btn = $(this);
					if (isFollowing) {
						btn.data('action', 'unfollow');
						btn.text("<?php echo cl_translate("Unfollow"); ?>");
						btn.removeClass('main-outline').addClass('main-inline');
					} else {
						btn.data('action', 'follow');
						btn.text("<?php echo cl_translate("Follow"); ?>");
						btn.removeClass('main-inline').addClass('main-outline');
					}
				});
			}

			// Initialize follow status cache from localStorage on page load
			_smc.initFollowStatusCache = function() {
				try {
					var followCache = JSON.parse(localStorage.getItem('followStatusCache') || '{}');
					if (!window.followStatusCache) {
						window.followStatusCache = {};
					}

					// Clean up old entries (keep only last 100 entries to prevent localStorage bloat)
					var entries = Object.keys(followCache);
					if (entries.length > 100) {
						var newCache = {};
						entries.slice(-100).forEach(function(key) {
							newCache[key] = followCache[key];
						});
						followCache = newCache;
						localStorage.setItem('followStatusCache', JSON.stringify(followCache));
					}

					// Merge localStorage data into memory cache
					Object.assign(window.followStatusCache, followCache);
					console.log('Loaded follow status cache from localStorage:', followCache);
				} catch(e) {
					console.log('Error loading follow status from localStorage:', e);
					window.followStatusCache = {};
				}
			}

			// Update all follow buttons on page based on cached status
			_smc.updateAllFollowButtonsFromCache = function() {
				if (!window.followStatusCache) return;

				Object.keys(window.followStatusCache).forEach(function(userId) {
					var isFollowing = window.followStatusCache[userId];

					// Update all follow buttons for this user
					$('button[data-id="' + userId + '"]').each(function() {
						var btn = $(this);
						if (btn.hasClass('btn-follow')) {
							if (isFollowing) {
								btn.data('action', 'unfollow');
								btn.text("<?php echo cl_translate("Unfollow"); ?>");
								btn.removeClass('main-outline').addClass('main-inline');
							} else {
								btn.data('action', 'follow');
								btn.text("<?php echo cl_translate("Follow"); ?>");
								btn.removeClass('main-inline').addClass('main-outline');
							}
						}
					});

					// Update post data attributes
					$('.post-list-item').each(function() {
						var postItem = $(this);
						var postUserId = postItem.find('[data-uinfo-lbox="' + userId + '"]').length;
						if (postUserId > 0) {
							postItem.attr('data-is-following', isFollowing ? '1' : '0');
						}
					});
				});
			}

			// Call initialization when page loads
			$(document).ready(function() {
				_smc.initFollowStatusCache();
				// Update buttons after a short delay to ensure DOM is ready
				setTimeout(function() {
					_smc.updateAllFollowButtonsFromCache();
				}, 500);
			});

			_smc.req_follow = function(event = false) {
				if (event) {
					if (_smc.is_logged()) {
						var target = $(event);

				        if(target.data('action') == 'unfollow') {
				        	var promise = SMColibri.confirm_action({
								btn_1: "<?php echo cl_translate("Cancel"); ?>",
								btn_2: "<?php echo cl_translate("Unfollow"); ?>",
								title: "<?php echo cl_translate("Please confirm your actions!"); ?>",
								message: "<?php echo cl_translate("Please note that, if you unsubscribe then this user's posts will no longer appear in the feed on your main page."); ?>",
							});

							promise.done(function() {
					            target.data('action', 'follow');
								target.text("<?php echo cl_translate("Follow"); ?>");
								target.replaceClass('main-inline','main-outline');

								$.ajax({
									url: '<?php echo cl_link("native_api/main/follow"); ?>',
									type: 'POST',
									dataType: 'json',
									data: {id: target.data('id')},
								}).done(function(data) {
									if (data.status != 200) {
										_smc.errorMSG();
									}
								}).always(function() {
									$("div.confirm-actions-modal").modal("hide");
								});

								cl_bs_notify("<?php echo cl_translate("You unfollowed <b>@{0}</b>"); ?>".format(target.data("user-name")), 3000, "success");
					        });

							promise.fail(function() {
					            $("div.confirm-actions-modal").modal("hide");
					        });
				        }
				        else if(target.data('action') == 'cancel') {
				        	var promise = SMColibri.confirm_action({
								btn_1: "<?php echo cl_translate("Cancel"); ?>",
								btn_2: "<?php echo cl_translate("Cancel request"); ?>",
								title: "<?php echo cl_translate("Please confirm your actions!"); ?>",
								message: "<?php echo cl_translate("This will cancel your pending request and this user will no longer see it."); ?>",
							});

							promise.done(function() {
					            target.data('action', 'follow');
								target.text("<?php echo cl_translate("Follow"); ?>");
								target.replaceClass('main-inline','main-outline');

								$.ajax({
									url: '<?php echo cl_link("native_api/main/follow"); ?>',
									type: 'POST',
									dataType: 'json',
									data: {id: target.data('id')},
								}).done(function(data) {
									if (data.status != 200) {
										_smc.errorMSG();
									}
								}).always(function() {
									$("div.confirm-actions-modal").modal("hide");
								});

								cl_bs_notify("<?php echo cl_translate("You unfollowed <b>@{0}</b>"); ?>".format(target.data("user-name")), 3000, "success");
					        });

							promise.fail(function() {
					            $("div.confirm-actions-modal").modal("hide");
					        });
				        }

				        else {
				        	target.data('action', 'cancel');
			            	target.text("<?php echo cl_translate("Requested"); ?>");
							target.replaceClass('main-outline', 'main-inline');

							delay(function() {
								target.text("<?php echo cl_translate("Pending"); ?>");
							}, 1500);	

				        	$.ajax({
								url: '<?php echo cl_link("native_api/main/follow"); ?>',
								type: 'POST',
								dataType: 'json',
								data: {id: target.data('id')},
							}).done(function(data) {
								if (data.status != 200) {
									_smc.errorMSG();
								}
							});

							cl_bs_notify("<?php echo cl_translate("Follow request sent to <b>@{0}</b>"); ?>".format(target.data("user-name")), 3000, "info");
				        }
					}
					else{
						_smc.req_auth();
					}
				}
			}

			_smc.block = function(event = false) {
				if (event) {
					if (_smc.is_logged()) {
						var target = $(event);

				        if(target.data('action') == 'block') {
				        	var promise = SMColibri.confirm_action({
								btn_1: "<?php echo cl_translate("Cancel"); ?>",
								btn_2: "<?php echo cl_translate("Block"); ?>",
								title: "<?php echo cl_translate("Please confirm your actions!"); ?>",
								message: "<?php echo cl_translate("Blocked users will no longer be able to write a message to you, follow you, or see your profile and publications, etc."); ?>",
							});

							promise.done(function() {
					            target.data('action','unblock');
								target.text("<?php echo cl_translate("Unblock"); ?>");

								if (target.hasClass('toggle-class')) {
									target.replaceClass('main-inline', 'main-outline');
								}
	
								$.ajax({
									url: '<?php echo cl_link("native_api/main/block"); ?>',
									type: 'POST',
									dataType: 'json',
									data: {id: target.data('id')},
								}).done(function(data) {
									if (data.status != 200) {
										SMColibri.errorMSG();
									}
									else {
										if (_smc.curr_pn == 'profile') {
											_smc.spa_reload();
										}
									}
								}).always(function() {
									$("div.confirm-actions-modal").modal("hide");
								});
					        });

							promise.fail(function() {
					            $("div.confirm-actions-modal").modal("hide");
					        });
				        }

				        else if(target.data('action') == 'unblock') {
				        	var promise = SMColibri.confirm_action({
								btn_1: "<?php echo cl_translate("Cancel"); ?>",
								btn_2: "<?php echo cl_translate("Unblock"); ?>",
								title: "<?php echo cl_translate("Please confirm your actions!"); ?>",
								message: "<?php echo cl_translate("Are you sure you want to unblock this user? Now they can follow you or see your posts, etc."); ?>",
							});

							promise.done(function() {
					            target.data('action','block');
								target.text("<?php echo cl_translate("Block"); ?>");

								if (target.hasClass('toggle-class')) {
									target.replaceClass('main-outline', 'main-inline');
								}
	
								$.ajax({
									url: '<?php echo cl_link("native_api/main/block"); ?>',
									type: 'POST',
									dataType: 'json',
									data: {id: target.data('id')},
								}).done(function(data) {
									if (data.status != 200) {
										SMColibri.errorMSG();
									}
									else {
										if (_smc.curr_pn == 'profile') {
											$(window).reloadPage();
										}
									}
								}).always(function() {
									$("div.confirm-actions-modal").modal("hide");
								});
					        });

							promise.fail(function() {
					            $("div.confirm-actions-modal").modal("hide");
					        });
				        }
					}
					else{
						_smc.req_auth();
					}
				}
			}

			_smc.errorMSG = function() {
				cl_bs_notify("<?php echo cl_translate("An error occurred while processing your request. Please try again later."); ?>", 5000, "error");
			}

			_smc.extend_vue = function(app_name = "", vue_instance = {}) {
				_smc.vue[app_name] = vue_instance;
			}

			_smc.progress_bar = function(a = "show") {
				if (a == "show") {
					window.waitMe.start();
				}

				else {
					window.waitMe.end();
				}
			}

			_smc.update_msb_indicators = function() {
				if (_smc.is_logged()) {
					var main_left_sb   = $('div[data-app="left-sidebar"]');
					var main_bottom_nb = $('div[data-app="mobile-navbar"]');
					var main_header_nb = $('.timeline-header .navbar-ctrl[data-navitem="notifications"]');
					var timer_id       = setInterval(function() {
						$.ajax({
							url: '<?php echo cl_link("native_api/main/update_msb_indicators"); ?>',
							type: 'GET',
							dataType: 'json',
						}).done(function(data) {
							if (data.status == 200) {
								if (data.notifications > 0) {
									var notifs_total = data.notifications;

									if (data.notifications > 99) {
										notifs_total = "99+";
									}

									main_left_sb.find('[data-an="group-ind"]').addClass("group-ind-active");
									main_left_sb.find('[data-an="new-notifs"]').text($.trim(notifs_total));
									main_bottom_nb.find('[data-an="new-notifs"]').text($.trim(notifs_total));
									main_header_nb.find('[data-an="new-notifs"]').text($.trim(notifs_total));
								}
								else {
									main_left_sb.find('[data-an="group-ind"]').removeClass("group-ind-active");
									main_left_sb.find('[data-an="new-notifs"]').empty();
									main_bottom_nb.find('[data-an="new-notifs"]').empty();
									main_header_nb.find('[data-an="new-notifs"]').empty();
								}

								if (data.messages) {

									var messages_total = data.messages;

									if (data.messages > 99) {
										messages_total = "99+";
									}

									main_left_sb.find('[data-an="group-ind"]').addClass("group-ind-active");
									main_left_sb.find('[data-an="new-messages"]').text($.trim(messages_total));
									main_bottom_nb.find('[data-an="new-messages"]').text($.trim(messages_total));
								}
								else {
									main_left_sb.find('[data-an="group-ind"]').removeClass("group-ind-active");
									main_left_sb.find('[data-an="new-messages"]').empty();
									main_bottom_nb.find('[data-an="new-messages"]').empty();
								}
							}
							else {
								clearInterval(timer_id);
							}
						});
					}, (Number(_smc.msb_upinterval) * 1000));
				}
			}

			_smc.hide_sb = function() {
				$('div[data-app="lsb-back-drop"]').trigger('click');
			}

			_smc.spa_load = function(url = "", push_state = true, reload = false) {

				if (_smc.curr_url == url && reload == false) {
					return false;
				}

				_smc.curr_url = url;

				var timeline      = $('[data-el="timeline-container-wrapper"]');
				var preloader     = timeline.find('[data-el="spa-preloader"]');
				var left_sidebar  = $('[data-app="left-sidebar"]');
				var mobile_navbar = $('[data-app="mobile-navbar"]');

				if (push_state) {
					window.history.pushState({state: "new", back_url: url}, "", url);
				}

				if (window.mobileCheck()) {
					_smc.hide_sb();
				}

				$.ajax({
					url: url,
					type: 'GET',
					data: {spa_load: '1'},
					dataType: 'json',
					async: true,
					beforeSend: function() {
						_smc.user_lbox_rm();

						// Clean up videos before navigation to prevent data waste
						_smc.cleanupVideosBeforeNavigation();

						preloader.removeClass('d-none');

						_smc.toggleSB("hide");

						$(window).scrollTop(0);

						cl_close_all_modals();
					}
				}).done(function(data = {}) {
					if (data.status == 200) {
						var prevapp   = _smc.curr_pn;
						var json_data = data.json_data;
						_smc.curr_pn  = json_data.pn;


						$('head').find('title').text(json_data.page_title);

						if ($('body').hasClass('cl-app-{0}'.format(prevapp))) {
							$('body').removeClass('cl-app-{0}'.format(prevapp));
							$('body').addClass('cl-app-{0}'.format(json_data.pn));
						}
						else {
							$('body').addClass('cl-app-{0}'.format(json_data.pn));
						}

						$('body').attr('data-page-tab', json_data.page_tab);
		


						timeline.find('[data-el="timeline-content"]').html($(data.html));

						left_sidebar.find('[data-navitem]').each(function(index, el) {
							$(el).removeClass('active');
						}).promise().done(function() {

							if (_smc.curr_pn == "profile") {
								if (json_data.page_xdata.is_me) {
									left_sidebar.find('[data-navitem="profile"]').addClass('active');
								}
							}
							else{
								left_sidebar.find('[data-navitem="{0}"]'.format(_smc.curr_pn)).addClass('active');
							}
						});

						mobile_navbar.find('[data-navitem]').each(function(index, el) {
							$(el).removeClass('active');
						}).promise().done(function() {
							mobile_navbar.find('[data-navitem="{0}"]'.format(_smc.curr_pn)).addClass('active');

							// INSTANT video autoplay trigger - no delay at all
							_smc.triggerInstantAutoplay();
						});
					}
					else if(data.status == 302) {
						_smc.spa_load(data.redirect_url);
					}

					else {
						_smc.spa_load("<?php echo cl_link('404'); ?>");
					}
				}).always(function() {
					if (_smc.timeout !== false) {
						clearTimeout(_smc.timeout);
					}

					_smc.timeout = setTimeout(function() {
						preloader.addClass('d-none');

						// Reinitialize video systems immediately after SPA navigation
						_smc.reinitializeVideoSystems();
					}, 10); // Minimal delay for instant response

					$("div.dropdown-menu.show").each(function(index, el) {
						$(el).removeClass("show");
					});
				});
			}

			_smc.spa_reload = function() {
				if (window.location.href != undefined) {
					_smc.spa_load(window.location.href, false, true);
				}
				else {
					_smc.spa_load(data['url'], false, true);
				}

				return false;
			}

			_smc.go_back = function() {
				history.back();
			}

			// Reinitialize video systems after SPA navigation
			_smc.reinitializeVideoSystems = function() {
				console.log('🎬 SPA: SIMPLE video systems reinitialization...');

				// Find all timeline videos
				var timelineVideos = document.querySelectorAll('.publication-video video, .cl-plyr-video video');
				console.log('🎬 SPA: Found', timelineVideos.length, 'timeline videos');

				// Simple re-registration with unified video manager
				if (window.__unifiedVideoManager) {
					timelineVideos.forEach(function(video) {
						if (video.id && !video.closest('#shortsViewer')) {
							// Remove from existing tracking to avoid duplicates
							window.__unifiedVideoManager.allVideos.delete(video);
							// Re-register for autoplay
							window.__unifiedVideoManager.registerVideo(video, 'timeline');
						}
					});
				}

				// Re-register with smart video preloader
				if (window.__smartVideoPreloader) {
					timelineVideos.forEach(function(video) {
						if (video.id && !video.closest('#shortsViewer')) {
							window.__smartVideoPreloader.registerVideo(video);
						}
					});
				}

				// Initialize HLS/MP4 system for new videos
				timelineVideos.forEach(function(video) {
					if (video.dataset.hls && !video._hlsInitialized && window.initializeHLSVideos) {
						window.initializeHLSVideos('#' + video.id, false);
					}
				});

				// Dispatch custom event for other video systems
				window.dispatchEvent(new CustomEvent('vlixContentUpdated'));

				console.log('🎬 SPA: Simple reinitialization complete');
			}

			// Trigger INSTANT autoplay - no delays, no thresholds, just play
			_smc.triggerInstantAutoplay = function() {
				console.log('🎬 SPA: INSTANT autoplay trigger - no delays!');

				// Find first timeline video and play it immediately
				var timelineVideos = document.querySelectorAll('.publication-video video, .cl-plyr-video video');
				if (timelineVideos.length > 0) {
					var firstVideo = timelineVideos[0];
					if (firstVideo && firstVideo.id) {
						console.log('🎬 SPA: INSTANT play first video:', firstVideo.id);

						// Register with unified manager if needed
						if (window.__unifiedVideoManager) {
							if (!window.__unifiedVideoManager.allVideos.has(firstVideo)) {
								window.__unifiedVideoManager.registerVideo(firstVideo, 'timeline');
							}
							// Force play immediately - no visibility checks
							window.__unifiedVideoManager.playVideo(firstVideo, 'timeline', 1.0);
						} else {
							// Direct play if no manager
							firstVideo.play().catch(function(e) {
								console.log('Direct play failed:', e);
							});
						}
					}
				}
			}

			// Compatibility function for VlixSPA system
			_smc.reinit = function() {
				console.log('🎬 SMColibri: Reinit called - delegating to video systems reinitialization');
				_smc.reinitializeVideoSystems();
			}

			// Clean up videos before SPA navigation to prevent data waste
			_smc.cleanupVideosBeforeNavigation = function() {
				console.log('🎬 SPA: Cleaning up videos before navigation...');

				// Pause all videos to stop downloading
				document.querySelectorAll('video').forEach(function(video) {
					video.pause();

					// For videos that are not in the shorts viewer, clean up to save data
					if (!video.closest('#shortsViewer')) {
						console.log('🎬 SPA: Cleaning up video before navigation:', video.id);

						// Remove any ongoing downloads by clearing src temporarily if still loading
						if (video.src && video.readyState < 4) {
							var currentSrc = video.src;
							video.removeAttribute('src');
							video.load(); // This stops the download

							// Restore src immediately for potential state preservation
							video.src = currentSrc;
						}
					}
				});

				// Clean up video managers
				if (window.__unifiedVideoManager) {
					// Pause all managed videos
					if (window.__unifiedVideoManager.pauseAllVideos) {
						window.__unifiedVideoManager.pauseAllVideos();
					}
				}

				// Clean up smart preloader
				if (window.__smartVideoPreloader && window.__smartVideoPreloader.pauseAllPreloading) {
					window.__smartVideoPreloader.pauseAllPreloading();
				}
			}

			_smc.jump_back = function(step = 1) {
				history.go(step);
			}

			_smc.ad_conversion = function(e = false) {
				if (e) {
					var _self = $(e);
					var id    = _self.data('ad-id');
					var url   = _self.data('ad-url');

					if (_self.data('conversed') == undefined) {
						$.ajax({
							url: '<?php echo cl_link("native_api/ads/ad_conversion"); ?>',
							type: 'POST',
							dataType: 'json',
							data: {id: id},
						});

						_self.data('conversed', 'true');
					}

					window.open(url, '_blank');
				}
			}

			_smc.report_post = function(id = false) {
				if (_smc.is_logged()) {
					if (_smc.vue.report_post != undefined && id) {
	                    _smc.vue.report_post.open(id);
					}
					else {
						_smc.errorMSG();
					}
				}
                else {
                    _smc.req_auth();
                }
			}

			_smc.update_ilb = function() {
				$("a.fbox-media").fancybox({
					arrows: true,
					openEffect: 'elastic',
	    			closeEffect: false,
	    			hash: false,
					i18n: {
					    en: {
					    	ERROR: "The requested content could not be loaded. <br/> Please try again later.",
					    }
					}
				});	
			}

			_smc.update_plyr = function() {
				// --------------------------------------------------------------
				// Lightweight Reels-style player replacement for all videos
				// --------------------------------------------------------------
				const threshold = 0.25;

				// Global mute flag (true = muted)
				if(window.__globalMute===undefined){window.__globalMute=true;}

				// Helper to toggle mute for all videos (feed + overlay)
				window.toggleGlobalMute = function(state){
					window.__globalMute = state;
					document.querySelectorAll('video').forEach(v=>{v.muted = state;});
				};

				// Create unified video playback manager (singleton)
				if (!window.__unifiedVideoManager) {
					window.__unifiedVideoManager = {
						currentVideo: null,
						allVideos: new Set(),
						observers: new Map(),
						isScrolling: false,
						scrollTimeout: null,
						lastVideoSwitch: 0,
						lastScrollY: 0,

						// Register a video for unified playback management
						registerVideo(video, context = 'timeline') {
							if (!video || this.allVideos.has(video)) return;

							this.allVideos.add(video);
							console.log('Registered video for unified playback:', video.id || 'unnamed', 'context:', context);

							// Add "Watch More vlix." overlay for timeline videos when they end
							if (context === 'timeline') {
								this.addWatchMoreOverlay(video);
							}

							// Create context-specific observer if needed
							if (!this.observers.has(context)) {
								// Use multiple thresholds for better responsiveness
								const thresholds = [0.1, 0.25, 0.5, 0.75, 1.0];
								this.observers.set(context, new IntersectionObserver((entries) => {
									// Process all entries first to get full picture
									const visibleVideos = [];

									entries.forEach((entry) => {
										const vid = entry.target;

										// ULTRA DATA SAVING: Don't preload timeline videos automatically
										// Only load when user actually clicks to play
										console.log(`📱 DATA SAVING: Timeline video ${vid.id} visible but not preloaded to save data`);

										// Collect videos with meaningful visibility
										if (entry.intersectionRatio >= 0.3) {
											visibleVideos.push({
												video: vid,
												ratio: entry.intersectionRatio,
												context: context
											});
										}
									});

									// Use smart selection instead of individual decisions
									if (visibleVideos.length > 0) {
										// Let the smart algorithm decide which video should play
										const result = this.getMostVisibleVideo();
										if (result.video && result.visibility >= 0.3) {
											this.playVideo(result.video, context, result.visibility);
										}
									} else if (this.currentVideo) {
										// Timeline videos should pause immediately when out of view
										const isTimelineVideo = this.currentVideo.closest('.publication-video, .cl-plyr-video');
										if (isTimelineVideo) {
											// For timeline videos, pause immediately when not visible
											this.pauseVideo(this.currentVideo);
											console.log('⏸️ TIMELINE: Paused video out of view:', this.currentVideo.id || 'unnamed');
										} else if (!this.isScrolling) {
											// For other videos (shorts), only pause when not scrolling
											this.pauseVideo(this.currentVideo);
										}
									}
								}, {
									threshold: thresholds,
									rootMargin: '0px 0px 0px 0px' // No margin to prevent background loading
								}));
							}

							this.observers.get(context).observe(video);
						},

						// Add "Watch More vlix." overlay for timeline videos
						addWatchMoreOverlay(video) {
							if (!video || video._watchMoreAdded) return;

							// Only add overlay in PWA mode
							const isPWA = window.matchMedia && window.matchMedia('(display-mode: standalone)').matches ||
							              window.navigator.standalone === true ||
							              document.referrer.includes('android-app://');

							if (!isPWA) {
								console.log('🎬 WATCH MORE: Not in PWA mode, skipping overlay for video:', video.id);
								return;
							}

							video._watchMoreAdded = true;
							console.log('🎬 WATCH MORE: Adding overlay to timeline video:', video.id, 'loop:', video.loop);

							// Keep loop enabled but detect when video reaches the end

							// Create the overlay element
							const overlay = document.createElement('div');
							overlay.className = 'watch-more-vlix-overlay';
							overlay.innerHTML = `
								<div class="watch-more-content">
									<div class="watch-more-text">Watch More vlix.</div>
									<div class="watch-more-arrow">→</div>
								</div>
							`;
							overlay.style.cssText = `
								position: absolute;
								top: 0;
								left: 0;
								width: 100%;
								height: 100%;
								background: rgba(0, 0, 0, 0.7);
								display: none;
								align-items: center;
								justify-content: center;
								z-index: 500;
								cursor: pointer;
								backdrop-filter: blur(2px);
							`;

							// Style the content
							const content = overlay.querySelector('.watch-more-content');
							content.style.cssText = `
								text-align: center;
								color: white;
								font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
							`;

							const text = overlay.querySelector('.watch-more-text');
							text.style.cssText = `
								font-size: 18px;
								font-weight: 600;
								margin-bottom: 8px;
								letter-spacing: 0.5px;
							`;

							const arrow = overlay.querySelector('.watch-more-arrow');
							arrow.style.cssText = `
								font-size: 24px;
								font-weight: bold;
								opacity: 0.8;
							`;

							// Find the video container and add the overlay
							const videoContainer = video.closest('.cl-plyr-video, .publication-video, .lozad-media');
							console.log('🎬 WATCH MORE: Video container found:', !!videoContainer, videoContainer?.className);

							if (videoContainer) {
								// Ensure container has relative positioning
								if (getComputedStyle(videoContainer).position === 'static') {
									videoContainer.style.position = 'relative';
								}
								videoContainer.appendChild(overlay);
								console.log('✅ WATCH MORE: Overlay added to container for video:', video.id);
							} else {
								console.warn('⚠️ WATCH MORE: No suitable container found for video:', video.id);
								// Fallback: add to video's parent
								if (video.parentElement) {
									video.parentElement.style.position = 'relative';
									video.parentElement.appendChild(overlay);
									console.log('✅ WATCH MORE: Overlay added to parent element for video:', video.id);
								}
							}

							// Track if overlay has been shown for this play cycle
							let overlayShownThisCycle = false;

							// Add timeupdate listener to detect when video reaches the end (with loop)
							video.addEventListener('timeupdate', () => {
								if (!video.duration) return;

								const timeRemaining = video.duration - video.currentTime;

								// Show overlay when video is within 0.5 seconds of ending
								if (timeRemaining <= 0.5 && timeRemaining > 0 && !overlayShownThisCycle) {
									console.log('📺 TIMELINE VIDEO NEAR END: Showing "Watch More vlix." overlay for', video.id);
									console.log('📺 Time remaining:', timeRemaining, 'Current time:', video.currentTime, 'Duration:', video.duration);
									overlay.style.display = 'flex';
									overlayShownThisCycle = true;
								}

								// Reset flag when video loops back to beginning
								if (video.currentTime < 1 && overlayShownThisCycle) {
									console.log('📺 VIDEO LOOPED: Resetting overlay flag for', video.id);
									overlayShownThisCycle = false;
								}
							});

							// Also add ended event listener as backup (in case loop gets disabled)
							video.addEventListener('ended', () => {
								console.log('📺 TIMELINE VIDEO ENDED: Showing "Watch More vlix." overlay for', video.id);
								overlay.style.display = 'flex';
								overlayShownThisCycle = true;
							});

							// Add debugging for metadata loading
							video.addEventListener('loadedmetadata', () => {
								console.log('📺 WATCH MORE DEBUG: Video loaded metadata:', video.id, 'duration:', video.duration, 'loop:', video.loop);
							});

							// Add click handler to open shorts - PWA mode only
							overlay.addEventListener('click', (e) => {
								e.preventDefault();
								e.stopPropagation();
								console.log('🎬 WATCH MORE: Overlay clicked for timeline video');

								// Only proceed if in PWA mode
								const isPWA = window.matchMedia && window.matchMedia('(display-mode: standalone)').matches ||
								              window.navigator.standalone === true ||
								              document.referrer.includes('android-app://');

								if (!isPWA) {
									console.log('🎬 WATCH MORE: Not in PWA mode, ignoring overlay click');
									return;
								}

								// Hide the overlay
								overlay.style.display = 'none';

								// Set flag to prevent unauthorized play detection while shorts are active
								window.__shortsActive = true;
								console.log('🎬 WATCH MORE: Set shorts active flag to prevent unauthorized play detection');

								// Save current video state before opening shorts
								window.clickedVideoState = {
									id: video.id,
									currentTime: video.currentTime,
									paused: video.paused,
									muted: video.muted
								};

								// Store reference to original video element for seamless transition
								window.originalTimelineVideo = video;

								// User clicked overlay intending to watch with sound – unmute globally
								console.log('🔊 Watch More overlay clicked - unmuting globally. Current mute state:', window.__globalMute);
								if(window.toggleGlobalMute){
									window.toggleGlobalMute(false);
									console.log('✅ Called toggleGlobalMute(false). New state:', window.__globalMute);
								} else {
									// Fallback
									console.log('⚠️ toggleGlobalMute not found, using fallback');
									window.__globalMute = false;
								}

								console.log('🎬 WATCH MORE: About to call openOverlay with video:', video.id);
								try {
									// Call the same openOverlay function that timeline video clicks use
									if (typeof openOverlay === 'function') {
										openOverlay(video);
									} else {
										console.error('❌ openOverlay function not found');
										// Fallback: redirect to shorts page
										window.location.href = '/shorts';
									}
								} catch (error) {
									console.error('❌ Error in openOverlay:', error);
									console.error('Stack trace:', error.stack);
									// Fallback: redirect to shorts page
									window.location.href = '/shorts';
								}
							});

							// Hide overlay when video starts playing again
							video.addEventListener('play', () => {
								overlay.style.display = 'none';
								overlayShownThisCycle = false;
								console.log('📺 VIDEO PLAY: Hidden overlay and reset flag for', video.id);
							});

							// Add a manual trigger for testing
							video._showWatchMoreOverlay = () => {
								console.log('🧪 MANUAL TRIGGER: Showing Watch More overlay for', video.id);
								overlay.style.display = 'flex';
							};

							console.log('✅ WATCH MORE: Added "Watch More vlix." overlay to timeline video:', video.id);
						},

						// Manual function to test overlays on all timeline videos
						testWatchMoreOverlays() {
							// Only test in PWA mode
							const isPWA = window.matchMedia && window.matchMedia('(display-mode: standalone)').matches ||
							              window.navigator.standalone === true ||
							              document.referrer.includes('android-app://');

							if (!isPWA) {
								console.log('🧪 TESTING: Not in PWA mode, skipping overlay test');
								return;
							}

							console.log('🧪 TESTING: Triggering Watch More overlays on all timeline videos');
							const timelineVideos = document.querySelectorAll('.publication-video video, .cl-plyr-video video');
							timelineVideos.forEach(video => {
								if (video._showWatchMoreOverlay) {
									console.log('🧪 TESTING: Triggering overlay for', video.id);
									video._showWatchMoreOverlay();
								} else {
									console.log('🧪 TESTING: No overlay function found for', video.id);
								}
							});
						},

						// Play a video and pause all others with smart switching
						playVideo(video, context = 'timeline', intersectionRatio = 1.0) {
							if (!video) return;

							// If this is already the current video and it's playing, don't interrupt
							if (video === this.currentVideo && !video.paused) return;

							// Debounce rapid video switching
							const now = Date.now();
							if (this.lastVideoSwitch && (now - this.lastVideoSwitch) < 100) {
								// Too soon since last switch, ignore
								return;
							}

							// Use intersection ratio for more lenient visibility check
							const isVisible = intersectionRatio >= 0.3 || this.isVideoVisibleLenient(video);
							if (!isVisible) {
								console.log('Video not sufficiently visible, skipping play:', video.id || 'unnamed', 'ratio:', intersectionRatio);
								return;
							}

							// Additional stability check - don't switch if current video is still well visible
							if (this.currentVideo && this.currentVideo !== video) {
								const currentVisibility = this.isVideoVisibleLenient(this.currentVideo);
								if (currentVisibility && intersectionRatio < 0.7) {
									// Current video is still visible and new video isn't dominant enough
									console.log('Current video still visible, not switching:', this.currentVideo.id || 'unnamed');
									return;
								}
							}

							// Record the switch time
							this.lastVideoSwitch = now;

							// Pause all other videos first
							this.pauseAllExcept(video);

							// Set as current
							this.currentVideo = video;
							console.log('Playing video:', video.id || 'unnamed', 'context:', context, 'visibility:', intersectionRatio);

							// Preload and play immediately without delay for better responsiveness
							if (context === 'timeline') {
								lazyLoadAndPlay(video);
							} else {
								// For shorts or other contexts, use their specific play logic
								if (window.loadAndPlay) {
									window.loadAndPlay(video);
								} else {
									// Ensure video is loaded before playing
									this.ensureVideoLoaded(video).then(() => {
										video.play().catch(() => {});
									});
								}
							}
						},

						// More lenient visibility check for fast scrolling
						isVideoVisibleLenient(video) {
							const rect = video.getBoundingClientRect();
							const viewportHeight = window.innerHeight;

							// Calculate visible height ratio only (ignore width for mobile)
							const visibleTop = Math.max(rect.top, 0);
							const visibleBottom = Math.min(rect.bottom, viewportHeight);
							const visibleHeight = Math.max(0, visibleBottom - visibleTop);
							const totalHeight = rect.height;
							const visibilityRatio = totalHeight > 0 ? visibleHeight / totalHeight : 0;

							// Check if this is a timeline video
							const isTimelineVideo = video.closest('.publication-video, .cl-plyr-video');

							if (isTimelineVideo) {
								// Timeline videos need at least 20% visibility to be considered visible
								// This ensures they pause very quickly when scrolling out of view
								return visibilityRatio >= 0.2;
							} else {
								// Other videos (shorts) use 50% threshold for more responsive behavior
								return visibilityRatio >= 0.5;
							}
						},

						// ULTRA DATA SAVING: Don't preload timeline videos automatically
						preloadVideo(video) {
							if (video.dataset.preloaded) return; // Already processed

							// Mark as processed to avoid duplicate work
							video.dataset.preloaded = 'true';

							// DON'T initialize HLS or load anything automatically to save data
							// DON'T set preload or src to prevent data consumption
							// DON'T call video.load() to prevent downloading

							console.log(`📱 ULTRA DATA SAVING: Video ${video.id} marked but not loaded to save mobile data`);

							console.log('Preloaded video:', video.id || 'unnamed');
						},

						// Ensure video is loaded and ready to play
						ensureVideoLoaded(video) {
							return new Promise((resolve) => {
								if (video.readyState >= 3) {
									// Video already has enough data
									console.log('📹 Video already loaded, no reload needed:', video.id);
									resolve();
								} else if (video.readyState >= 1) {
									// Video has metadata, just wait for more data without reload
									console.log('📹 Video has metadata, waiting for data without reload:', video.id);
									const onCanPlay = () => {
										video.removeEventListener('canplay', onCanPlay);
										video.removeEventListener('loadeddata', onCanPlay);
										resolve();
									};
									video.addEventListener('canplay', onCanPlay);
									video.addEventListener('loadeddata', onCanPlay);
									setTimeout(resolve, 500); // Timeout fallback
								} else {
									// Video needs initial loading
									console.log('📹 Video needs initial loading:', video.id);
									const onCanPlay = () => {
										video.removeEventListener('canplay', onCanPlay);
										video.removeEventListener('loadeddata', onCanPlay);
										resolve();
									};
									video.addEventListener('canplay', onCanPlay);
									video.addEventListener('loadeddata', onCanPlay);

									// Only trigger load if video has no data at all
									if (video.readyState === 0 && (video.src || video.dataset.src)) {
										if (!video.src && video.dataset.src) {
											video.src = video.dataset.src;
										}
										video.load(); // Only load if absolutely necessary
									}

									// Timeout fallback
									setTimeout(resolve, 500);
								}
							});
						},

						// Check if video is actually visible on screen
						isVideoVisible(video) {
							const rect = video.getBoundingClientRect();
							const viewportHeight = window.innerHeight;
							const viewportWidth = window.innerWidth;

							// Check if video is within viewport bounds
							const isInViewport = (
								rect.top < viewportHeight &&
								rect.bottom > 0 &&
								rect.left < viewportWidth &&
								rect.right > 0
							);

							if (!isInViewport) return false;

							// Calculate visibility ratio
							const visibleTop = Math.max(rect.top, 0);
							const visibleBottom = Math.min(rect.bottom, viewportHeight);
							const visibleLeft = Math.max(rect.left, 0);
							const visibleRight = Math.min(rect.right, viewportWidth);

							const visibleHeight = Math.max(0, visibleBottom - visibleTop);
							const visibleWidth = Math.max(0, visibleRight - visibleLeft);
							const visibleArea = visibleHeight * visibleWidth;
							const totalArea = rect.height * rect.width;

							const visibilityRatio = totalArea > 0 ? visibleArea / totalArea : 0;

							// Require at least 60% visibility
							return visibilityRatio >= 0.6;
						},

						// Pause a specific video - Timeline videos pause immediately
						pauseVideo(video) {
							if (!video) return;

							const isTimelineVideo = video.closest('.publication-video, .cl-plyr-video');

							// Timeline videos should always pause when not visible
							if (isTimelineVideo) {
								video.pause();
								if (video === this.currentVideo) {
									this.currentVideo = null;
								}
								console.log('⏸️ TIMELINE: Paused video immediately:', video.id || 'unnamed');
								return;
							}

							// For non-timeline videos, keep Instagram-style protection during scroll
							if (this.isScrolling) {
								console.log('🛡️ Instagram-style: Skipping pause for non-timeline video during scroll:', video.id);
								return;
							}

							video.pause();
							if (video === this.currentVideo) {
								this.currentVideo = null;
							}
							console.log('Paused video:', video.id || 'unnamed');
						},

						// Pause all videos except the specified one
						pauseAllExcept(exceptVideo) {
							this.allVideos.forEach(video => {
								if (video !== exceptVideo && !video.paused) {
									video.pause();
									console.log('Paused video:', video.id || 'unnamed');
								}
							});
						},

						// Smart video selection with multiple criteria
						getMostVisibleVideo() {
							let bestVideo = null;
							let bestScore = 0;
							const viewportHeight = window.innerHeight;
							const viewportCenter = viewportHeight / 2;

							// Track scroll direction for smarter selection
							if (!this.lastScrollY) this.lastScrollY = window.scrollY;
							const scrollDirection = window.scrollY > this.lastScrollY ? 'down' : 'up';
							this.lastScrollY = window.scrollY;

							const candidates = [];

							this.allVideos.forEach(video => {
								const rect = video.getBoundingClientRect();

								// Skip videos that are completely out of view
								if (rect.bottom <= 0 || rect.top >= viewportHeight) return;

								// Calculate visibility metrics
								const visibleTop = Math.max(rect.top, 0);
								const visibleBottom = Math.min(rect.bottom, viewportHeight);
								const visibleHeight = Math.max(0, visibleBottom - visibleTop);
								const totalHeight = rect.height;
								const visibilityRatio = totalHeight > 0 ? visibleHeight / totalHeight : 0;

								// Only consider videos with meaningful visibility
								if (visibilityRatio < 0.3) return;

								// Calculate video center position
								const videoCenter = rect.top + (rect.height / 2);
								const distanceFromCenter = Math.abs(videoCenter - viewportCenter);
								const centerScore = Math.max(0, 1 - (distanceFromCenter / viewportHeight));

								// Calculate absolute visible area (important for different sized videos)
								const visibleArea = visibleHeight * rect.width;
								const areaScore = visibleArea / (viewportHeight * rect.width);

								// Scroll direction bonus
								let scrollBonus = 0;
								if (scrollDirection === 'down' && rect.top < viewportCenter) {
									scrollBonus = 0.1; // Bonus for video we're scrolling towards
								} else if (scrollDirection === 'up' && rect.bottom > viewportCenter) {
									scrollBonus = 0.1;
								}

								// Current video stability bonus (prevent flickering)
								const stabilityBonus = (video === this.currentVideo) ? 0.15 : 0;

								// Composite score with weighted factors
								const score = (
									visibilityRatio * 0.4 +        // 40% visibility ratio
									centerScore * 0.3 +            // 30% center proximity
									areaScore * 0.2 +              // 20% absolute area
									scrollBonus +                  // 10% scroll direction
									stabilityBonus                 // 15% stability (prevents flicker)
								);

								candidates.push({
									video,
									score,
									visibilityRatio,
									centerScore,
									areaScore,
									rect
								});
							});

							// Sort by score and find the best candidate
							candidates.sort((a, b) => b.score - a.score);

							if (candidates.length > 0) {
								const winner = candidates[0];

								// Additional validation: ensure winner is significantly better than current
								if (this.currentVideo && this.currentVideo !== winner.video) {
									const currentCandidate = candidates.find(c => c.video === this.currentVideo);
									if (currentCandidate && (winner.score - currentCandidate.score) < 0.1) {
										// Scores are too close, keep current video for stability
										return { video: this.currentVideo, visibility: currentCandidate.visibilityRatio };
									}
								}

								// Throttle logging to prevent spam (only log if winner changed or every 5 seconds)
								const now = Date.now();
								const shouldLog = !this.lastLoggedWinner ||
												  this.lastLoggedWinner !== winner.video.id ||
												  (now - (this.lastLogTime || 0)) > 5000;

								if (shouldLog) {
									console.log('Smart video selection:', {
										winner: winner.video.id || 'unnamed',
										score: winner.score.toFixed(3),
										visibility: (winner.visibilityRatio * 100).toFixed(1) + '%',
										candidates: candidates.length
									});
									this.lastLoggedWinner = winner.video.id;
									this.lastLogTime = now;
								}

								return { video: winner.video, visibility: winner.visibilityRatio };
							}

							return { video: null, visibility: 0 };
						},

						// Periodic check to ensure no out-of-view videos are playing
						startPeriodicCheck() {
							if (this.periodicCheckInterval) return;

							this.periodicCheckInterval = setInterval(() => {
								// Check current video visibility
								if (this.currentVideo && !this.isVideoVisibleLenient(this.currentVideo)) {
									const isTimelineVideo = this.currentVideo.closest('.publication-video, .cl-plyr-video');
									if (isTimelineVideo) {
										// Timeline videos pause immediately regardless of scroll state
										console.log('⏰ PERIODIC: Timeline video no longer visible, pausing:', this.currentVideo.id || 'unnamed');
										this.pauseVideo(this.currentVideo);
									} else if (!this.isScrolling) {
										// Other videos only pause when not scrolling
										console.log('⏰ PERIODIC: Current video no longer visible, pausing:', this.currentVideo.id || 'unnamed');
										this.pauseVideo(this.currentVideo);
									}
								}

								// Check for any playing videos that shouldn't be
								this.allVideos.forEach(video => {
									if (!video.paused && video !== this.currentVideo && !this.isVideoVisibleLenient(video)) {
										const isTimelineVideo = video.closest('.publication-video, .cl-plyr-video');
										if (isTimelineVideo || !this.isScrolling) {
											console.log('⏰ PERIODIC: Found unauthorized playing video, pausing:', video.id || 'unnamed');
											video.pause();
										}
									}
								});
							}, 500); // Check every 500ms for more responsive timeline video pausing
						},

						// Stop periodic check
						stopPeriodicCheck() {
							if (this.periodicCheckInterval) {
								clearInterval(this.periodicCheckInterval);
								this.periodicCheckInterval = null;
							}
						}
					};

					// Start the periodic check
					window.__unifiedVideoManager.startPeriodicCheck();

					// Add global scroll listener to handle visibility changes
					let lastScrollCheck = 0;
					window.addEventListener('scroll', () => {
						const manager = window.__unifiedVideoManager;
						manager.isScrolling = true;

						// Clear existing timeout
						clearTimeout(manager.scrollTimeout);

						// IMMEDIATE check for timeline videos - no throttling or delays
						if (manager.currentVideo) {
							const isTimelineVideo = manager.currentVideo.closest('.publication-video, .cl-plyr-video');
							if (isTimelineVideo) {
								const currentVisibility = manager.isVideoVisibleLenient(manager.currentVideo);
								if (!currentVisibility) {
									// Timeline videos pause IMMEDIATELY when not visible
									manager.pauseVideo(manager.currentVideo);
									console.log('⚡ IMMEDIATE: Timeline video paused during scroll:', manager.currentVideo.id || 'unnamed');
								}
							}
						}

						// Throttle other checks to prevent excessive processing
						const now = Date.now();
						if (now - lastScrollCheck < 50) return; // Max 20 checks per second
						lastScrollCheck = now;

						// Immediate response for better UX during scroll
						requestAnimationFrame(() => {
							const result = manager.getMostVisibleVideo();

							// Only switch if there's a clear winner with good visibility
							if (result.video && result.video !== manager.currentVideo) {
								if (result.visibility >= 0.4) { // Slightly lower threshold for scroll
									manager.playVideo(result.video, 'timeline', result.visibility);
								}
							} else if (!result.video && manager.currentVideo) {
								// Check if current video is timeline video for immediate pause
								const isTimelineVideo = manager.currentVideo.closest('.publication-video, .cl-plyr-video');
								const currentVisibility = manager.isVideoVisibleLenient(manager.currentVideo);

								if (isTimelineVideo && !currentVisibility) {
									// Timeline videos pause immediately when not visible
									manager.pauseVideo(manager.currentVideo);
									console.log('⏸️ SCROLL: Timeline video paused (not visible):', manager.currentVideo.id || 'unnamed');
								} else if (!isTimelineVideo && !currentVisibility) {
									// Other videos pause only when really not visible
									manager.pauseVideo(manager.currentVideo);
								}
							}
						});

						// Additional check for timeline videos during scroll
						// Timeline videos should pause immediately when out of view, even during scroll
						if (manager.currentVideo) {
							const isTimelineVideo = manager.currentVideo.closest('.publication-video, .cl-plyr-video');
							if (isTimelineVideo && !manager.isVideoVisibleLenient(manager.currentVideo)) {
								manager.pauseVideo(manager.currentVideo);
								console.log('⚡ SCROLL: Timeline video paused immediately during scroll:', manager.currentVideo.id || 'unnamed');
							}
						}

						// Mark scrolling as finished after delay
						manager.scrollTimeout = setTimeout(() => {
							manager.isScrolling = false;
							// Final authoritative check after scrolling stops
							const result = manager.getMostVisibleVideo();
							if (result.video && result.video !== manager.currentVideo && result.visibility >= 0.3) {
								manager.playVideo(result.video, 'timeline', result.visibility);
							}
						}, 200); // Slightly longer delay for final decision
					}, { passive: true });

					// Add ultra-aggressive scroll handler specifically for timeline videos
					// This bypasses all throttling and delays for immediate pausing
					window.addEventListener('scroll', () => {
						const manager = window.__unifiedVideoManager;
						if (!manager || !manager.currentVideo) return;

						const isTimelineVideo = manager.currentVideo.closest('.publication-video, .cl-plyr-video');
						if (isTimelineVideo && !manager.currentVideo.paused) {
							// Check visibility immediately without any delays
							const rect = manager.currentVideo.getBoundingClientRect();
							const viewportHeight = window.innerHeight;
							const visibleTop = Math.max(rect.top, 0);
							const visibleBottom = Math.min(rect.bottom, viewportHeight);
							const visibleHeight = Math.max(0, visibleBottom - visibleTop);
							const totalHeight = rect.height;
							const visibilityRatio = totalHeight > 0 ? visibleHeight / totalHeight : 0;

							// Pause immediately if less than 20% visible
							if (visibilityRatio < 0.2) {
								manager.currentVideo.pause();
								manager.currentVideo = null;
								console.log('⚡ ULTRA-IMMEDIATE: Timeline video paused at', Math.round(visibilityRatio * 100) + '% visibility');
							}
						}
					}, { passive: true });

					// Add global play event listener to catch any unauthorized video plays
					document.addEventListener('play', (event) => {
						if (event.target.tagName === 'VIDEO') {
							const video = event.target;
							if (window.__unifiedVideoManager.allVideos.has(video)) {
								// Skip unauthorized play detection if shorts are active
								if (window.__shortsActive) {
									console.log('Skipping unauthorized play detection - shorts active');
									return;
								}

								// If this video is not the current one, pause it
								if (video !== window.__unifiedVideoManager.currentVideo) {
									console.log('Unauthorized video play detected, pausing:', video.id || 'unnamed');
									video.pause();
								}
							}
						}
					}, true);
				}

				// Legacy observer for backward compatibility
				window.__reelsIO = {
					observe: (video) => window.__unifiedVideoManager.registerVideo(video, 'timeline')
				};

				// Iterate over every video element (legacy class="plyr" kept)
				document.querySelectorAll("video.plyr, .cl-plyr-video video").forEach(function(el) {
					const $el = $(el);
					if ($el.data("reels-ready")) return; // only once per element
					$el.data("reels-ready", true);

					// Basic settings
					el.muted = window.__globalMute;
					el.playsInline = true;
					el.loop = true;
					el.controls = false;

					// Single tap/click => mute toggle, double-click => fullscreen
					let lastTap = 0;
					el.addEventListener("click", (ev) => {
						const now = Date.now();
						if (now - lastTap < 300) {
							toggleFullscreen(el);
							lastTap = 0;
							return;
						}
						lastTap = now;
						// global mute toggle
						window.toggleGlobalMute(!window.__globalMute);
						if (el.paused) lazyLoadAndPlay(el);
					});

					// When entering fullscreen we want native controls, when exiting hide them
					el.addEventListener("fullscreenchange", updateControls);
					el.addEventListener("webkitfullscreenchange", updateControls);
					function updateControls(){
						const fs = document.fullscreenElement || document.webkitFullscreenElement;
						if(fs === el){
							el.controls = true;
						} else {
							el.controls = false;
						}
					}

					function toggleFullscreen(video){
						if(document.fullscreenElement || document.webkitFullscreenElement){
							document.exitFullscreen ? document.exitFullscreen() : document.webkitExitFullscreen();
						} else {
							(video.requestFullscreen ? video.requestFullscreen() : video.webkitRequestFullscreen()).catch(()=>{});
						}
					}

					// Detect orientation after metadata loads
					el.addEventListener("loadedmetadata", () => {
						if (el.videoWidth > el.videoHeight) {
							el.classList.add("landscape");
						} else {
							el.classList.remove("landscape");
						}
					});

					// Observe visibility (always attach to <video> itself)
					window.__reelsIO.observe(el);
				});

				function lazyLoadAndPlay(video) {
					// Simple autoplay - don't interfere with shorts viewer
					const isShorts = video.closest('#shortsViewer, .shorts-container');
					if (isShorts) {
						console.log('📹 Shorts video - simple play:', video.id);
						video.play().catch(() => {});
						return;
					}

					console.log('📹 Timeline video play:', video.id);

					// Only for timeline videos - set poster as background
					if (video.poster) {
						const container = video.closest('.cl-plyr-video, .publication-video');
						if (container) {
							container.style.backgroundImage = `url(${video.poster})`;
							container.style.backgroundSize = 'cover';
							container.style.backgroundPosition = 'center';
							container.style.backgroundRepeat = 'no-repeat';
						}
					}

					// Ensure video has proper autoplay attributes
					video.muted = window.__globalMute !== false;
					video.playsInline = true;
					video.loop = true;

					// Lazy source assignment for data-src support
					if (!video.dataset.reelsLoaded) {
						const src = video.dataset.src || video.getAttribute("data-src");
						if (src && !video.src) {
							video.src = src;
							console.log('📹 Set source:', video.id);
						}
						video.dataset.reelsLoaded = "1";
					}

					// Initialize HLS if needed and not already done
					if (video.classList.contains('lazy-hls') && !video._hlsInitialized && window.initializeHLSVideos) {
						console.log('📹 Initializing HLS for timeline video:', video.id);
						window.initializeHLSVideos(`#${video.id}`, true);
					}

					// Simple play with retry logic
					const attemptPlay = () => {
						video.play().then(() => {
							console.log('📹 Playing:', video.id);
						}).catch((error) => {
							console.log('📹 Play failed:', video.id, error.message);
							// Retry once after a short delay if it's an interaction issue
							if (error.name === 'NotAllowedError') {
								setTimeout(() => {
									video.play().catch(() => console.log('📹 Retry failed:', video.id));
								}, 100);
							}
						});
					};

					// If video is ready, play immediately, otherwise wait for it to load
					if (video.readyState >= 2) {
						attemptPlay();
					} else {
						video.addEventListener('loadeddata', attemptPlay, { once: true });
						// Trigger loading if needed
						if (video.load && !video.src) {
							video.load();
						}
					}
				}
			}

			return _smc;
		} 

		if (window.SMColibri === undefined) {
			window.SMColibri = _SMColibri();
		}
	})(window);

	$(document).ready(function($) {

	    SMColibri.init();

	    // Initialize timeline video autoplay
	    function initializeTimelineAutoplay() {
	        console.log('🎬 Initializing timeline video autoplay...');

	        const timelineVideos = document.querySelectorAll('.publication-video video, .cl-plyr-video video');
	        console.log('🎬 Found', timelineVideos.length, 'timeline videos');

	        timelineVideos.forEach((video, index) => {
	            // Ensure autoplay attributes are set
	            if (!video.hasAttribute('autoplay')) {
	                video.setAttribute('autoplay', '');
	                console.log('🎬 Added autoplay attribute to:', video.id);
	            }
	            if (!video.hasAttribute('muted')) {
	                video.setAttribute('muted', '');
	                console.log('🎬 Added muted attribute to:', video.id);
	            }

	            // Set up proper attributes for autoplay
	            video.muted = window.__globalMute !== false;
	            video.playsInline = true;
	            video.loop = true;

	            // Register with unified video manager if not already registered
	            if (window.__unifiedVideoManager && !window.__unifiedVideoManager.allVideos.has(video)) {
	                window.__unifiedVideoManager.registerVideo(video, 'timeline');
	                console.log('🎬 Registered video with unified manager:', video.id);
	            }
	        });

	        // Trigger immediate autoplay check
	        setTimeout(() => {
	            if (window.__unifiedVideoManager) {
	                const result = window.__unifiedVideoManager.getMostVisibleVideo();
	                if (result.video && result.visibility >= 0.3) {
	                    window.__unifiedVideoManager.playVideo(result.video, 'timeline', result.visibility);
	                    console.log('🎬 Started autoplay for visible video:', result.video.id);
	                }
	            }
	        }, 500);
	    }

	    // Initialize on page load
	    initializeTimelineAutoplay();

	    // Re-initialize when new content is loaded (SPA navigation)
	    window.addEventListener('vlixContentUpdated', initializeTimelineAutoplay);

	    var clipboard   = new ClipboardJS('.clip-board-copy');
	    var page_height = $(document).height();

	    clipboard.on('success', function(event) {
	    	cl_bs_notify("<?php echo cl_translate("Copied to your clipboard!"); ?>", 3000, "info");
	    });

	    clipboard.on('error', function(event) {
	    	cl_bs_notify("<?php echo cl_translate("Failed to copy to clipboard!"); ?>", 3000, "info");
	    });

	    if (navigator.cookieEnabled == false) {
	    	$('[data-app="announcement"]').html("<div class='announcement-danger'><?php echo cl_translate('Oops! It looks like you have cookies disabled. For this site to work properly, you need to enable cookies.'); ?></div>");
	    }

	    $(document).on('click', 'div[data-app="lsb-back-drop"]', function(event) {
    		event.preventDefault();
    		SMColibri.toggleSB();
    	});

    	SMColibri.update_plyr();

	    setInterval(function() {

	    	var new_page_height = $(document).height();
	    	var _lozad_         = lozad();

			_lozad_.observe();

	    	if (page_height != new_page_height) {
	    		page_height = new_page_height;
	    		SMColibri.fix_sidebars();
		    	SMColibri.update_ilb();			
		    	SMColibri.update_plyr();		
			}
	    }, 500);

	    $("div.publication-text").readmore({
	    	moreLink: '<span class="readmore-toggle"><?php echo cl_translate("Read more"); ?></span>',
	    	lessLink: '<span class="readmore-toggle"><?php echo cl_translate("Read less"); ?></span>'
	    });

	    $(document).on('click tap', 'a[data-spa]', function(event) {
	    	// EXCLUDE: Don't intercept comment links in shorts viewer
	    	if ($(this).closest('#shortsViewer, .shorts-container, .video-container, .video-player-container').length > 0 &&
	    	    $(this).attr('href') && $(this).attr('href').includes('/thread/')) {
	    		console.log('🔍 SPA: Skipping comment link in shorts viewer');
	    		return true; // Let the shorts comment handler deal with it
	    	}

			event.preventDefault();

			var page_url_source = $(this).attr('href');

			SMColibri.spa_load(page_url_source);

			return false;
		});

		window.addEventListener("popstate", function (event) {

			if (event.state) {
				if ($.isEmptyObject(event.state.back_url) != true) {
					SMColibri.spa_load(event.state.back_url, false);
				}
			}

			return false;
		});

		$(document).on('click tap', '[data-href]', function(event) {
			event.preventDefault();
			event.stopPropagation();

			window.open($(this).data('href'), '_blank');

			return false;
		});

		if (window.mobileCheck() != true) {
			$(document).on('mouseenter', '[data-uinfo-lbox]', function(event) {
				event.preventDefault();

				var uid = $(this).data("uinfo-lbox");

				SMColibri.user_lbox(uid, $(this));
			});

			$(document).on('mouseleave', '[data-uinfo-lbox]', function(event) {
				event.preventDefault();

				if (cl_empty(SMColibri.userlbox.status)) {
					clearTimeout(SMColibri.userlbox.pre_delay);

					SMColibri.userlbox.pre_delay = false;
				}

				if (cl_empty(SMColibri.userlbox.interv) != true) {
					clearInterval(SMColibri.userlbox.interv);
				}

				SMColibri.userlbox.interv = setInterval(function() {
					if (SMColibri.userlbox.lifetm !== "pause" && $.isNumeric(SMColibri.userlbox.lifetm)) {
						if (SMColibri.userlbox.lifetm <= 0) {
							SMColibri.user_lbox_rm();

							clearInterval(SMColibri.userlbox.interv);
						}
						else{
							SMColibri.userlbox.lifetm -= 100;
						}
					}
				}, 100);
			});

			$(document).on('mouseenter', "[data-app='user-info-lbox']", function() {
				SMColibri.userlbox.lifetm = "pause";
			});

			$(document).on('mouseleave', "[data-app='user-info-lbox']", function() {
				SMColibri.userlbox.lifetm = 1000;
			});
		}
	});
</script>