<script>
	"use strict";

	$(document).ready(function($) {
		var _app  = $('[data-app="edit-user-perms"]');
		var _data = _app.find('form[data-an="form"]');

		_data.ajaxForm({
			url: '<?php echo cl_link("native_api/cpanel/save_moder_settings"); ?>',
			type: 'POST',
			dataType: 'json',
			data: {
				user_id: "<?php echo $cl["moder_data"]["id"]; ?>"
			},
			beforeSend: function() {
				_data.find('small.invalid-feedback').remove();
				_data.find('[data-an="submit-ctrl"]').attr('disabled', 'true').text("Please wait");
			},
			success: function(data) {
				if (data.status == 200) {
					cl_bs_notify("Your changes have been saved successfully!", 3000);
				}
				else {
					if (data.message) {
						alert(data.message);
					}
					else{
						alert("Something went wrong while trying to save user balance. Please check your details or try again later");
					}
				}
			},
			complete: function() {
				_data.find('[data-an="submit-ctrl"]').removeAttr('disabled').text("Save changes");
			}
		});
	});
</script>