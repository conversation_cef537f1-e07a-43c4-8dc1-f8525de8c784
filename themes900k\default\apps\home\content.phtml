<div class="timeline-container" data-app="homepage">
	<div class="timeline-header" data-el="tl-header">
		<div class="timeline-header__botline">
			<div class="lp">
				<div class="nav-link-holder">
					<a href="<?php echo cl_link("/"); ?>" data-spa="true">
						<?php echo cl_translate("Home"); ?>
					</a>
				</div>
			</div>
			<div class="cp">
				<a href="<?php echo cl_link("/"); ?>">
					<img src="{%config site_logo%}" alt="Logo">
				</a>
			</div>
			<div class="rp">
				<div class="nav-link-holder">
					<button class="go-options dropleft">
						<div class="dropdown-toggle icon go-options__icon" data-toggle="dropdown">
							<?php echo cl_ficon('more_horiz'); ?>
						</div>
						<div class="dropdown-menu dropdown-icons">
							<a class="dropdown-item" href="<?php echo cl_link("feed_preferences"); ?>" data-spa="true">
								<span class="flex-item dropdown-item-icon">
									<?php echo cl_ficon('news'); ?>
								</span>
								<span class="flex-item">
									<?php echo cl_translate("Feed preferences"); ?>
								</span>
								<span class="flex-item dropdown-item-icon dropdown-item-icon_adinfo">
									<?php echo cl_ficon('open'); ?>
								</span>
							</a>
						</div>
					</button>
				</div>
			</div>
		</div>
	</div>
	<div class="homepage">
		

		<?php if ($cl["config"]["swift_system_status"] == "on"): ?>
			<div class="homepage__swifts">
				<div class="swifts-slider">
					<div class="swiper" id="homepage-swifts-slider">
						<div class="swiper-wrapper">
							<div class="swiper-slide">
								<div class="swift-item" onclick="SMColibri.PS.new_swift.add_swift();">
									<div class="swift-item__body create-swift">
										<div class="swift-item__avatar">
											<img src="<?php echo($me['avatar']); ?>" alt="Avatar">
										</div>
										<span class="add-ikon">
											<?php echo cl_ficon("add"); ?>
										</span>
									</div>
									<div class="swift-item__footer">
										<?php echo cl_translate("Create new swift"); ?>
									</div>
								</div>
							</div>

							<?php if (not_empty($cl["tl_swifts"])): ?>
								<?php foreach ($cl["tl_swifts"] as $swift_id => $swift_data): ?>
									<div class="swiper-slide">
										<div data-an="swift-item-<?php echo($swift_data['id']); ?>" class="swift-item <?php if(not_empty($swift_data['has_unseen'])) {echo('active');} ?>" onclick="SMColibri.PS.play_swift.show('<?php echo($swift_id); ?>');">
											<div class="swift-item__body">
												<div class="swift-item__avatar">
													<img src="<?php echo($swift_data['avatar']); ?>" alt="Avatar">
												</div>
											</div>
											<div class="swift-item__footer">
												<?php if (not_empty($swift_data["is_user"])): ?>
													<?php echo cl_translate("Your swifts"); ?>
												<?php else: ?>
													<?php echo($swift_data['name']); ?>
												<?php endif; ?>
											</div>
										</div>
									</div>
								<?php endforeach; ?>
							<?php endif; ?>
						</div>
					</div>
					<div class="swifts-slider__footer">
						<div class="swiper-scrollbar" id="homepage-swifts-scrollbar"></div>
					</div>
				</div>
			</div>
		<?php endif; ?>
	</div>
	<div class="timeline-posts-container">
		<?php if (not_empty($cl["tl_feed"])): ?>
			<?php if (not_empty($cl["admin_pinned_post"])): ?>
				<div class="timeline-posts-ls">
					<?php 
						$cl['li'] = $cl["admin_pinned_post"];
						
						echo cl_template('timeline/post');
					?>
				</div>
			<?php endif; ?>
			<div class="timeline-posts-container">
				<?php $count = 0; ?>
				<?php if (not_empty($cl["tl_feed"])): ?>
						<div class="timeline-posts-ls" data-an="entry-list">
							<?php foreach ($cl["tl_feed"] as $cl["li"]): ?>
									<?php $count++; ?>
									<?php echo cl_template('timeline/post'); ?>
									<?php if ($count == 4) {
										echo cl_template('home/follow');
									} ?>
							<?php endforeach; ?>
						</div>
						<?php endif; ?>
						</div>
		<?php else: ?>
			<?php echo cl_template('home/includes/no_posts'); ?>
		<?php endif; ?>
	</div>

	<?php if ($cl["config"]["swift_system_status"] == "on"): ?>
		<?php echo cl_template("home/modals/swift_lightbox"); ?>
		<?php echo cl_template("home/modals/swift_pubbox"); ?>
	<?php endif; ?>

	<?php echo cl_template("main/includes/inline_statics/app_statics"); ?>
	<?php echo cl_template("home/scripts/app_master_script"); ?>
</div>
<!-- MDB -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.2.0/mdb.min.css" rel="stylesheet" />
<!-- MDB -->

<script>
	// Initialise Carousel
	const cardSlider = new Carousel(document.querySelector("#cardSlider"), {
		Dots: false,
	})
	const cardSlider2 = new Carousel(document.querySelector("#cardSlider2"), {
		Dots: false,
	})
</script>
<script>
	"use strict";

	var offset = 30;

	jQuery(document).ready(function ($) {
		var _app = $('div[data-app="homepage"]');
		var feed_ls = _app.find('[data-an="entry-list"]');

		_app.find('button[data-an="load-more"]').on('click', function (event) {
			event.preventDefault();

			var _self = $(this);


			$.ajax({
				url: "<?php echo cl_link("native_api/home/<USER>"); ?>",
				type: 'GET',
				dataType: 'json',
				data: {
					offset: offset,
				},
				beforeSend: function () {
					_self.attr('disabled', 'true').text("<?php echo cl_translate("Please wait"); ?>");
				}
			}).done(function (data) {
				if (data.status == 200) {
					feed_ls.append(data.html);

					_self.removeAttr('disabled').text("<?php echo cl_translate("Show more"); ?>");

					offset += 30;
				}

				else {
					_self.text("<?php echo cl_translate("That is all for now!"); ?>");
				}
			});
		});
	});
</script>