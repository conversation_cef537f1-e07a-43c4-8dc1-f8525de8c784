<div class="notifications-list__item" data-list-item="<?php echo($cl['li']['id']); ?>">
	<div class="notif-data">
		<div class="notifications-list__item-avatar">
			<a href="<?php echo($cl['li']['user_url']); ?>" data-spa="true" class="block-link">
				<div class="user-avatar" style="width: 45px; height: 45px;">
					<img src="<?php echo($cl['li']['avatar']); ?>" alt="Avatar">
				</div>
			</a>
		</div>
		<div class="notifications-list__item-data" style="margin-top: 3px; margin-left: 5px;">
			<div class="notifications-data">
				<div class="notifications-data__body">
					<a href="<?php echo($cl['li']['url']); ?>" data-spa="true">
						<div class="notifications-data__topline">
							<span class="user-name-holder">
								<span class="user-name-holder__name">
									<?php echo $cl['li']['name']; ?>
								</span>

								<?php if ($cl['li']['verified'] == '1'): ?>
									<span class="user-name-holder__badge">
										<?php echo cl_icon("verified_user_badge"); ?>
									</span>
								<?php endif; ?>
							</span>
							<span class="notif-text <?php if($cl['li']['status'] == '0') {echo('unseen');}; ?>">
								<?php if ($cl['li']['subject'] == 'verified'): ?>
									<?php echo cl_translate('Your verification request has been accepted'); ?>
								<?php elseif ($cl['li']['subject'] == 'reply'): ?>
									<div style="position:absolute;  margin-top: 5px; margin-left: -32px;"><img src="/themes/default/statics/img/comments.png" width="24" height="24"></div>

									
									<?php echo cl_translate('Replied to your post'); ?>
								<?php elseif($cl['li']['subject'] == 'subscribe'): ?>
									<div style="position:absolute;  margin-top: 5px; margin-left: -32px;"><img src="/themes/default/statics/img/repost.png" width="24" height="24"></div>
											
									<?php echo cl_translate('Started following you'); ?>
								<?php elseif($cl['li']['subject'] == 'subscribe_request'): ?>
									<div style="position:absolute;  margin-top: 5px; margin-left: -32px;"><img src="/themes/default/statics/img/user_add.png" width="24" height="24"></div>
													
									<?php echo cl_translate('Wants to follow you'); ?>
								<?php elseif($cl['li']['subject'] == 'subscribe_accept'): ?>
									<div style="position:absolute;  margin-top: 5px; margin-left: -32px;"><img src="/themes/default/statics/img/user_add3.png" width="24" height="24"></div>
											
									<?php echo cl_translate('Accepted your follow request'); ?>
								<?php elseif($cl['li']['subject'] == 'mention'): ?>
									<div style="position:absolute;  margin-top: 5px; margin-left: -32px;"><img src="/themes/default/statics/img/ment.png" width="24" height="24"></div>
											
									<?php echo cl_translate('Mentioned you in a post'); ?>
								<?php elseif($cl['li']['subject'] == 'like'): ?>
									<div style="position:absolute;  margin-top: 5px; margin-left: -32px;"><img src="/themes/default/statics/img/like.png" width="24" height="24"></div>

									
									<?php echo cl_translate('Liked your post'); ?>
								<?php elseif($cl['li']['subject'] == 'repost' ): ?>
									<div style="position:absolute; margin-top: 5px; margin-left: -32px;"><img src="/themes/default/statics/img/repost.png" width="24" height="24"></div>


									<?php echo cl_translate('Shared your publication'); ?>
								<?php elseif($cl['li']['subject'] == 'visit'): ?>
									
									<?php echo cl_translate('Visited your profile'); ?>
								<?php elseif($cl['li']['subject'] == 'ad_approval'): ?>
									<div style="position:absolute;  margin-top: 5px; margin-left: -32px;"><img src="/themes/default/statics/img/ad.png" width="24" height="24"></div>
													
									<?php echo cl_translate('Your add has been approved'); ?>
								<?php elseif($cl['li']['subject'] == 'content_subscription'): ?>
									
									<?php echo cl_translate('Subscribed to your content'); ?>
								<?php elseif($cl['li']['subject'] == 'wallet_local_receipt'): ?>
										
									<?php echo cl_translate('Transferred {@trans_amount@} to your wallet', array(
										"trans_amount" => cl_html_el("b", $cl['li']['json']["trans_amount"])
									)); ?>
								<?php endif; ?>
							</span>
						</div>
						<div class="notifications-data__midline">
							<span class="notif-time">
								<?php echo $cl['li']['time']; ?>
							</span>
						</div>
					</a>
				</div>
				<div class="notifications-data__ctrls">
					<div class="notif-ctrls">
						<div class="notif-ctrls__delete">
							<button type="button" class="btn-custom-icon col-red" onclick="SMColibri.PS.notifs.delete_notif(<?php echo($cl['li']['id']); ?>);">
								<?php echo cl_ficon("delete"); ?>
							</button>
						</div>
					</div>
				</div>
			</div>
			<?php if($cl['li']['subject'] == 'subscribe_request'): ?>
				<div class="notif-controls">
					<button onclick="SMColibri.PS.notifs.accept_follow(<?php echo $cl['li']['json']["follow_request_id"]; ?>, <?php echo($cl['li']['id']); ?>);" class="btn btn-custom main-inline sm">
						<?php echo cl_translate("Accept request"); ?>
					</button>
				</div>
			<?php endif; ?>
		</div>
	</div>
</div>