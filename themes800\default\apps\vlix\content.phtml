<!-- Vlix: Immediate Shorts Viewer -->
<div id="vlix-container" style="display: none;">
	<h3>Loading Vlix...</h3>
</div>

<script>
// Immediately open shorts viewer when Vlix page loads
document.addEventListener('DOMContentLoaded', function() {
	console.log('🎬 VLIX: Page loaded, opening shorts immediately...');

	// Enable sound globally first
	if (window.toggleGlobalMute) {
		window.toggleGlobalMute(false);
		console.log('🔊 VLIX: Enabled sound globally');
	}

	// Open shorts viewer immediately without needing timeline videos
	setTimeout(function() {
		if (window.openOverlay) {
			console.log('🎬 VLIX: Opening shorts viewer directly...');

			// Open shorts without initial video - it will load from API
			window.openOverlay(null);
		} else {
			console.log('🎬 VLIX: openOverlay not available, retrying...');
			// Retry after a short delay
			setTimeout(function() {
				if (window.openOverlay) {
					window.openOverlay(null);
				}
			}, 500);
		}
	}, 100); // Very short delay
});
</script>
