<div class="timeline-container">
	<div class="timeline-header" data-el="tl-header">
		<div class="timeline-header__botline">
			<div class="lp">
				<div class="nav-link-holder">
					<a href="<?php echo cl_link("settings/information"); ?>" data-spa="true">
						<?php echo cl_translate("Your information"); ?>
					</a>
				</div>
			</div>
			<div class="cp">
				<a href="<?php echo cl_link("home"); ?>">
					<img src="{%config site_logo%}" alt="Logo">
				</a>
			</div>
			<div class="rp">
				<div class="nav-link-holder">
					<span class="go-back" onclick="SMColibri.go_back();">
						<?php echo cl_ficon('arrow_back'); ?>
					</span>
				</div>
			</div>
		</div>
	</div>
	<div class="profile-settings">
		<div class="profile-settings__content">
			<div class="settings-form">
				<form class="form" id="cl-account-info-vue-app" v-on:submit="submit_form($event)">
					<div class="form-group no-mb">
						<div class="form-toggle">
                            <div class="form-toggle__lp">
                                <?php echo cl_icon('information'); ?>
                            </div>
                            <div class="form-toggle__mp">
                                <span class="form-toggle__title">
                                    <?php echo cl_translate("My information"); ?>
                                </span>
                                <span class="form-toggle__desc">
                                    <?php echo cl_translate("My information"); ?>
                                </span>
                            </div>
                            <div class="form-toggle__rp">
                                <div class="form-check">
                                	<input v-model="form_data.my_info" type="checkbox" class="form-check-input">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group no-mb">
                    	<div class="form-toggle">
                            <div class="form-toggle__lp">
                                <?php echo cl_ficon('people_swap'); ?>
                            </div>
                            <div class="form-toggle__mp">
                                <span class="form-toggle__title">
                                    <?php echo cl_translate("Followings"); ?>
                                </span>
                                <span class="form-toggle__desc">
                                    <?php echo cl_translate("List of people I follow"); ?>
                                </span>
                            </div>
                            <div class="form-toggle__rp">
                                <div class="form-check">
                                	<input v-model="form_data.following" type="checkbox" class="form-check-input">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group no-mb">
                    	<div class="form-toggle">
                            <div class="form-toggle__lp">
                                <?php echo cl_ficon('people'); ?>
                            </div>
                            <div class="form-toggle__mp">
                                <span class="form-toggle__title">
                                    <?php echo cl_translate("Followers"); ?>
                                </span>
                                <span class="form-toggle__desc">
                                    <?php echo cl_translate("List of people who follow me"); ?>
                                </span>
                            </div>
                            <div class="form-toggle__rp">
                                <div class="form-check">
                                	<input v-model="form_data.followers" type="checkbox" class="form-check-input">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group no-mb">
                    	<div class="form-toggle">
                            <div class="form-toggle__lp">
                                <?php echo cl_ficon('note'); ?>
                            </div>
                            <div class="form-toggle__mp">
                                <span class="form-toggle__title">
                                    <?php echo cl_translate("Posts"); ?>
                                </span>
                                <span class="form-toggle__desc">
                                    <?php echo cl_translate("List of my publications and replies"); ?>
                                </span>
                            </div>
                            <div class="form-toggle__rp">
                                <div class="form-check">
                                	<input v-model="form_data.posts" type="checkbox" class="form-check-input">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                    	<div class="form-toggle">
                            <div class="form-toggle__lp">
                                <?php echo cl_ficon('bookmark'); ?>
                            </div>
                            <div class="form-toggle__mp">
                                <span class="form-toggle__title">
                                    <?php echo cl_translate("Saved posts"); ?>
                                </span>
                                <span class="form-toggle__desc">
                                    <?php echo cl_translate("List of my bookmarks"); ?>
                                </span>
                            </div>
                            <div class="form-toggle__rp">
                                <div class="form-check">
                                	<input v-model="form_data.bookmarks" type="checkbox" class="form-check-input">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group padding-x-offset">
                        <div class="form-info-label">
                            <?php echo cl_translate("Check the boxes in the options to select the account information you want to download"); ?>
                        </div>
                    </div>
                    <div class="form-group no-mb padding-x-offset d-flex">
                    	<button v-if="submitting != true" v-bind:disabled="is_invalid_form" type="submit" class="ml-auto btn btn-custom main-inline lg">
	                        <?php echo cl_translate("Download info"); ?>
	                    </button>
	                    <button v-else disabled="true" type="button" class="ml-auto btn btn-custom main-inline lg">
	                        <?php echo cl_translate("Please wait"); ?>
	                    </button>
                    </div>
                </form>
			</div>
		</div>
	</div>

	<script>
		"use strict";

		$(document).ready(function($) {
			Vue.use(window.vuelidate.default);
			var VueValids = window.validators;

			new Vue({
				el: "#cl-account-info-vue-app",
				data: {
					submitting: false,
					unsuccessful_attempt: false,
					form_data: {
						my_info: false,
						following: false,
						followers: false,
						posts: false,
						bookmarks: false
					}
				},
				computed: {
					is_invalid_form: function() {
						var _app_  = this;
						var fields = Object.values(_app_.form_data);
						
						return ((fields.contains(true)) ? false : true);
					}
				},
				methods: {
					submit_form: function(_self = null) {
						_self.preventDefault();

						var _app_ = this;

						$(_self.target).ajaxSubmit({
							url: "<?php echo cl_link("native_api/settings/download_profile_info"); ?>",
							type: 'POST',
							dataType: 'json',
							data: {
								my_info: ((_app_.form_data.my_info == true) ? "Y" : "N"),
								following: ((_app_.form_data.following == true) ? "Y" : "N"),
								followers: ((_app_.form_data.followers == true) ? "Y" : "N"),
								posts: ((_app_.form_data.posts == true) ? "Y" : "N"),
								bookmarks: ((_app_.form_data.bookmarks == true) ? "Y" : "N")
							},
							beforeSend: function() {
								_app_.submitting = true;
							},
							success: function(data) {
								if (data.status == 200) {
									_app_.reset_form();

									cl_redirect(data.url, true);
								}

								else {
									_app_.unsuccessful_attempt = true;
									_app_.submitting = false;
								}
							}
						});
					},
					reset_form: function() {
						var _app_ = this;
						_app_.submitting = false;
						_app_.unsuccessful_attempt = false;
						_app_.form_data = Object({
							my_info: false,
							following: false,
							followers: false,
							posts: false,
							bookmarks: false
						});
					}
				}
			});
		});
	</script>
</div>