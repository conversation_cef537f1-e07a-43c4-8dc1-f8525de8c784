<div class="cp-app-container" data-app="user-ads">
    <div class="current-page-name">
        <div class="lp">
            <h1>
                User ads
            </h1>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        Manage user ads
                    </h2>
                </div>
                <div class="body">
                    <div class="regular-table">
                        <table class="table table-hover no-mb">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>
                                        Username
                                    </th>
                                    <th>
                                        Target URL
                                    </th>
                                    <th>
                                        Budget
                                    </th>
                                    <th>
                                        Clicks
                                    </th>
                                    <th>
                                        Views
                                    </th>
                                    <th>
                                        Status
                                    </th>
                                    <th>
                                        Approved
                                    </th>
                                    <th>
                                        Date
                                    </th>
                                    <th>
                                        Action
                                    </th>
                                </tr>
                            </thead>
                            <tbody data-an="ads-list">
                                <?php if (not_empty($cl['user_ads'])): ?>
                                    <?php foreach ($cl['user_ads'] as $cl['li']): ?>
                                        <?php echo cl_template('cpanel/assets/manage_ads/includes/list_item'); ?>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="8">
                                            <div class="empty-table">
                                                <h4><?php echo('No ads found'); ?></h4>
                                                <p>
                                                    <?php echo('It looks like there are no ads in your database yet. All advertisements of your website users will be displayed here.'); ?>
                                                </p>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                        <div class="table-pagination">
                            <?php if (not_empty($cl['user_ads'])): ?>
                                <a onclick="SMC_CPanel.PS.paginate(this);" data-dir="prev" href="javascript:void(0);" class="pagination-ctrls prev">
                                    <?php echo cl_ficon("arrow_left"); ?>
                                </a>
                                <a onclick="SMC_CPanel.PS.paginate(this);" data-dir="next" href="javascript:void(0);" class="pagination-ctrls next">
                                    <?php echo cl_ficon("arrow_right"); ?>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo cl_template('cpanel/assets/manage_ads/scripts/app_master_script'); ?>