<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo fetch_or_get($cl['page_title'], $cl['config']['name']); ?></title>
    
    <!-- Dynamic meta tags -->
    <meta name="description" content="<?php echo $cl['config']['description']; ?>" data-dynamic="true">
    <meta name="keywords" content="<?php echo $cl['config']['keywords']; ?>" data-dynamic="true">
    
    <!-- Styles -->
    <link href="<?php echo cl_static_file('css/bootstrap.min.css'); ?>" rel="stylesheet">
    <link href="<?php echo cl_static_file('css/spa.css'); ?>" rel="stylesheet">
    <link href="<?php echo cl_static_file('css/custom.style.css'); ?>" rel="stylesheet">
    
    <!-- Scripts -->
    <script src="<?php echo cl_static_file('js/jquery-3.6.0.min.js'); ?>"></script>
    <script src="<?php echo cl_static_file('js/bootstrap.bundle.min.js'); ?>"></script>
    <script src="<?php echo cl_static_file('js/spa.js'); ?>"></script>
    
    <!-- App initialization -->
    <script>
        "use strict";

        window.addEventListener('load', function() {
            window.SMColibri = Object.assign({}, {
                config: {
                    theme: "<?php echo $cl['config']['theme']; ?>",
                    theme_url: "<?php echo $cl['theme_url']; ?>",
                    site_url: "<?php echo $cl['config']['url']; ?>",
                    csrf_token: "<?php echo fetch_or_get($cl['csrf_token'], ''); ?>",
                    auth_token: "<?php echo fetch_or_get($cl['auth_token'], ''); ?>",
                    api_endpoint: "<?php echo cl_link('native_api/main'); ?>",
                    language: <?php echo json($cl['language']); ?>,
                }
            });
        });
    </script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom fixed-top">
        <div class="container">
            <a class="navbar-brand" href="<?php echo cl_link('/'); ?>">
                <img src="<?php echo $cl['config']['site_logo']; ?>" alt="Logo" height="30">
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <?php if (not_empty($cl['is_logged'])): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo cl_link('home'); ?>">
                                <i class="fas fa-home"></i> Home
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo cl_link('explore'); ?>">
                                <i class="fas fa-compass"></i> Explore
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo cl_link('notifications'); ?>">
                                <i class="fas fa-bell"></i> Notifications
                                <?php if ($cl['new_notifications']): ?>
                                    <span class="badge bg-danger"><?php echo $cl['new_notifications']; ?></span>
                                <?php endif; ?>
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">
                                <img src="<?php echo $cl['me']['avatar']; ?>" class="rounded-circle" width="24" height="24">
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li>
                                    <a class="dropdown-item" href="<?php echo cl_link($cl['me']['username']); ?>">
                                        Profile
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="<?php echo cl_link('settings'); ?>">
                                        Settings
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item" href="<?php echo cl_link('logout'); ?>" data-no-spa>
                                        Logout
                                    </a>
                                </li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo cl_link('login'); ?>">
                                Login
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo cl_link('signup'); ?>">
                                Sign up
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container py-5 mt-5">
        <?php echo $cl['page_content']; ?>
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">
                        &copy; <?php echo date('Y'); ?> <?php echo $cl['config']['name']; ?>. All rights reserved.
                    </p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="<?php echo cl_link('terms'); ?>" class="text-muted me-3">Terms</a>
                    <a href="<?php echo cl_link('privacy'); ?>" class="text-muted me-3">Privacy</a>
                    <a href="<?php echo cl_link('about'); ?>" class="text-muted">About</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Modals Container -->
    <div id="vue-modals-container"></div>

    <!-- Global variables for JavaScript -->
    <script>
        window.csrf_token = "<?php echo fetch_or_get($cl['csrf_token'], ''); ?>";
        window.site_url = "<?php echo $cl['config']['url']; ?>";
    </script>
</body>
</html> 